#!/usr/bin/env python3
"""
GPU Monitor Script - Monitor GPU utilization during video processing
"""

import subprocess
import time
import json
import os
from datetime import datetime

def get_gpu_stats():
    """Get current GPU statistics"""
    try:
        result = subprocess.run([
            'nvidia-smi', 
            '--query-gpu=utilization.gpu,utilization.memory,memory.used,memory.total,temperature.gpu,power.draw',
            '--format=csv,noheader,nounits'
        ], capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0:
            values = result.stdout.strip().split(', ')
            return {
                'gpu_util': int(values[0]),
                'mem_util': int(values[1]),
                'mem_used': int(values[2]),
                'mem_total': int(values[3]),
                'temperature': int(values[4]),
                'power_draw': float(values[5])
            }
    except Exception as e:
        print(f"Error getting GPU stats: {e}")
    
    return None

def get_cpu_stats():
    """Get current CPU statistics"""
    try:
        import psutil
        return {
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_percent': psutil.virtual_memory().percent
        }
    except Exception as e:
        print(f"Error getting CPU stats: {e}")
        return {'cpu_percent': 0, 'memory_percent': 0}

def monitor_gpu(duration=300, interval=2):
    """Monitor GPU for specified duration"""
    print(f"🔍 Monitoring GPU for {duration} seconds (interval: {interval}s)")
    print("=" * 80)
    print(f"{'Time':<12} {'GPU%':<6} {'MEM%':<6} {'VRAM':<12} {'TEMP':<6} {'POWER':<8} {'CPU%':<6}")
    print("=" * 80)
    
    start_time = time.time()
    max_gpu_util = 0
    max_mem_util = 0
    avg_gpu_util = 0
    avg_cpu_util = 0
    samples = 0
    
    try:
        while time.time() - start_time < duration:
            gpu_stats = get_gpu_stats()
            cpu_stats = get_cpu_stats()
            
            if gpu_stats and cpu_stats:
                current_time = datetime.now().strftime("%H:%M:%S")
                vram_usage = f"{gpu_stats['mem_used']}/{gpu_stats['mem_total']}MB"
                
                print(f"{current_time:<12} "
                      f"{gpu_stats['gpu_util']:<6}% "
                      f"{gpu_stats['mem_util']:<6}% "
                      f"{vram_usage:<12} "
                      f"{gpu_stats['temperature']:<6}°C "
                      f"{gpu_stats['power_draw']:<8.1f}W "
                      f"{cpu_stats['cpu_percent']:<6.1f}%")
                
                # Track statistics
                max_gpu_util = max(max_gpu_util, gpu_stats['gpu_util'])
                max_mem_util = max(max_mem_util, gpu_stats['mem_util'])
                avg_gpu_util += gpu_stats['gpu_util']
                avg_cpu_util += cpu_stats['cpu_percent']
                samples += 1
            
            time.sleep(interval)
            
    except KeyboardInterrupt:
        print("\n⏹️ Monitoring stopped by user")
    
    # Print summary
    if samples > 0:
        print("\n" + "=" * 80)
        print("📊 MONITORING SUMMARY:")
        print(f"  Duration: {time.time() - start_time:.1f} seconds")
        print(f"  Samples: {samples}")
        print(f"  Max GPU Utilization: {max_gpu_util}%")
        print(f"  Max Memory Utilization: {max_mem_util}%")
        print(f"  Average GPU Utilization: {avg_gpu_util/samples:.1f}%")
        print(f"  Average CPU Utilization: {avg_cpu_util/samples:.1f}%")
        
        # Analysis
        print("\n💡 ANALYSIS:")
        if max_gpu_util < 20:
            print("  ⚠️ Low GPU utilization - GPU may not be used effectively")
        elif max_gpu_util < 50:
            print("  ⚠️ Moderate GPU utilization - room for improvement")
        else:
            print("  ✅ Good GPU utilization")
        
        if avg_cpu_util/samples > 80:
            print("  ⚠️ High CPU usage - may indicate CPU fallback")
        else:
            print("  ✅ Normal CPU usage")

def check_ffmpeg_processes():
    """Check for running FFmpeg processes"""
    try:
        result = subprocess.run([
            'tasklist', '/FI', 'IMAGENAME eq ffmpeg.exe', '/FO', 'CSV'
        ], capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            if len(lines) > 1:  # Header + processes
                print(f"🎬 Found {len(lines)-1} FFmpeg processes running")
                return True
            else:
                print("🎬 No FFmpeg processes found")
                return False
    except Exception as e:
        print(f"Error checking FFmpeg processes: {e}")
        return False

def main():
    """Main monitoring function"""
    print("🚀 GPU NVIDIA RTX 3070Ti Monitor")
    print("=" * 50)
    
    # Check initial GPU status
    gpu_stats = get_gpu_stats()
    if gpu_stats:
        print(f"📊 Initial GPU Status:")
        print(f"  GPU Utilization: {gpu_stats['gpu_util']}%")
        print(f"  Memory Utilization: {gpu_stats['mem_util']}%")
        print(f"  VRAM Usage: {gpu_stats['mem_used']}/{gpu_stats['mem_total']} MB")
        print(f"  Temperature: {gpu_stats['temperature']}°C")
        print(f"  Power Draw: {gpu_stats['power_draw']}W")
    else:
        print("❌ Could not get GPU status")
        return
    
    # Check for FFmpeg processes
    check_ffmpeg_processes()
    
    print("\n" + "=" * 50)
    print("Starting monitoring...")
    print("Press Ctrl+C to stop")
    print("=" * 50)
    
    # Start monitoring
    monitor_gpu(duration=600, interval=2)  # Monitor for 10 minutes

if __name__ == "__main__":
    main()
