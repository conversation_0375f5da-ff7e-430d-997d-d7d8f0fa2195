"""
<PERSON><PERSON><PERSON> hàm tiện ích cho giao diện người dùng
"""
import sys
import time
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Any

class ProgressBar:
    """Hiển thị thanh tiến trình chuyên nghiệp trong terminal"""

    def __init__(self, total: int, prefix: str = '', suffix: str = '',
                decimals: int = 1, length: int = 50, fill: str = '█',
                print_end: str = '\r'):
        """
        Khởi tạo thanh tiến trình

        Args:
            total: Tổng số phần tử cần xử lý
            prefix: Chuỗi tiền tố
            suffix: Chuỗi hậu tố
            decimals: <PERSON><PERSON> chữ số thập phân cho phần trăm
            length: Độ dài của thanh tiến trình
            fill: Ký tự để điền vào thanh tiến trình
            print_end: <PERSON><PERSON> tự kết thúc khi in
        """
        self.total = total
        self.prefix = prefix
        self.suffix = suffix
        self.decimals = decimals
        self.length = length
        self.fill = fill
        self.print_end = print_end
        self.start_time = time.time()
        self.iteration = 0
        self.last_update_time = 0

    def update(self, iteration: Optional[int] = None, suffix: Optional[str] = None) -> None:
        """
        Cập nhật thanh tiến trình - phiên bản đơn giản không hiển thị phần trăm và thời gian

        Args:
            iteration: Số phần tử đã xử lý (nếu None, tăng iteration hiện tại lên 1)
            suffix: Chuỗi hậu tố mới (nếu None, giữ nguyên suffix hiện tại)
        """
        current_time = time.time()

        # Giới hạn tần suất cập nhật để tránh nhấp nháy (tối thiểu 0.1 giây giữa các lần cập nhật)
        if current_time - self.last_update_time < 0.1 and iteration != self.total:
            return

        self.last_update_time = current_time

        if iteration is not None:
            self.iteration = iteration
        else:
            self.iteration += 1

        if suffix is not None:
            self.suffix = suffix

        # In thông báo đơn giản
        print(f'\r{self.prefix} {self.suffix}', end=self.print_end)

        # In dòng mới khi hoàn thành
        if self.iteration == self.total:
            print(f"\n✅ Hoàn thành xử lý")

    def _format_time(self, seconds: float) -> str:
        """Định dạng thời gian từ giây sang chuỗi dễ đọc"""
        if seconds < 60:
            return f"{seconds:.1f}s"
        elif seconds < 3600:
            minutes = int(seconds // 60)
            seconds = int(seconds % 60)
            return f"{minutes}m {seconds}s"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            return f"{hours}h {minutes}m"

class DownloadStatus:
    """Hiển thị trạng thái tải xuống với giao diện chuyên nghiệp"""

    def __init__(self, url: str, video_title: str):
        self.url = url
        self.video_title = video_title
        self.start_time = time.time()
        self.downloaded_bytes = 0
        self.total_bytes = 0
        self.speed = 0  # bytes/second
        self.eta = 0    # seconds
        self.status = "Đang chuẩn bị..."
        self.progress = 0.0  # 0.0 to 1.0
        self.last_update_time = 0
        self.update_interval = 0.5  # Giới hạn cập nhật mỗi 0.5 giây

    def update(self, status: Dict[str, Any]) -> None:
        """Cập nhật trạng thái tải xuống từ hook của yt-dlp"""
        self.status = status.get('status', 'unknown')

        if self.status == 'downloading':
            self.downloaded_bytes = status.get('downloaded_bytes', 0)
            self.total_bytes = status.get('total_bytes') or status.get('total_bytes_estimate', 0)
            self.speed = status.get('speed', 0)
            self.eta = status.get('eta', 0)

            if self.total_bytes > 0:
                self.progress = self.downloaded_bytes / self.total_bytes

            # Giới hạn tần suất cập nhật để tránh nhấp nháy
            current_time = time.time()
            if current_time - self.last_update_time >= self.update_interval:
                self.last_update_time = current_time
                self._print_status()

        elif self.status == 'finished':
            elapsed = time.time() - self.start_time
            self.progress = 1.0

            # Tính toán tốc độ trung bình
            avg_speed = self.downloaded_bytes / elapsed if elapsed > 0 else 0

            print(f"\n✅ TẢI XUỐNG HOÀN THÀNH")
            print(f"   📁 File: {self.video_title}")
            print(f"   📊 Kích thước: {self._format_size(self.downloaded_bytes)}")
            print(f"   ⏱️ Thời gian: {self._format_time(elapsed)}")
            print(f"   🚀 Tốc độ TB: {self._format_speed(avg_speed)}")

        elif self.status == 'error':
            print(f"\n❌ LỖI KHI TẢI XUỐNG: {self.video_title}")

    def _print_status(self) -> None:
        """In trạng thái tải xuống hiện tại - phiên bản đơn giản"""
        # Hiển thị tiêu đề video ngắn gọn
        short_title = self.video_title[:40] + "..." if len(self.video_title) > 40 else self.video_title

        # In trạng thái đơn giản
        print(f"\r⬇️ ĐANG TẢI: {short_title}", end='')

    def _format_size(self, bytes: int) -> str:
        """Định dạng kích thước từ byte sang chuỗi dễ đọc"""
        if bytes < 1024:
            return f"{bytes}B"
        elif bytes < 1024 * 1024:
            return f"{bytes/1024:.1f}KB"
        elif bytes < 1024 * 1024 * 1024:
            return f"{bytes/(1024*1024):.1f}MB"
        else:
            return f"{bytes/(1024*1024*1024):.2f}GB"

    def _format_speed(self, bytes_per_second: float) -> str:
        """Định dạng tốc độ từ byte/giây sang chuỗi dễ đọc"""
        if bytes_per_second < 1024:
            return f"{bytes_per_second:.1f}B/s"
        elif bytes_per_second < 1024 * 1024:
            return f"{bytes_per_second/1024:.1f}KB/s"
        elif bytes_per_second < 1024 * 1024 * 1024:
            return f"{bytes_per_second/(1024*1024):.1f}MB/s"
        else:
            return f"{bytes_per_second/(1024*1024*1024):.2f}GB/s"

    def _format_time(self, seconds: float) -> str:
        """Định dạng thời gian từ giây sang chuỗi dễ đọc"""
        if seconds < 60:
            return f"{seconds:.1f}s"
        elif seconds < 3600:
            minutes = int(seconds // 60)
            seconds = int(seconds % 60)
            return f"{minutes}m {seconds}s"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            return f"{hours}h {minutes}m"

class ProcessingSummary:
    """Hiển thị tóm tắt quá trình xử lý"""

    def __init__(self):
        self.start_time = time.time()
        self.total_videos = 0
        self.processed_videos = 0
        self.failed_videos = 0
        self.skipped_videos = 0
        self.total_download_size = 0  # bytes
        self.total_processed_size = 0  # bytes
        self.video_details = []

    def add_video(self, url: str, title: str, status: str,
                 download_size: int = 0, processed_size: int = 0,
                 error: Optional[str] = None) -> None:
        """Thêm thông tin về một video đã xử lý"""
        self.total_videos += 1

        if status == 'processed':
            self.processed_videos += 1
            self.total_download_size += download_size
            self.total_processed_size += processed_size
        elif status == 'failed':
            self.failed_videos += 1
        elif status == 'skipped':
            self.skipped_videos += 1

        self.video_details.append({
            'url': url,
            'title': title,
            'status': status,
            'download_size': download_size,
            'processed_size': processed_size,
            'error': error
        })

    def print_summary(self) -> None:
        """In tóm tắt quá trình xử lý - phiên bản tối ưu"""
        elapsed_time = time.time() - self.start_time

        print("\n" + "=" * 60)
        print(f"📊 TÓM TẮT QUÁ TRÌNH XỬ LÝ")
        print("=" * 60)

        # Hiển thị thông tin tổng quan ngắn gọn
        print(f"\n⏱️ Thời gian xử lý: {self._format_time(elapsed_time)}")
        print(f"📈 Tổng số video: {self.total_videos} (✅ {self.processed_videos} thành công | ❌ {self.failed_videos} thất bại | ⏭️ {self.skipped_videos} bỏ qua)")

        # Chỉ hiển thị thông tin dung lượng nếu có video đã xử lý
        if self.processed_videos > 0:
            # Tính toán dung lượng đã tiết kiệm
            saved_size = self.total_download_size - self.total_processed_size
            saved_percent = (saved_size / self.total_download_size) * 100 if self.total_download_size > 0 else 0

            print(f"💾 Dung lượng: {self._format_size(self.total_download_size)} → {self._format_size(self.total_processed_size)} (Tiết kiệm: {saved_percent:.1f}%)")

        # Chỉ hiển thị danh sách video thất bại nếu có lỗi và số lượng ít
        if self.failed_videos > 0 and self.failed_videos <= 5:
            print("\n❌ Video thất bại:")
            count = 0
            for video in self.video_details:
                if video['status'] == 'failed':
                    error_msg = f" - {video['error']}" if video['error'] else ""
                    print(f"  - {video['title']}{error_msg}")
                    count += 1
                    if count >= 5:  # Giới hạn hiển thị tối đa 5 video thất bại
                        if self.failed_videos > 5:
                            print(f"  ... và {self.failed_videos - 5} video khác")
                        break

        print("\n" + "=" * 60)

    def _format_size(self, bytes: int) -> str:
        """Định dạng kích thước từ byte sang chuỗi dễ đọc"""
        if bytes < 1024:
            return f"{bytes}B"
        elif bytes < 1024 * 1024:
            return f"{bytes/1024:.1f}KB"
        elif bytes < 1024 * 1024 * 1024:
            return f"{bytes/(1024*1024):.1f}MB"
        else:
            return f"{bytes/(1024*1024*1024):.2f}GB"

    def _format_time(self, seconds: float) -> str:
        """Định dạng thời gian từ giây sang chuỗi dễ đọc"""
        if seconds < 60:
            return f"{seconds:.1f} giây"
        elif seconds < 3600:
            minutes = int(seconds // 60)
            seconds = int(seconds % 60)
            return f"{minutes} phút {seconds} giây"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            seconds = int(seconds % 60)
            return f"{hours} giờ {minutes} phút {seconds} giây"

def print_banner() -> None:
    """In banner ứng dụng - phiên bản chuyên nghiệp"""
    banner = """
╔══════════════════════════════════════════════════════════════════╗
║                                                                  ║
║   █▄█ █▀█ █░█ ▀█▀ █░█ █▄▄ █▀▀   █▀█ █▀█ █▀█ █▀▀ █▀▀ █▀ █▀ █▀█  ║
║   ░█░ █▄█ █▄█ ░█░ █▄█ █▄█ ██▄   █▀▀ █▀▄ █▄█ █▄▄ ██▄ ▄█ ▄█ █▄█  ║
║                                                                  ║
║                      PRO EDITION v2.1.0                          ║
║                                                                  ║
╚══════════════════════════════════════════════════════════════════╝
"""
    # Hiển thị thời gian hiện tại
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(banner)
    print(f"⏱️  Thời gian bắt đầu: {current_time}")
    print(f"🖥️  Hệ điều hành: {sys.platform.upper()}")
    print(f"🔄  Python: {sys.version.split()[0]}")

def print_section(title: str) -> None:
    """In tiêu đề phần với định dạng đẹp - phiên bản chuyên nghiệp"""
    width = 70
    print("\n" + "━" * width)
    print(f"  📌  {title.upper()}")
    print("━" * width)
