"""
Advanced error handling and fallback mechanisms for video processing.
"""

import logging
import time
import psutil
from typing import Dict, Any, Optional, Callable
from enum import Enum

logger = logging.getLogger("video_processor.error_handler")


class ErrorType(Enum):
    """Types of errors that can occur during processing."""
    NETWORK_ERROR = "network"
    CODEC_ERROR = "codec"
    GPU_ERROR = "gpu"
    MEMORY_ERROR = "memory"
    DISK_ERROR = "disk"
    FORMAT_ERROR = "format"
    TIMEOUT_ERROR = "timeout"
    UNKNOWN_ERROR = "unknown"


class ErrorSeverity(Enum):
    """Severity levels for errors."""
    LOW = 1      # Can retry with same method
    MEDIUM = 2   # Should try alternative method
    HIGH = 3     # Should skip and continue
    CRITICAL = 4 # Should abort processing


class SmartErrorHandler:
    """Smart error handler with adaptive retry strategies."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.error_history = {}
        self.retry_strategies = {
            ErrorType.NETWORK_ERROR: self._handle_network_error,
            ErrorType.CODEC_ERROR: self._handle_codec_error,
            ErrorType.GPU_ERROR: self._handle_gpu_error,
            ErrorType.MEMORY_ERROR: self._handle_memory_error,
            ErrorType.DISK_ERROR: self._handle_disk_error,
            ErrorType.FORMAT_ERROR: self._handle_format_error,
            ErrorType.TIMEOUT_ERROR: self._handle_timeout_error,
        }
    
    def classify_error(self, error: Exception, context: str = "") -> tuple[ErrorType, ErrorSeverity]:
        """Classify error type and severity."""
        error_str = str(error).lower()
        
        # Network errors
        if any(keyword in error_str for keyword in ['network', 'connection', 'timeout', 'dns', 'http']):
            severity = ErrorSeverity.MEDIUM if 'timeout' in error_str else ErrorSeverity.LOW
            return ErrorType.NETWORK_ERROR, severity
        
        # Codec errors
        if any(keyword in error_str for keyword in ['codec', 'vp9', 'av1', 'decode', 'encode']):
            severity = ErrorSeverity.HIGH if any(c in error_str for c in ['vp9', 'av1']) else ErrorSeverity.MEDIUM
            return ErrorType.CODEC_ERROR, severity
        
        # GPU errors
        if any(keyword in error_str for keyword in ['gpu', 'nvenc', 'cuda', 'nvidia', 'vram']):
            return ErrorType.GPU_ERROR, ErrorSeverity.MEDIUM
        
        # Memory errors
        if any(keyword in error_str for keyword in ['memory', 'ram', 'out of memory', 'allocation']):
            return ErrorType.MEMORY_ERROR, ErrorSeverity.HIGH
        
        # Disk errors
        if any(keyword in error_str for keyword in ['disk', 'space', 'permission', 'file']):
            return ErrorType.DISK_ERROR, ErrorSeverity.MEDIUM
        
        # Format errors
        if any(keyword in error_str for keyword in ['format', 'container', 'muxer']):
            return ErrorType.FORMAT_ERROR, ErrorSeverity.LOW
        
        return ErrorType.UNKNOWN_ERROR, ErrorSeverity.LOW
    
    def should_retry(self, error_type: ErrorType, attempt: int, max_attempts: int) -> bool:
        """Determine if we should retry based on error type and attempt count."""
        if attempt >= max_attempts:
            return False
        
        # Different retry limits for different error types
        retry_limits = {
            ErrorType.NETWORK_ERROR: 3,
            ErrorType.CODEC_ERROR: 1,  # Don't retry codec errors
            ErrorType.GPU_ERROR: 2,
            ErrorType.MEMORY_ERROR: 1,  # Don't retry memory errors
            ErrorType.DISK_ERROR: 2,
            ErrorType.FORMAT_ERROR: 3,
            ErrorType.TIMEOUT_ERROR: 2,
            ErrorType.UNKNOWN_ERROR: 2
        }
        
        return attempt < retry_limits.get(error_type, 2)
    
    def get_retry_delay(self, error_type: ErrorType, attempt: int) -> float:
        """Get adaptive retry delay based on error type and attempt."""
        base_delays = {
            ErrorType.NETWORK_ERROR: 2.0,
            ErrorType.CODEC_ERROR: 1.0,
            ErrorType.GPU_ERROR: 3.0,
            ErrorType.MEMORY_ERROR: 5.0,
            ErrorType.DISK_ERROR: 2.0,
            ErrorType.FORMAT_ERROR: 1.0,
            ErrorType.TIMEOUT_ERROR: 5.0,
            ErrorType.UNKNOWN_ERROR: 2.0
        }
        
        base_delay = base_delays.get(error_type, 2.0)
        # Exponential backoff with jitter
        return base_delay * (2 ** attempt) + (attempt * 0.5)
    
    def _handle_network_error(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle network-related errors."""
        return {
            "action": "retry",
            "modifications": {
                "reduce_concurrent_fragments": True,
                "increase_timeout": True,
                "use_different_client": True
            }
        }
    
    def _handle_codec_error(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle codec-related errors."""
        return {
            "action": "fallback",
            "modifications": {
                "force_cpu_encoding": True,
                "avoid_problematic_codec": True,
                "use_safe_format": True
            }
        }
    
    def _handle_gpu_error(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle GPU-related errors."""
        return {
            "action": "fallback",
            "modifications": {
                "disable_gpu": True,
                "reduce_memory_usage": True,
                "use_cpu_encoding": True
            }
        }
    
    def _handle_memory_error(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle memory-related errors."""
        return {
            "action": "modify",
            "modifications": {
                "reduce_parallel_processes": True,
                "reduce_buffer_size": True,
                "enable_memory_cleanup": True
            }
        }
    
    def _handle_disk_error(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle disk-related errors."""
        return {
            "action": "retry",
            "modifications": {
                "cleanup_temp_files": True,
                "check_disk_space": True,
                "use_different_temp_dir": True
            }
        }
    
    def _handle_format_error(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle format-related errors."""
        return {
            "action": "retry",
            "modifications": {
                "try_different_format": True,
                "use_safe_container": True,
                "adjust_codec_params": True
            }
        }
    
    def _handle_timeout_error(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle timeout-related errors."""
        return {
            "action": "retry",
            "modifications": {
                "increase_timeout": True,
                "reduce_complexity": True,
                "use_simpler_filters": True
            }
        }
    
    def get_recovery_strategy(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """Get recovery strategy for the given error."""
        error_type, severity = self.classify_error(error, context.get("operation", ""))
        
        if error_type in self.retry_strategies:
            return self.retry_strategies[error_type](error, context)
        
        return {
            "action": "retry",
            "modifications": {}
        }


def check_system_resources() -> Dict[str, Any]:
    """Check current system resource usage."""
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return {
            "cpu_usage": cpu_percent,
            "memory_usage": memory.percent,
            "memory_available": memory.available,
            "disk_usage": disk.percent,
            "disk_free": disk.free
        }
    except Exception as e:
        logger.warning(f"Failed to check system resources: {e}")
        return {}


def adaptive_resource_management(config: Dict[str, Any]) -> Dict[str, Any]:
    """Adaptively adjust processing parameters based on system resources."""
    resources = check_system_resources()
    adjustments = {}
    
    # Adjust based on CPU usage
    if resources.get("cpu_usage", 0) > 80:
        adjustments["reduce_parallel_processes"] = True
        adjustments["lower_cpu_priority"] = True
    
    # Adjust based on memory usage
    if resources.get("memory_usage", 0) > 85:
        adjustments["reduce_buffer_size"] = True
        adjustments["enable_memory_cleanup"] = True
        adjustments["reduce_concurrent_operations"] = True
    
    # Adjust based on disk usage
    if resources.get("disk_usage", 0) > 90:
        adjustments["cleanup_temp_files"] = True
        adjustments["reduce_temp_storage"] = True
    
    return adjustments
