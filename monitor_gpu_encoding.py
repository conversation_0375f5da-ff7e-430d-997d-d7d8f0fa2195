#!/usr/bin/env python3
"""
Monitor GPU encoding units specifically during video processing
"""
import subprocess
import time
import threading
import sys
from datetime import datetime

def get_gpu_encoding_stats():
    """Get GPU encoding unit statistics"""
    try:
        # Monitor encoding units specifically
        result = subprocess.run([
            'nvidia-smi', 'dmon', '-s', 'pucvmet', '-c', '1'
        ], capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            if len(lines) >= 2:  # Header + data
                data_line = lines[-1]  # Last line with data
                values = data_line.split()
                if len(values) >= 7:
                    return {
                        'gpu_util': int(values[1]),
                        'mem_util': int(values[2]),
                        'enc_util': int(values[3]),  # Encoding utilization
                        'dec_util': int(values[4]),  # Decoding utilization
                        'memory_used': int(values[5]),
                        'temperature': int(values[6])
                    }
    except Exception as e:
        pass
    
    # Fallback to regular nvidia-smi
    try:
        result = subprocess.run([
            'nvidia-smi', 
            '--query-gpu=utilization.gpu,utilization.memory,memory.used,temperature.gpu,encoder.stats.sessionCount,encoder.stats.averageFps',
            '--format=csv,noheader,nounits'
        ], capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0:
            values = result.stdout.strip().split(', ')
            return {
                'gpu_util': int(values[0]),
                'mem_util': int(values[1]),
                'memory_used': int(values[2]),
                'temperature': int(values[3]),
                'enc_sessions': values[4] if len(values) > 4 else 'N/A',
                'enc_fps': values[5] if len(values) > 5 else 'N/A'
            }
    except Exception as e:
        print(f"Error getting GPU encoding stats: {e}")
    
    return None

def monitor_encoding(duration=120, interval=2):
    """Monitor GPU encoding for specified duration"""
    print(f"🎮 Monitoring GPU Encoding for {duration} seconds")
    print("=" * 80)
    print(f"{'Time':<12} {'GPU%':<6} {'MEM%':<6} {'ENC%':<6} {'DEC%':<6} {'VRAM':<8} {'TEMP':<6} {'Status':<15}")
    print("=" * 80)
    
    start_time = time.time()
    max_gpu_util = 0
    max_enc_util = 0
    encoding_active_time = 0
    samples = 0
    
    try:
        while time.time() - start_time < duration:
            stats = get_gpu_encoding_stats()
            
            if stats:
                current_time = datetime.now().strftime("%H:%M:%S")
                
                # Determine status based on encoding activity
                status = ""
                if stats.get('enc_util', 0) > 0:
                    status = "🎬 ENCODING"
                    encoding_active_time += interval
                elif stats.get('dec_util', 0) > 0:
                    status = "📺 DECODING"
                elif stats['gpu_util'] > 30:
                    status = "🚀 GPU ACTIVE"
                elif stats['gpu_util'] > 10:
                    status = "⚡ GPU WORKING"
                else:
                    status = "💤 IDLE"
                
                print(f"{current_time:<12} "
                      f"{stats['gpu_util']:<6}% "
                      f"{stats['mem_util']:<6}% "
                      f"{stats.get('enc_util', 0):<6}% "
                      f"{stats.get('dec_util', 0):<6}% "
                      f"{stats['memory_used']:<8}MB "
                      f"{stats['temperature']:<6}°C "
                      f"{status:<15}")
                
                # Track statistics
                max_gpu_util = max(max_gpu_util, stats['gpu_util'])
                max_enc_util = max(max_enc_util, stats.get('enc_util', 0))
                samples += 1
            
            time.sleep(interval)
            
    except KeyboardInterrupt:
        print("\n⏹️ Monitoring stopped by user")
    
    # Print summary
    if samples > 0:
        print("=" * 80)
        print("📊 ENCODING MONITORING SUMMARY:")
        print(f"  Duration: {time.time() - start_time:.1f} seconds")
        print(f"  Max GPU Utilization: {max_gpu_util}%")
        print(f"  Max Encoding Utilization: {max_enc_util}%")
        print(f"  Encoding Active Time: {encoding_active_time:.1f}s ({encoding_active_time/(time.time()-start_time)*100:.1f}%)")
        
        if max_enc_util > 0:
            print("✅ GPU encoding was detected - NVENC is working!")
        elif max_gpu_util > 30:
            print("⚠️ GPU was active but encoding not detected - may be using compute")
        else:
            print("❌ No significant GPU activity - likely using CPU")

def run_simple_test():
    """Run a simple encoding test"""
    print("🧪 Running simple GPU encoding test...")
    try:
        result = subprocess.run([
            'python', 'run.py', 
            '--url', 'https://www.youtube.com/watch?v=jNQXAC9IVRw',
            '--title', 'GPU Encoding Test'
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ Test video processing completed")
        else:
            print(f"❌ Test failed: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("⏰ Test timed out")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🎮 GPU Encoding Monitor")
    print("=" * 50)
    
    # Check if nvidia-smi dmon is available
    try:
        result = subprocess.run(['nvidia-smi', 'dmon', '-h'], capture_output=True, timeout=5)
        if result.returncode == 0:
            print("✅ nvidia-smi dmon available - will monitor encoding units")
        else:
            print("⚠️ nvidia-smi dmon not available - using basic monitoring")
    except:
        print("⚠️ nvidia-smi dmon not available - using basic monitoring")
    
    # Start test in background
    test_thread = threading.Thread(target=run_simple_test)
    test_thread.daemon = True
    test_thread.start()
    
    # Wait for test to start
    time.sleep(3)
    
    # Monitor encoding
    monitor_encoding(duration=60, interval=2)
    
    # Wait for test to complete
    test_thread.join(timeout=10)
    
    print("\n🏁 Encoding monitoring complete!")
