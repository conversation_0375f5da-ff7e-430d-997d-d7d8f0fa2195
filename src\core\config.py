"""
Configuration management for the video processor application.
"""

import os
import json
import logging
from typing import Dict, Any, Optional

from src.core.exceptions import ConfigError

logger = logging.getLogger("video_processor.config")

# Default configuration
DEFAULT_CONFIG = {
    "download": {
        "format": "bestvideo[height=1080][vcodec!*=av01]+bestaudio/bestvideo[height=1080][ext=mp4]+bestaudio[ext=m4a]/bestvideo[height=1080]+bestaudio/best[height=1080]/best",
        "max_retries": 10,
        "concurrent_fragments": 32,
        "buffer_size": "32M",
        "use_aria2c": True,
        "cookies_file": "cookies.txt",
        "cookies_max_age_days": 7
    },
    "processing": {
        "add_title": True,
        "add_channel_name": True,
        "title_font_size": 70,
        "title_font": "Impact",
        "channel_font": "Impact",
        "max_title_width": 864,
        "title_position": "center",
        "channel_position": "bottom",
        "title_color": "white",
        "title_background": False,
        "title_background_color": "black",
        "max_segments": 40,
        "min_segment_duration": 90,
        "max_segment_duration": 150,
        "output_format": "mp4",
        "max_bitrate": 10000,
        "max_fps": 30,
        "max_parallel_processes": 2
    },
    "paths": {
        "output_dir": "data/output",
        "cache_dir": "data/cache",
        "log_file": "data/video_processor.log",
        "fonts_dir": "resources/fonts"
    }
}


class Config:
    """Configuration manager for the application."""

    def __init__(self, config_file: Optional[str] = None):
        """
        Initialize the configuration manager.

        Args:
            config_file: Path to the configuration file. If None, uses default config.
        """
        self.config_file = config_file
        self.config = DEFAULT_CONFIG.copy()

        if config_file and os.path.exists(config_file):
            self._load_config()
            logger.info(f"Loaded configuration from {config_file}")
        else:
            logger.info("Using default configuration")

    def _load_config(self) -> None:
        """Load configuration from file."""
        try:
            # Always read the file fresh to ensure we get the latest changes
            with open(self.config_file, "r", encoding="utf-8") as f:
                user_config = json.load(f)

            # Reset to default config before updating
            self.config = DEFAULT_CONFIG.copy()

            # Update default config with user config
            self._update_config(self.config, user_config)
        except (json.JSONDecodeError, IOError) as e:
            logger.warning(f"Failed to load configuration: {e}")
            raise ConfigError(f"Failed to load configuration: {e}")

    def _update_config(self, target: Dict[str, Any], source: Dict[str, Any]) -> None:
        """
        Recursively update target dictionary with values from source.

        Args:
            target: Target dictionary to update.
            source: Source dictionary with new values.
        """
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._update_config(target[key], value)
            else:
                target[key] = value

    def save(self, config_file: Optional[str] = None) -> None:
        """
        Save configuration to file.

        Args:
            config_file: Path to save the configuration. If None, uses the current config_file.
        """
        save_path = config_file or self.config_file
        if not save_path:
            logger.warning("No configuration file specified for saving")
            return

        try:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            with open(save_path, "w", encoding="utf-8") as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            logger.info(f"Saved configuration to {save_path}")
        except IOError as e:
            logger.warning(f"Failed to save configuration: {e}")
            raise ConfigError(f"Failed to save configuration: {e}")

    def get(self, section: str, key: str, default: Any = None) -> Any:
        """
        Get a configuration value.

        Args:
            section: Configuration section.
            key: Configuration key.
            default: Default value if not found.

        Returns:
            Configuration value or default.
        """
        # Reload config if file exists to ensure we get the latest changes
        if self.config_file and os.path.exists(self.config_file):
            self._load_config()

        try:
            return self.config[section][key]
        except KeyError:
            logger.debug(f"Configuration value not found: {section}.{key}, using default: {default}")
            return default

    def set(self, section: str, key: str, value: Any) -> None:
        """
        Set a configuration value.

        Args:
            section: Configuration section.
            key: Configuration key.
            value: Value to set.
        """
        # Reload config if file exists to ensure we get the latest changes
        if self.config_file and os.path.exists(self.config_file):
            self._load_config()

        if section not in self.config:
            self.config[section] = {}

        self.config[section][key] = value
        logger.debug(f"Set configuration value: {section}.{key} = {value}")

        # Save changes to file if available
        if self.config_file:
            self.save()

    def get_path(self, path_key: str) -> str:
        """
        Get an absolute path from the configuration.

        Args:
            path_key: Key in the paths section.

        Returns:
            Absolute path.
        """
        # Reload config if file exists to ensure we get the latest changes
        if self.config_file and os.path.exists(self.config_file):
            self._load_config()

        path = self.get("paths", path_key, "")
        if not path:
            logger.warning(f"Path not found in configuration: {path_key}")
            return ""

        # If path is relative, make it absolute
        if not os.path.isabs(path):
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            path = os.path.join(base_dir, path)

        return path
