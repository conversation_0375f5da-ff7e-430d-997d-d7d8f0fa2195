"""
Module tải video từ YouTube using yt-dlp with advanced configuration
"""
import os
import json
import logging
import tempfile
import subprocess
from typing import Optional, Dict, List
import yt_dlp
import re
import glob
import time
import traceback
import random  # Thêm import random

# Import hàm kiểm tra cookies
from src.processors.youtube_downloader_cookies_check import check_cookies_freshness

# Cấu hình tối ưu cho yt-dlp với GPU NVIDIA RTX 3070Ti
YTDLP_OPTIONS = {
    # Định dạng video - Ưu tiên H.264/H.265 cho GPU NVIDIA RTX 3070Ti
    # Tr<PERSON>h hoàn toàn VP9/VP8/AV1 để tối ưu GPU processing
    'format': (
        # 1080p H.264 (avc1) với AAC audio - ưu tiên cao nhất
        'bestvideo[height=1080][vcodec^=avc1][ext=mp4]+bestaudio[acodec=aac][ext=m4a][abr>=128][abr<=192]/'
        'bestvideo[height=1080][vcodec^=h264][ext=mp4]+bestaudio[acodec=aac][ext=m4a][abr>=128][abr<=192]/'

        # 1080p H.265 (hvc1) với AAC audio - ưu tiên thứ hai
        'bestvideo[height=1080][vcodec^=hvc1][ext=mp4]+bestaudio[acodec=aac][ext=m4a][abr>=128][abr<=192]/'
        'bestvideo[height=1080][vcodec^=h265][ext=mp4]+bestaudio[acodec=aac][ext=m4a][abr>=128][abr<=192]/'

        # YouTube format IDs cho H.264 + AAC đảm bảo
        '137+140/'  # 137=1080p H.264, 140=128k AAC

        # 720p H.264/H.265 fallback với AAC audio
        'bestvideo[height=720][vcodec^=avc1][ext=mp4]+bestaudio[acodec=aac][ext=m4a][abr>=128][abr<=192]/'
        'bestvideo[height=720][vcodec^=h264][ext=mp4]+bestaudio[acodec=aac][ext=m4a][abr>=128][abr<=192]/'

        # Loại trừ nghiêm ngặt các codec có vấn đề
        'bestvideo[height=1080][ext=mp4][vcodec!*=vp9][vcodec!*=vp8][vcodec!*=av01][vcodec!*=av1]+bestaudio[acodec=aac][ext=m4a]/'
        'bestvideo[height=720][ext=mp4][vcodec!*=vp9][vcodec!*=vp8][vcodec!*=av01][vcodec!*=av1]+bestaudio[acodec=aac][ext=m4a]/'

        # Fallback cuối cùng - format tương thích
        'best[height>=720][ext=mp4][vcodec!*=vp9][vcodec!*=vp8][vcodec!*=av01][vcodec!*=av1]'
    ),
    'format_sort': ['res:1080', 'vcodec:h264', 'acodec:aac', 'fps:30', 'size', 'br'],
    'video_format_sort': ['res:1080', 'vcodec:h264', 'fps:30', 'br', 'size'],
    'audio_format_sort': ['acodec:aac', 'br:128', 'asr:44100'],

    # Cấu hình đầu ra
    'outtmpl': '%(id)s_temp.%(ext)s',
    'merge_output_format': 'mp4',
    'writeinfojson': True,  # Lưu thông tin video dưới dạng JSON
    'clean_infojson': False,  # Giữ lại thông tin đầy đủ

    # Cấu hình xử lý sau tải xuống - ưu tiên AAC audio
    'postprocessor_args': {
        'ffmpeg': ['-c:v', 'copy', '-c:a', 'aac', '-b:a', '128k', '-ar', '44100', '-ac', '2']
    },
    'postprocessors': [{
        'key': 'FFmpegVideoConvertor',
        'preferedformat': 'mp4',
    }],

    # Tải phụ đề - Đã vô hiệu hóa để tăng tốc độ xử lý
    'writesubtitles': False,  # Không lưu phụ đề
    'writeautomaticsub': False,  # Không lưu phụ đề tự động
    'allsubtitles': False,  # Không lưu tất cả phụ đề
    'subtitleslangs': [],  # Không có ngôn ngữ phụ đề
    'embedsubtitles': False,  # Không nhúng phụ đề

    # Sử dụng client web chuẩn để tránh giới hạn của client Android
    # Thêm nhiều tham số hơn để đảm bảo có thể tải video 1080p
    'extractor_args': {
        'youtube': {
            'player_client': ['web', 'tv_embedded', 'ios', 'android'],
            'player_skip': ['webpage', 'configs'],
            'player_client_name': 'WEB',
            'po_token': 'web.gvs+QmVhckBCYWNrQEJlYXJAQmFja0BAZWFAQB',  # PO token đặc biệt để truy cập các định dạng cao hơn
            'formats': 'missing_pot',  # Hiển thị cả các định dạng thiếu PO token
            'innertube_client': 'web',  # Sử dụng client web để có thể truy cập các định dạng cao hơn
            'innertube_key': 'AIzaSyAO_FJ2SlqU8Q4STEHLGCilw_Y9_11qcW8',  # API key cho client web
            'innertube_context': '{"client":{"clientName":"WEB","clientVersion":"2.20230331.00.00"}}',  # Context cho client web
            'max_formats': 100  # Tăng số lượng định dạng tối đa để đảm bảo tìm thấy 1080p
        }
    },

    # Sử dụng User-Agent đặc biệt để "đánh lừa" YouTube
    'http_headers': {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'DNT': '1',
        'Cache-Control': 'no-cache',
        'Referer': 'https://www.youtube.com/',  # Thêm Referer để tăng tính xác thực
        'Origin': 'https://www.youtube.com',  # Thêm Origin để tăng tính xác thực
        'Sec-Fetch-Dest': 'document',  # Thêm các header Sec-Fetch để tăng tính xác thực
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1'
    },

    # Tối ưu hóa tải xuống với aria2c
    'external_downloader': 'aria2c',
    'external_downloader_args': {
        'aria2c': [
            '--min-split-size=1M',  # Chia nhỏ file để tải song song
            '--max-connection-per-server=32',  # Tăng lên 32 kết nối mỗi server
            '--max-concurrent-downloads=32',  # Tăng lên 32 tải xuống đồng thời
            '--split=32',  # Chia file thành 32 phần
            '--optimize-concurrent-downloads=true',  # Tự động tối ưu hóa
            '--file-allocation=none',  # Không cấp phát trước file
            '--continue=true',  # Tiếp tục tải nếu bị gián đoạn
            '--retry-wait=2',  # Thời gian chờ giữa các lần thử lại
            '--max-tries=10',  # Số lần thử lại tối đa
            '--summary-interval=0',  # Không hiển thị tóm tắt
            '--console-log-level=warn',  # Chỉ hiển thị cảnh báo
            '--disk-cache=64M',  # Tăng bộ nhớ đệm đĩa
            '--async-dns=true',  # Sử dụng DNS bất đồng bộ
            '--timeout=30',  # Thời gian chờ kết nối
            '--connect-timeout=10',  # Thời gian chờ kết nối
            '--auto-file-renaming=false',  # Không tự động đổi tên file
            '--allow-overwrite=true',  # Cho phép ghi đè file
            '--always-resume=true',  # Luôn tiếp tục tải nếu có thể
            '--max-resume-failure-tries=10',  # Số lần thử lại khi không thể tiếp tục tải
            '--http-accept-gzip=true',  # Chấp nhận nén gzip
            '--reuse-uri=true'  # Tái sử dụng URI
        ]
    },

    # Tối ưu hóa tải xuống
    'concurrent_fragment_downloads': 32,  # Tăng số lượng fragment tải song song lên 32
    'buffersize': 1024*1024*64,  # Tăng buffer size lên 64MB
    'throttledratelimit': 0,  # Không giới hạn tốc độ tải xuống
    'hls_prefer_native': True,  # Hỗ trợ xử lý HLS (m3u8)
    'downloader_options': {  # Thêm tùy chọn cho downloader
        'http': {
            'timeout': 60,  # Tăng timeout lên 60s
            'retries': 10  # Số lần thử lại
        }
    },

    # Thử lại khi gặp lỗi
    'retries': 10,  # Số lần thử lại
    'fragment_retries': 10,  # Số lần thử lại cho mỗi fragment
    'file_access_retries': 10,  # Số lần thử lại khi truy cập file
    'extractor_retries': 10,  # Số lần thử lại khi trích xuất thông tin
    'retry_sleep_functions': {'fragment': lambda n: 5 * (2 ** (n-1))},  # Exponential backoff
    'socket_timeout': 60,  # Timeout 60s

    # Tùy chọn vượt qua giới hạn
    'geo_bypass': True,  # Bypass geo-restrictions
    'geo_bypass_country': 'US',  # Sử dụng IP US
    'age_limit': 21,  # Bypass age restrictions
    'prefer_insecure': True,  # Ưu tiên kết nối không an toàn nếu cần thiết
    'force_generic_extractor': False,  # Không sử dụng extractor chung

    # Tùy chọn gỡ lỗi và hiển thị - Vô hiệu hóa tất cả thông tin hiển thị
    'verbose': False,  # Không hiển thị thông tin chi tiết
    'quiet': True,  # Im lặng
    'no_warnings': True,  # Không hiển thị cảnh báo
    'no_color': True,  # Không sử dụng màu
    'progress': False,  # Không hiển thị thanh tiến trình
    'logger': None,  # Không sử dụng logger mặc định
    'debug_printtraffic': False,  # Không in thông tin traffic
    'noprogress': True,  # Không hiển thị tiến trình
    'consoletitle': False,  # Không hiển thị tiêu đề console
    'nopart': True,  # Không tạo file .part

    # Tùy chọn khác
    'mark_watched': False,  # Không đánh dấu video đã xem
    'ignoreerrors': True,  # Bỏ qua lỗi
    'extract_flat': False,  # Không trích xuất thông tin phẳng
    'allow_unplayable_formats': True,  # Cho phép các định dạng không thể phát
    'check_formats': False,  # Không kiểm tra các định dạng
    'source_address': '0.0.0.0',  # Địa chỉ nguồn
    'force_ipv4': True,  # Ép buộc sử dụng IPv4
    'sleep_interval': 1,  # Thời gian chờ giữa các yêu cầu
    'max_sleep_interval': 5  # Thời gian chờ tối đa giữa các yêu cầu
}

class YouTubeDownloader:
    def __init__(self, url: str, output_dir: str, cookies_file: Optional[str] = None, auto_create_cookies: bool = True):
        self.url = url
        self.output_dir = output_dir
        self.cookies_file = cookies_file
        self.temp_dir = tempfile.mkdtemp()
        self.video_title = None
        self.is_shorts = False  # Biến mới để đánh dấu video là Shorts
        self.video_info = None  # Lưu thông tin video

        # Tự động tạo cookies từ trình duyệt nếu không có cookies_file
        if auto_create_cookies and (not cookies_file or not os.path.exists(cookies_file)):
            print(f"⚠️ Không tìm thấy file cookies: {cookies_file}")
            print(f"🔑 Đang tự động tạo cookies từ trình duyệt...")
            self.cookies_file = self._create_cookies_from_browser()
            if self.cookies_file:
                print(f"✅ Đã tạo cookies thành công: {self.cookies_file}")
            else:
                print(f"⚠️ Không thể tạo cookies từ trình duyệt. Có thể gặp lỗi xác thực.")

        # Thư mục cache cho video đã tải
        self.cache_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "cache")
        os.makedirs(self.cache_dir, exist_ok=True)

        # Tạo file cache index nếu chưa tồn tại
        self.cache_index_file = os.path.join(self.cache_dir, "cache_index.json")
        if not os.path.exists(self.cache_index_file):
            with open(self.cache_index_file, "w", encoding="utf-8") as f:
                json.dump({}, f)

        # Kiểm tra nếu URL là YouTube Shorts
        if '/shorts/' in url:
            self.is_shorts = True
            logging.info(f"📱 Phát hiện YouTube Shorts video: {url}")
            print(f"📱 Phát hiện YouTube Shorts video, sẽ giữ nguyên tỷ lệ 9:16")

        # Đảm bảo output_dir tồn tại
        os.makedirs(output_dir, exist_ok=True)

        # Tìm file videos.txt
        self._find_videos_txt()

        # Khởi tạo đường dẫn các file txt
        # Thay vì lấy base_dir, lấy videos_txt_dir (nếu tìm thấy)
        if hasattr(self, 'videos_txt_path') and self.videos_txt_path:
            videos_txt_dir = os.path.dirname(self.videos_txt_path)
            self.completed_urls_file = os.path.join(videos_txt_dir, "completed_urls.txt")
            self.failed_urls_file = os.path.join(videos_txt_dir, "failed_urls.txt")
            self.videos_file = self.videos_txt_path
        else:
            # Fallback: nếu không tìm thấy videos.txt, sử dụng thư mục cha của output_dir
            base_dir = os.path.dirname(output_dir)
            self.completed_urls_file = os.path.join(base_dir, "completed_urls.txt")
            self.failed_urls_file = os.path.join(base_dir, "failed_urls.txt")
            self.videos_file = os.path.join(base_dir, "videos.txt")

        # Khởi tạo các file txt nếu chưa tồn tại
        self._init_txt_files()

        self.original_video_path = None
        self.channel_info = None  # Lưu thông tin kênh

    def _find_videos_txt(self):
        """Tìm file videos.txt chứa URL đang xử lý"""
        try:
            # Tìm tất cả các file videos.txt trong hệ thống
            videos_txt_files = glob.glob(os.path.join(os.path.dirname(self.output_dir), "**", "videos.txt"), recursive=True)

            # Lọc để tìm file chứa URL hiện tại
            for txt_file in videos_txt_files:
                if os.path.exists(txt_file):
                    with open(txt_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if self.url in content:
                            self.videos_txt_path = txt_file
                            logging.info(f"🔍 Đã tìm thấy file videos.txt chứa URL: {txt_file}")
                            return
            # Nếu không tìm thấy, ghi log
            logging.warning(f"⚠️ Không tìm thấy file videos.txt chứa URL: {self.url}")
            self.videos_txt_path = None
        except Exception as e:
            logging.error(f"❌ Lỗi khi tìm file videos.txt: {str(e)}")
            self.videos_txt_path = None

    def _init_txt_files(self):
        """Khởi tạo các file txt cần thiết"""
        # Đảm bảo thư mục tồn tại
        os.makedirs(os.path.dirname(self.completed_urls_file), exist_ok=True)

        # Tạo file completed_urls.txt nếu chưa tồn tại
        if not os.path.exists(self.completed_urls_file):
            with open(self.completed_urls_file, 'w', encoding='utf-8') as f:
                pass
            logging.debug(f"Đã tạo file mới: {self.completed_urls_file}")

        # Tạo file failed_urls.txt nếu chưa tồn tại
        if not os.path.exists(self.failed_urls_file):
            with open(self.failed_urls_file, 'w', encoding='utf-8') as f:
                pass
            logging.debug(f"Đã tạo file mới: {self.failed_urls_file}")

        logging.debug("Đã khởi tạo các file txt cần thiết")

    def _remove_from_videos_txt(self, url: str) -> None:
        """Xóa URL khỏi file videos.txt"""
        if not os.path.exists(self.videos_file):
            logging.debug(f"File không tồn tại: {self.videos_file}")
            return
        try:
            # Đọc tất cả các dòng
            with open(self.videos_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # Lọc ra các dòng không chứa URL cần xóa
            filtered_lines = [line for line in lines if url not in line]

            # Ghi lại file
            with open(self.videos_file, 'w', encoding='utf-8') as f:
                f.writelines(filtered_lines)

            # Nếu có thay đổi, ghi log debug
            if len(lines) != len(filtered_lines):
                logging.debug(f"Đã xóa URL khỏi videos.txt: {url}")

        except Exception as e:
            logging.error(f"Lỗi khi xóa URL khỏi videos.txt: {str(e)}")

    def _get_current_time(self):
        """Get current time formatted for log entries"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def _add_to_failed(self, url: str, reason: str, remove_from_videos: bool = True) -> None:
        """Thêm URL vào danh sách thất bại nếu tải xuống hoặc xử lý thất bại"""
        try:
            # Thêm vào file failed_urls.txt
            timestamp = self._get_current_time()
            with open(self.failed_urls_file, 'a', encoding='utf-8') as f:
                f.write(f"{url} {timestamp} - {reason}\n")
            logging.debug(f"Đã thêm URL vào danh sách thất bại: {url}")

            if remove_from_videos:
                self._remove_from_videos_txt(url)
        except Exception as e:
            logging.error(f"Lỗi khi thêm URL vào danh sách thất bại: {str(e)}")

    def _add_to_completed(self, url: str) -> None:
        """Thêm URL vào danh sách đã hoàn thành và xóa khỏi videos.txt"""
        # Thêm vào file completed.txt
        try:
            timestamp = self._get_current_time()
            with open(self.completed_urls_file, 'a', encoding='utf-8') as f:
                f.write(f"{url} {timestamp}\n")
            logging.debug(f"Đã thêm {url} vào danh sách hoàn thành")
            print(f"✅ Đã thêm {url} vào danh sách hoàn thành")
        except Exception as e:
            logging.error(f"Lỗi khi thêm vào completed.txt: {str(e)}")

        # Xóa khỏi videos.txt - Đảm bảo luôn xóa URL
        self._remove_from_videos_txt(url)
        logging.info(f"🗑️ Đã xóa URL khỏi videos.txt: {url}")
        print(f"🗑️ Đã xóa URL khỏi videos.txt: {url}")

    def _extract_video_id(self, url: str) -> Optional[str]:
        """Trích xuất ID video từ URL YouTube"""
        try:
            if 'youtube.com/watch?v=' in url:
                video_id = url.split('watch?v=')[-1].split('&')[0]
            elif 'youtu.be/' in url:
                video_id = url.split('youtu.be/')[-1].split('?')[0]
            elif 'youtube.com/shorts/' in url:
                video_id = url.split('shorts/')[-1].split('?')[0]
            else:
                video_id = None
            return video_id
        except Exception as e:
            logging.error(f"❌ Lỗi khi trích xuất video ID: {str(e)}")
            return None

    def _get_video_id(self, url: str) -> Optional[str]:
        """Lấy ID video từ URL YouTube (giữ lại để tương thích ngược)"""
        return self._extract_video_id(url)

    def _find_subtitle_file(self, _: str) -> Optional[str]:
        """Tìm file phụ đề của video - Đã vô hiệu hóa để tăng tốc độ xử lý"""
        # Đã vô hiệu hóa tính năng tìm kiếm và sử dụng phụ đề để tăng tốc độ xử lý
        logging.info("Tính năng phụ đề đã bị vô hiệu hóa để tăng tốc độ xử lý")
        return None

    def _check_cache(self, url: str) -> Optional[str]:
        """Kiểm tra xem video đã được cache chưa"""
        try:
            video_id = self._get_video_id(url)
            if not video_id:
                return None

            # Đọc cache index
            if os.path.exists(self.cache_index_file):
                with open(self.cache_index_file, 'r', encoding='utf-8') as f:
                    cache_index = json.load(f)

                # Kiểm tra xem video_id có trong cache không
                if video_id in cache_index:
                    cache_path = cache_index[video_id]['path']
                    if os.path.exists(cache_path):
                        logging.info(f"✅ Tìm thấy video trong cache: {cache_path}")
                        print(f"✅ Tìm thấy video trong cache: {os.path.basename(cache_path)}")
                        return cache_path
                    else:
                        # Xóa khỏi cache nếu file không tồn tại
                        del cache_index[video_id]
                        with open(self.cache_index_file, 'w', encoding='utf-8') as f:
                            json.dump(cache_index, f, indent=2)

            return None
        except Exception as e:
            logging.error(f"❌ Lỗi khi kiểm tra cache: {str(e)}")
            return None

    def _add_to_cache(self, url: str, file_path: str) -> None:
        """Thêm video vào cache"""
        try:
            video_id = self._get_video_id(url)
            if not video_id or not os.path.exists(file_path):
                return

            # Đọc cache index
            cache_index = {}
            if os.path.exists(self.cache_index_file):
                with open(self.cache_index_file, 'r', encoding='utf-8') as f:
                    cache_index = json.load(f)

            # Thêm vào cache index
            cache_index[video_id] = {
                'url': url,
                'path': file_path,
                'timestamp': time.time(),
                'title': self.video_title or os.path.basename(file_path)
            }

            # Lưu cache index
            with open(self.cache_index_file, 'w', encoding='utf-8') as f:
                json.dump(cache_index, f, indent=2)

            logging.info(f"✅ Đã thêm video vào cache: {file_path}")
        except Exception as e:
            logging.error(f"❌ Lỗi khi thêm vào cache: {str(e)}")

    def _is_completed(self, url: str) -> bool:
        """Kiểm tra xem URL đã được xử lý thành công chưa"""
        try:
            # Kiểm tra cache trước
            cached_path = self._check_cache(url)
            if cached_path:
                return True

            # Kiểm tra xem file completed_urls.txt có tồn tại không
            if not os.path.exists(self.completed_urls_file):
                logging.info(f"🔍 File completed_urls.txt không tồn tại: {self.completed_urls_file}")
                return False

            # Đọc danh sách URL đã hoàn thành
            with open(self.completed_urls_file, 'r', encoding='utf-8') as f:
                completed_urls = [line.strip() for line in f.readlines() if line.strip()]

            # Kiểm tra URL có trong danh sách đã hoàn thành (so sánh chính xác)
            is_completed = url in completed_urls

            if is_completed:
                logging.info(f"✅ URL đã tồn tại trong danh sách hoàn thành: {url}")
                print(f"✅ URL đã tồn tại trong danh sách hoàn thành: {url}")

                # Kiểm tra xem video đã được tải thực sự chưa
                try:
                    # Lấy ID video từ URL
                    video_id = self._get_video_id(url)

                    if video_id:
                        # Tìm các file video đã tải trong thư mục output
                        video_files = []
                        for root, _, files in os.walk(self.output_dir):
                            for file in files:
                                if file.endswith('.mp4') and video_id in file:
                                    video_files.append(os.path.join(root, file))

                        # Nếu không tìm thấy file video nào chứa video_id
                        if not video_files:
                            logging.warning(f"⚠️ URL nằm trong danh sách hoàn thành nhưng không tìm thấy file video: {url}")
                            print(f"⚠️ URL nằm trong danh sách hoàn thành nhưng không tìm thấy file video: {url}")

                            # Xóa URL khỏi completed_urls.txt
                            new_completed = [u for u in completed_urls if u != url]
                            with open(self.completed_urls_file, 'w', encoding='utf-8') as f:
                                f.write('\n'.join(new_completed))

                            logging.info(f"🗑️ Đã xóa URL khỏi danh sách hoàn thành (không tìm thấy file): {url}")
                            print(f"🗑️ Đã xóa URL khỏi danh sách hoàn thành (không tìm thấy file): {url}")
                            return False
                        else:
                            logging.info(f"✅ Tìm thấy file video đã xử lý: {', '.join([os.path.basename(f) for f in video_files])}")
                            # Thêm vào cache
                            for video_file in video_files:
                                self._add_to_cache(url, video_file)
                except Exception as e:
                    logging.error(f"❌ Lỗi khi kiểm tra file video: {str(e)}")

            return is_completed

        except Exception as e:
            logging.error(f"❌ Lỗi khi kiểm tra trạng thái hoàn thành: {str(e)}")
            return False

    def _cleanup_original_video(self):
        """Xóa file video gốc sau khi xử lý xong"""
        try:
            if self.original_video_path and os.path.exists(self.original_video_path):
                os.remove(self.original_video_path)
                print(f"🗑️ Đã xóa video gốc: {os.path.basename(self.original_video_path)}")
                logging.info(f"🗑️ Đã xóa video gốc: {os.path.basename(self.original_video_path)}")
                self.original_video_path = None
                # Thêm delay để đảm bảo quá trình xóa file hoàn tất
                time.sleep(1)
        except Exception as e:
            logging.error(f"Lỗi khi xóa video gốc: {str(e)}")
            print(f"⚠️ Lỗi khi xóa video gốc: {str(e)}")

    def _count_processed_videos(self) -> int:
        """Count number of processed videos in output directory"""
        try:
            count = 0
            for _, _, files in os.walk(self.output_dir):
                count += sum(1 for f in files if f.endswith('.mp4'))
            return count
        except Exception as e:
            logging.error(f"Error counting videos: {str(e)}")
            return 0

    def _is_member_only(self, error_msg: str) -> bool:
        """Check if video is member-only content"""
        return "available to this channel's members" in error_msg

    def _get_channel_tags(self, channel_id: str = None, channel_name: str = None) -> List[str]:
        """Get fixed hashtags based on channel"""
        # Định nghĩa các chủ đề cố định cho từng kênh đã biết
        known_channels = {
            # Scuba/Diving channels
            'DALLMYD': ['scuba', 'diving', 'treasure'],
            'AdamCScuba': ['scuba', 'diving', 'underwater'],
            'ScubaDiving': ['scuba', 'diving', 'ocean'],

            # Primitive/Survival channels
            'Primitive Survival Tool': ['primitive', 'survival', 'building'],
            'Primitive Technology': ['primitive', 'technology', 'building'],
            'Primitive Skills': ['primitive', 'survival', 'jungle'],
            'Jungle Survival': ['jungle', 'survival', 'primitive'],
            'Wild Survival': ['wild', 'survival', 'nature'],
            'Mr Heang Update': ['building', 'construction', 'swimming'],
            'Wilderness Survival': ['wilderness', 'survival', 'nature'],

            # Tech channels
            'LinusTechTips': ['tech', 'review', 'hardware'],
            'Linus Tech Tips': ['tech', 'review', 'hardware'],
            'TechLinked': ['tech', 'news', 'hardware'],
            'ShortCircuit': ['tech', 'unboxing', 'review'],

            # Gaming channels
            'IGN': ['gaming', 'review', 'news'],
            'GameSpot': ['gaming', 'review', 'news']
        }

        # Định nghĩa các từ khóa theo chủ đề
        topic_keywords = {
            'tech': {
                'name': ['tech', 'technology', 'hardware', 'software', 'computer', 'pc', 'laptop'],
                'tags': ['tech', 'review', 'hardware'],
                'indicators': ['review', 'unboxing', 'testing', 'benchmark', 'comparison', 'versus', 'tech', 'technology']
            },
            'gaming': {
                'name': ['game', 'gaming', 'play', 'playthrough', 'walkthrough'],
                'tags': ['gaming', 'gameplay', 'review'],
                'indicators': ['gameplay', 'playthrough', 'walkthrough', 'game', 'gaming', 'xbox', 'playstation', 'nintendo']
            },
            'music': {
                'name': ['music', 'song', 'concert', 'band', 'singer', 'rap'],
                'tags': ['music', 'song', 'artist'],
                'indicators': ['music', 'song', 'concert', 'singer', 'band', 'album', 'track', 'instrumental']
            },
            'cooking': {
                'name': ['cook', 'cooking', 'food', 'recipe', 'kitchen'],
                'tags': ['cooking', 'food', 'recipe'],
                'indicators': ['recipe', 'cooking', 'food', 'baking', 'kitchen', 'chef', 'homemade', 'meal']
            },
            'education': {
                'name': ['education', 'learn', 'tutorial', 'howto', 'course'],
                'tags': ['education', 'tutorial', 'learning'],
                'indicators': ['learn', 'tutorial', 'how to', 'course', 'lesson', 'education', 'school', 'university']
            },
            'sports': {
                'name': ['sport', 'sports', 'football', 'basketball', 'soccer'],
                'tags': ['sports', 'athlete', 'fitness'],
                'indicators': ['match', 'game', 'sports', 'playing', 'tournament', 'championship', 'team', 'player']
            },
            'news': {
                'name': ['news', 'report', 'update', 'daily', 'weekly'],
                'tags': ['news', 'update', 'report'],
                'indicators': ['news', 'report', 'breaking', 'update', 'headlines', 'reporting', 'coverage']
            },
            'art': {
                'name': ['art', 'draw', 'paint', 'design', 'creative'],
                'tags': ['art', 'creative', 'design'],
                'indicators': ['drawing', 'painting', 'art', 'creative', 'design', 'artist', 'illustration']
            },
            'beauty': {
                'name': ['beauty', 'makeup', 'cosmetic', 'fashion', 'style'],
                'tags': ['beauty', 'fashion', 'style'],
                'indicators': ['makeup', 'beauty', 'skincare', 'fashion', 'style', 'cosmetic', 'hairstyle']
            },
            'science': {
                'name': ['science', 'physics', 'chemistry', 'biology', 'experiment'],
                'tags': ['science', 'research', 'education'],
                'indicators': ['experiment', 'science', 'laboratory', 'research', 'discovery', 'physics', 'chemistry']
            },
            'travel': {
                'name': ['travel', 'journey', 'trip', 'tour', 'vlog'],
                'tags': ['travel', 'vlog', 'adventure'],
                'indicators': ['travel', 'journey', 'trip', 'vacation', 'tourism', 'traveling', 'destination']
            },
            'automotive': {
                'name': ['car', 'auto', 'vehicle', 'motor', 'drive'],
                'tags': ['auto', 'car', 'review'],
                'indicators': ['car', 'vehicle', 'auto', 'driving', 'testdrive', 'supercar', 'engine', 'horsepower']
            },
            'photography': {
                'name': ['photo', 'camera', 'photography', 'photographer'],
                'tags': ['photo', 'camera', 'tutorial'],
                'indicators': ['camera', 'photo', 'photography', 'lens', 'photoshoot', 'photographer', 'picture']
            },
            'comedy': {
                'name': ['comedy', 'funny', 'humor', 'laugh', 'joke'],
                'tags': ['comedy', 'funny', 'entertainment'],
                'indicators': ['funny', 'comedy', 'joke', 'humor', 'stand-up', 'comedian', 'prank']
            },
            'primitive': {
                'name': ['primitive', 'survival', 'jungle', 'wild', 'nature', 'build', 'building'],
                'tags': ['primitive', 'survival', 'building'],
                'indicators': ['primitive', 'survival', 'jungle', 'build', 'building', 'construction', 'handmade']
            },
            'diy': {
                'name': ['diy', 'build', 'homemade', 'handmade', 'craft', 'project'],
                'tags': ['diy', 'build', 'handmade'],
                'indicators': ['diy', 'do it yourself', 'build', 'craft', 'homemade', 'handmade', 'project', 'making']
            },
            'swimming': {
                'name': ['swim', 'swimming', 'pool', 'water', 'slide', 'park', 'waterpark'],
                'tags': ['swimming', 'pool', 'water'],
                'indicators': ['swimming', 'pool', 'water', 'slide', 'waterpark', 'aquatic', 'underwater']
            }
        }

        # Kiểm tra kênh đã biết trước
        for known_channel, tags in known_channels.items():
            if (channel_id and known_channel.lower() in channel_id.lower()) or \
               (channel_name and known_channel.lower() in channel_name.lower()):
                return tags

        # Nếu không tìm thấy trong danh sách đã biết, phân tích tên kênh
        if channel_name:
            channel_name = channel_name.lower()

            # Tìm chủ đề phù hợp dựa trên tên kênh
            for topic, data in topic_keywords.items():
                for keyword in data['name']:
                    if keyword in channel_name:
                        return data['tags']

            try:
                # Lấy thêm thông tin kênh nếu cần
                if channel_id:
                    ydl_opts = {
                        'quiet': True,
                        'extract_flat': True,
                        'force_generic_extractor': True
                    }

                    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                        channel_info = ydl.extract_info(
                            f"https://www.youtube.com/channel/{channel_id}",
                            download=False
                        )

                        if channel_info:
                            # Phân tích mô tả kênh
                            description = channel_info.get('description', '').lower()
                            for topic, data in topic_keywords.items():
                                for keyword in data['name']:
                                    if keyword in description:
                                        return data['tags']

                            # Phân tích video gần đây
                            entries = channel_info.get('entries', [])
                            video_titles = ' '.join([e.get('title', '').lower() for e in entries[:10]])

                            # Đếm số lần xuất hiện của từ khóa trong tiêu đề video
                            topic_counts = {}
                            for topic, data in topic_keywords.items():
                                count = sum(1 for keyword in data['name'] if keyword in video_titles)
                                if count > 0:
                                    topic_counts[topic] = count

                            # Chọn chủ đề xuất hiện nhiều nhất
                            if topic_counts:
                                best_topic = max(topic_counts.items(), key=lambda x: x[1])[0]
                                return topic_keywords[best_topic]['tags']

            except Exception as e:
                logging.warning(f"Error analyzing channel: {str(e)}")

        # Trả về tags mặc định nếu không xác định được chủ đề
        return ['content', 'video', 'youtube']

    def _check_video_limit(self) -> bool:
        """Kiểm tra số lượng video đã tải"""
        # Kiểm tra thư mục output có tồn tại không
        if not os.path.exists(self.output_dir):
            logging.info(f"📁 Thư mục output chưa tồn tại, sẽ được tạo: {self.output_dir}")
            return True

        # Đếm số file MP4 trong thư mục output
        mp4_files = [f for f in os.listdir(self.output_dir) if f.endswith('.mp4')]
        current_count = len(mp4_files)

        logging.info(f"✅ Thư mục hiện có {current_count}/50 video")

        # Nếu đã đạt giới hạn 50 video
        if current_count >= 50:
            logging.warning("⚠️ Đã đạt giới hạn số video tối đa (50 video)")
            # Thêm thông báo rõ ràng hơn
            print("⚠️ Đã đạt giới hạn 50 video, chuyển sang kênh tiếp theo")
            # Thêm URL vào danh sách thất bại với lý do rõ ràng
            self._add_to_failed(self.url, "Video limit reached (50 videos)", False)
            return False

        return True

    def _is_video_processed(self, url: str) -> bool:
        """Kiểm tra xem video đã được xử lý chưa"""
        try:
            # Lấy video ID từ URL
            video_id = self._extract_video_id(url)
            if not video_id:
                return False

            # Kiểm tra trong thư mục output
            output_files = os.listdir(self.output_dir)
            for file in output_files:
                if file.endswith('.mp4') and video_id in file:
                    return True

            return False

        except Exception as e:
            logging.error(f"❌ Lỗi khi kiểm tra video đã xử lý: {str(e)}")
            return False

    def _extract_video_id(self, url: str) -> Optional[str]:
        """Trích xuất video ID từ URL YouTube"""
        try:
            # Kiểm tra URL hợp lệ
            if not url or not isinstance(url, str):
                return None

            # Tách video ID từ URL
            if 'youtube.com/watch?v=' in url:
                return url.split('watch?v=')[1].split('&')[0]
            elif 'youtu.be/' in url:
                return url.split('youtu.be/')[1].split('?')[0]
            else:
                return None

        except Exception as e:
            logging.error(f"❌ Lỗi khi trích xuất video ID: {str(e)}")
            return None

    def download(self) -> Optional[str]:
        """Tải video từ YouTube và trả về đường dẫn file đã tải"""
        try:
            # Kiểm tra giới hạn video trước tiên để tránh tải không cần thiết
            if not self._check_video_limit():
                # Thêm URL vào danh sách thất bại với lý do rõ ràng
                self._add_to_failed(self.url, "Video limit reached (50 videos)", False)
                return None

            # Kiểm tra cache trước
            cached_path = self._check_cache(self.url)
            if cached_path and os.path.exists(cached_path):
                logging.info(f"✅ Sử dụng video từ cache: {cached_path}")
                print(f"💾 Sử dụng video từ cache: {os.path.basename(cached_path)}")
                self.original_video_path = cached_path

                # Lấy thông tin video từ cache
                video_id = self._get_video_id(self.url)
                if video_id:
                    with open(self.cache_index_file, 'r', encoding='utf-8') as f:
                        cache_index = json.load(f)
                    if video_id in cache_index:
                        self.video_title = cache_index[video_id].get('title', os.path.basename(cached_path))

                return cached_path

            # Tạo options cho yt-dlp
            options = YTDLP_OPTIONS.copy()

            # Thêm cookies nếu có
            if self.cookies_file and os.path.exists(self.cookies_file):
                options['cookiefile'] = self.cookies_file
                # Kiểm tra tính mới của cookies
                try:
                    # Kiểm tra thời gian tạo file cookies
                    cookies_age = time.time() - os.path.getmtime(self.cookies_file)
                    cookies_age_days = cookies_age / (60 * 60 * 24)

                    if cookies_age_days > 7:
                        print(f"⚠️ File cookies đã cũ ({cookies_age_days:.1f} ngày). Nên cập nhật cookies mới.")

                    print(f"🔑 Sử dụng cookies từ file: {self.cookies_file}")
                except Exception as e:
                    logging.warning(f"⚠️ Lỗi khi kiểm tra cookies: {str(e)}")
                logging.info(f"✅ Sử dụng cookies từ file: {self.cookies_file}")

            # Thêm tham số xác thực
            options['mark_watched'] = True  # Đánh dấu video đã xem
            options['allow_oauth'] = True   # Cho phép xác thực OAuth
            options['allow_password'] = True  # Cho phép xác thực bằng mật khẩu

            # Sử dụng cấu hình tối ưu để tải video 1080p
            # Sử dụng format linh hoạt để lấy độ phân giải cao nhất có sẵn
            # Ưu tiên chính xác 1080p, sau đó mới đến các độ phân giải thấp hơn
            options['format'] = 'bestvideo[height=1080]+bestaudio/137+140/bestvideo[height<=1080]+bestaudio/best[height<=1080]/best'
            options['merge_output_format'] = 'mp4'  # Đảm bảo đầu ra là MP4

            # Thêm các tham số mới để đảm bảo tải được video 1080p
            options['format_sort'] = ['res:1080', 'fps', 'size', 'br']
            options['video_format_sort'] = ['res:1080', 'fps', 'br', 'size']
            options['audio_format_sort'] = ['br', 'asr']

            # Ẩn hoàn toàn các log debug
            options['quiet'] = True
            options['no_warnings'] = True
            options['no_color'] = True
            options['progress'] = False
            options['logger'] = None
            options['debug_printtraffic'] = False
            options['verbose'] = False
            options['print_traffic'] = False
            options['no_progress'] = True
            options['consoletitle'] = False
            options['logtostderr'] = False

            # Chuyển hướng output vào file log
            log_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "log.txt")
            options['writeinfojson'] = False  # Không ghi file info json

            # Sử dụng aria2c để tải nhanh hơn với các tham số tối ưu
            options['external_downloader'] = 'aria2c'
            options['external_downloader_args'] = {
                'aria2c': [
                    '--min-split-size=1M',  # Chia nhỏ file để tải song song
                    '--max-connection-per-server=32',  # Tăng lên 32 kết nối mỗi server
                    '--max-concurrent-downloads=32',  # Tăng lên 32 tải xuống đồng thời
                    '--split=32',  # Chia file thành 32 phần
                    '--optimize-concurrent-downloads=true',  # Tự động tối ưu hóa
                    '--file-allocation=none',  # Không cấp phát trước file
                    '--continue=true',  # Tiếp tục tải nếu bị gián đoạn
                    '--retry-wait=2',  # Thời gian chờ giữa các lần thử lại
                    '--max-tries=10',  # Số lần thử lại tối đa
                    '--allow-overwrite=true',  # Cho phép ghi đè file
                    '--always-resume=true',  # Luôn tiếp tục tải nếu có thể
                    '--http-accept-gzip=true'  # Chấp nhận nén gzip
                ]
            }

            # Thêm tham số formats=missing_pot và các tham số khác để có thể truy cập các định dạng 1080p
            options['extractor_args'] = {
                'youtube': {
                    'player_client': ['web', 'tv_embedded', 'ios', 'android'],
                    'player_skip': ['webpage', 'configs'],
                    'player_client_name': 'WEB',
                    'po_token': 'web.gvs+QmVhckBCYWNrQEJlYXJAQmFja0BAZWFAQB',  # PO token đặc biệt
                    'formats': 'missing_pot',  # Hiển thị cả các định dạng thiếu PO token
                    'innertube_client': 'web',  # Sử dụng client web
                    'innertube_key': 'AIzaSyAO_FJ2SlqU8Q4STEHLGCilw_Y9_11qcW8',  # API key
                    'max_formats': 100  # Tăng số lượng định dạng tối đa
                }
            }

            # Thêm các tùy chọn cho ffmpeg để ghép video và audio
            options['postprocessor_args'] = {
                'ffmpeg': ['-c:v', 'copy', '-c:a', 'aac', '-b:a', '192k']
            }

            # Thêm các header HTTP để tăng tính xác thực
            options['http_headers'] = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Referer': 'https://www.youtube.com/',
                'Origin': 'https://www.youtube.com',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'same-origin'
            }

            # Thêm các tùy chọn để xử lý tốt hơn với cookies
            if self.cookies_file and os.path.exists(self.cookies_file):
                # Kiểm tra tính mới của cookies
                check_cookies_freshness(self.cookies_file)

                print(f"🔑 Sử dụng cookies từ file: {self.cookies_file}")
                options['cookiefile'] = self.cookies_file

            # Xóa tùy chọn lấy cookies từ trình duyệt để tránh lỗi
            if 'cookiesfrombrowser' in options:
                del options['cookiesfrombrowser']

            # Các tùy chọn khác
            options['mark_watched'] = False  # Không đánh dấu video đã xem
            options['verbose'] = True  # Hiển thị thông tin chi tiết hơn

            # Khởi tạo yt-dlp
            ydl = yt_dlp.YoutubeDL(options)

            # Lấy thông tin video
            try:
                self.video_info = ydl.extract_info(self.url, download=False, process=True)
                if not self.video_info:
                    raise Exception("Không thể lấy thông tin video")

                video_id = self.video_info.get('id')
                if not video_id:
                    raise Exception("Không thể lấy ID video")

                # Lấy và hiển thị thông tin video
                self.video_title = self.video_info.get('title', 'Unknown Title')
                channel_name = self.video_info.get('channel', 'Unknown Channel')
                resolution = self.video_info.get('height', '?')

                print(f"📝 Tiêu đề video: {self.video_title}")
                print(f"🎬 Kênh: {channel_name}")
                print(f"🔍 Độ phân giải hiện có: {resolution}p")

                # Kiểm tra các phiên bản có sẵn của video
                formats = self.video_info.get('formats', [])
                available_formats = []

                # Lọc các định dạng video có độ phân giải
                for fmt in formats:
                    if fmt.get('height') and fmt.get('width'):
                        format_info = {
                            'height': fmt.get('height'),
                            'width': fmt.get('width'),
                            'ext': fmt.get('ext', 'unknown'),
                            'format_id': fmt.get('format_id', ''),
                            'format_note': fmt.get('format_note', ''),
                            'vcodec': fmt.get('vcodec', 'unknown'),
                            'acodec': fmt.get('acodec', 'unknown'),
                            'filesize': fmt.get('filesize', 0)
                        }
                        available_formats.append(format_info)

                # Sắp xếp theo độ phân giải giảm dần
                available_formats.sort(key=lambda x: (x['height'], x['width']), reverse=True)

                # Lấy danh sách các độ phân giải duy nhất
                available_heights = []
                for fmt in available_formats:
                    if fmt['height'] not in available_heights:
                        available_heights.append(fmt['height'])

                if available_formats:
                    # Hiển thị thông tin về các định dạng có sẵn
                    print(f"🔍 Các độ phân giải có sẵn: {', '.join([f'{h}p' for h in available_heights])}")

                    # Hiển thị thông tin chi tiết hơn về các định dạng cao nhất
                    best_format = available_formats[0]
                    print(f"🔍 Định dạng tốt nhất có sẵn: {best_format['width']}x{best_format['height']} ({best_format['format_note']})")

                    # Hiển thị thông báo về độ phân giải
                    if 1080 not in available_heights:
                        print(f"⚠️ Video không có sẵn ở độ phân giải 1080p. Sẽ tải độ phân giải cao nhất có sẵn: {best_format['height']}p")
                    else:
                        print(f"✅ Video có sẵn ở độ phân giải 1080p. Sẽ tải ở độ phân giải cao nhất.")

                    # Hiển thị thông tin về các định dạng cao nhất
                    print("\n🔍 Các định dạng video tốt nhất có sẵn:")
                    for i, fmt in enumerate(available_formats[:3], 1):
                        filesize_mb = fmt['filesize'] / 1024 / 1024 if fmt['filesize'] else 0
                        print(f"  {i}. {fmt['width']}x{fmt['height']} - {fmt['format_note']} ({fmt['ext']}) - {filesize_mb:.1f}MB")

                # Cập nhật cấu hình tải xuống để lấy định dạng tốt nhất
                if available_formats:
                    # Tìm định dạng video có độ phân giải cao nhất
                    best_format = available_formats[0]
                    best_format_id = best_format.get('format_id')

                    # Kiểm tra xem có định dạng 1080p không
                    has_1080p = any(fmt['height'] == 1080 for fmt in available_formats)

                    # Nếu có định dạng 1080p, sử dụng định dạng đó
                    if has_1080p:
                        # Tìm định dạng 1080p tốt nhất
                        format_1080p = next((fmt for fmt in available_formats if fmt['height'] == 1080), None)
                        if format_1080p:
                            best_format_id = format_1080p.get('format_id')
                            best_format = format_1080p

                    # Tìm định dạng audio tốt nhất
                    audio_formats = [fmt for fmt in self.video_info.get('formats', []) if fmt.get('acodec') != 'none' and fmt.get('vcodec') == 'none']
                    audio_formats.sort(key=lambda x: x.get('abr', 0), reverse=True)
                    best_audio_format = audio_formats[0] if audio_formats else None
                    best_audio_format_id = best_audio_format.get('format_id') if best_audio_format else None

                    # Sử dụng cấu hình tối ưu để lấy định dạng tốt nhất
                    # Sử dụng cấu hình đơn giản hơn để tránh lỗi
                    options['format'] = "bestvideo[height<=1080]+bestaudio/best[height<=1080]"

                    # Sử dụng client web chuẩn để tránh giới hạn của client Android
                    options['extractor_args'] = {
                        'youtube': {
                            'player_client': ['web', 'ios'],
                            'player_skip': ['webpage', 'configs'],
                            'player_client_name': 'WEB',
                            'po_token': 'web.gvs+QmVhckBCYWNrQEJlYXJAQmFja0BAZWFAQB',
                            'formats': 'missing_pot'  # Hiển thị cả các định dạng thiếu PO token
                        }
                    }

                    # Thêm lệnh trực tiếp để đảm bảo có thể tải các định dạng cao hơn
                    print(f"🔍 Thử tải video với tham số formats=missing_pot...")
                    try:
                        # Tạo lệnh yt-dlp với tham số formats=missing_pot và po_token
                        cmd = [
                            'yt-dlp',
                            '-F',
                            '--extractor-args', 'youtube:formats=missing_pot',
                            '--extractor-args', 'youtube:player_client=android_creator',
                            '--extractor-args', 'youtube:player_client_name=ANDROID_CREATOR',
                            '--extractor-args', 'youtube:po_token=CjoSFEFuZHJvaWRDcmVhdG9yQXBwR0FGAT06GAoaCmFuZHJvaWRfYXBwIAEyAggBMgIIAzICCAI%3D',
                            '--user-agent', 'com.google.android.apps.youtube.creator/22.30.100 (Linux; U; Android 13; US) gzip',
                            self.url  # Sử dụng self.url
                        ]
                        # Tạo đường dẫn đến file log
                        log_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "log.txt")

                        # Mở file log để ghi output của yt-dlp
                        with open(log_file, 'a', encoding='utf-8') as log_f:
                            log_f.write(f"\n\n=== YT-DLP FORMAT CHECK LOG: {self.url} ===\n")
                            # Chuyển hướng stderr vào file log, nhưng giữ stdout để phân tích
                            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=log_f, text=True)

                        output = result.stdout

                        # Kiểm tra xem có định dạng 1080p không
                        if '1080p' in output:
                            print(f"✅ Đã tìm thấy định dạng 1080p với tham số formats=missing_pot")
                            # Cập nhật options
                            options['extractor_args']['youtube']['formats'] = 'missing_pot'

                            # Tải video trực tiếp bằng lệnh yt-dlp
                            print(f"🔍 Đang tải video với tham số formats=missing_pot...")
                            video_id = self._extract_video_id(self.url)
                            temp_output_file = f"{video_id}_temp.mp4"

                            # Tạo lệnh yt-dlp với các tham số tối ưu để tải video 1080p
                            # Sử dụng format linh hoạt thay vì format ID cụ thể
                            # Tạo đường dẫn đến file log
                            log_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "log.txt")

                            download_cmd = [
                                'yt-dlp',
                                '-f', 'bestvideo[height=1080][vcodec^=avc1][ext=mp4]+bestaudio[acodec=aac][ext=m4a][abr>=128][abr<=192]/bestvideo[height=1080][vcodec^=h264][ext=mp4]+bestaudio[acodec=aac][ext=m4a][abr>=128][abr<=192]/137+140/bestvideo[height=1080][ext=mp4][vcodec!*=vp9][vcodec!*=vp8][vcodec!*=av01][vcodec!*=av1]+bestaudio[acodec=aac][ext=m4a]/best[height>=720][ext=mp4][vcodec!*=vp9][vcodec!*=vp8][vcodec!*=av01][vcodec!*=av1]',  # GPU-optimized format for NVIDIA RTX 3070Ti
                                '--user-agent', 'com.google.android.apps.youtube.creator/22.30.100 (Linux; U; Android 13; US) gzip',
                                '--external-downloader', 'aria2c',
                                '--external-downloader-args', 'aria2c:-x 8 -k 1M --retry-wait=2 --max-tries=5 --summary-interval=0 --console-log-level=warn',
                                '-o', temp_output_file,
                                '--merge-output-format', 'mp4',
                                '--quiet',  # Ẩn các log debug
                                '--no-warnings',  # Không hiển thị cảnh báo
                                '--no-color',  # Không sử dụng màu
                                '--no-progress',  # Không hiển thị tiến trình
                                '--extractor-args', 'youtube:player_client=android_creator',  # Sử dụng client Android Creator
                                '--extractor-args', 'youtube:player_client_name=ANDROID_CREATOR',  # Sử dụng client ANDROID_CREATOR
                                '--extractor-args', 'youtube:po_token=CjoSFEFuZHJvaWRDcmVhdG9yQXBwR0FGAT06GAoaCmFuZHJvaWRfYXBwIAEyAggBMgIIAzICCAI%3D',  # Thêm po_token
                                '--extractor-args', 'youtube:formats=missing_pot',  # Tham số quan trọng để truy cập 1080p
                                '--geo-bypass',
                                '--geo-bypass-country', 'US',
                                '--referer', 'https://www.youtube.com/',
                                '--no-write-subs',  # Không tải xuống phụ đề
                                '--no-write-auto-subs',  # Không tải xuống phụ đề tự động
                                '--no-embed-subs',  # Không nhúng phụ đề vào video
                                '--add-header', 'Origin:https://www.youtube.com',
                                '--add-header', 'Sec-Fetch-Dest:empty',
                                '--add-header', 'Sec-Fetch-Mode:cors',
                                '--add-header', 'Sec-Fetch-Site:same-site',
                                '--add-header', 'X-YouTube-Client-Name:14',
                                '--add-header', 'X-YouTube-Client-Version:22.30.100',
                                self.url
                            ]

                            # Thêm tham số cookies - đây là yếu tố quan trọng để có thể tải video ở độ phân giải cao hơn
                            if self.cookies_file and os.path.exists(self.cookies_file):
                                download_cmd.insert(3, '--cookies')
                                download_cmd.insert(4, self.cookies_file)
                                print(f"✅ Sử dụng cookies từ file: {self.cookies_file}")

                            # Thêm các tham số khác
                            # Không sử dụng aria2c vì có thể gây ra vấn đề với định dạng 1080p

                            # Chạy lệnh tải xuống - không hiển thị lệnh chi tiết
                            # Ghi lệnh vào file log thay vì hiển thị trên console
                            with open(log_file, 'a', encoding='utf-8') as log_f:
                                log_f.write(f"\n[COMMAND] {' '.join(download_cmd)}\n")
                            # Mở file log để ghi output của yt-dlp
                            with open(log_file, 'a', encoding='utf-8') as log_f:
                                log_f.write(f"\n\n=== YT-DLP DOWNLOAD LOG: {self.url} ===\n")
                                # Chuyển hướng cả stdout và stderr vào file log
                                download_result = subprocess.run(download_cmd, stdout=log_f, stderr=log_f, text=True)

                            # Kiểm tra kết quả
                            if os.path.exists(temp_output_file):
                                print(f"✅ Đã tải thành công video với tham số formats=missing_pot")
                                self.original_video_path = os.path.abspath(temp_output_file)
                                return self.original_video_path
                            else:
                                print(f"⚠️ Lỗi khi tải video: {download_result.stderr}")
                    except Exception as e:
                        print(f"⚠️ Lỗi khi thử với tham số formats=missing_pot: {str(e)}")

                    # Hiển thị thông báo
                    print(f"🔍 Đã cập nhật cấu hình tải xuống để lấy định dạng tốt nhất có sẵn")
                    print(f"🔍 Định dạng hiện tại: {best_format['width']}x{best_format['height']} (format_id: {best_format_id})")
                    if best_audio_format:
                        print(f"🔍 Định dạng audio: {best_audio_format.get('format_note', '')} (format_id: {best_audio_format_id})")

                    # Thêm các tùy chọn để đảm bảo lấy được độ phân giải cao nhất
                    options['merge_output_format'] = 'mp4'
                    options['hls_prefer_native'] = True  # Hỗ trợ xử lý HLS (m3u8)

                    # Thêm các tùy chọn cho ffmpeg để ghép video và audio
                    options['postprocessor_args'] = {
                        'ffmpeg': ['-c:v', 'copy', '-c:a', 'aac', '-b:a', '192k']
                    }

                    # Chỉ sử dụng phương pháp trực tiếp - tỷ lệ thành công cao nhất
                    clients_to_try = [
                        # Phương pháp trực tiếp - tỷ lệ thành công cao nhất
                        {
                            'name': 'DIRECT_ANDROID',
                            'player_client': ['android'],
                            'po_token': 'CjoSFEFuZHJvaWRZb3VUdWJlQXBwR0FGAT06GAoaCmFuZHJvaWRfYXBwIAEyAggBMgIIAzICCAI%3D',
                            'formats': 'none',
                            'user_agent': 'com.google.android.youtube/18.20.35 (Linux; U; Android 13) gzip',
                            'format': 'bestvideo[height=1080][fps<=30][ext=mp4]+bestaudio[ext=m4a]/137+140/bestvideo[height=1080][fps<=30]+bestaudio/best[height=1080][fps<=30]/best',
                            'innertube_client': 'android',
                            'innertube_key': 'AIzaSyA8eiZmM1FaDVjRy-df2KTyQ_vz_yYM39w',
                            'innertube_context': '{"client":{"clientName":"ANDROID","clientVersion":"18.20.35","androidSdkVersion":33,"userAgent":"com.google.android.youtube/18.20.35 (Linux; U; Android 13) gzip","hl":"en","timeZone":"UTC","utcOffsetMinutes":0}}',
                            'client_headers': {
                                'X-YouTube-Client-Name': '3',
                                'X-YouTube-Client-Version': '18.20.35'
                            }
                        }
                    ]

                    # Sử dụng client đầu tiên trong danh sách
                    current_client = clients_to_try[0]

                    # Cấu hình client - xử lý trường hợp client không có các tham số cụ thể
                    if current_client['player_client'] is not None:
                        youtube_args = {
                            'player_skip': ['webpage', 'configs']
                        }

                        # Thêm các tham số nếu có
                        if current_client['player_client'] is not None:
                            youtube_args['player_client'] = current_client['player_client']

                        if current_client['player_client'] is not None:
                            youtube_args['player_client_name'] = current_client['name']

                        if current_client['po_token'] is not None:
                            youtube_args['po_token'] = current_client['po_token']

                        if current_client['formats'] is not None:
                            youtube_args['formats'] = current_client['formats']

                        options['extractor_args'] = {
                            'youtube': youtube_args
                        }
                    else:
                        # Xóa extractor_args nếu đã tồn tại
                        if 'extractor_args' in options:
                            del options['extractor_args']

                    # Cập nhật User-Agent
                    options['http_headers']['User-Agent'] = current_client['user_agent']

                    print(f"🔍 Đang thử với client: {current_client['name']}")

                    # Thêm các tùy chọn để vượt qua giới hạn của YouTube
                    options['geo_bypass'] = True
                    options['geo_bypass_country'] = 'US'

                    # Thêm các tùy chọn để tối ưu hóa tải xuống
                    options['concurrent_fragment_downloads'] = 16
                    options['retries'] = 10
                    options['fragment_retries'] = 10
                    options['file_access_retries'] = 10

                    # Sử dụng User-Agent của thiết bị cao cấp để tăng khả năng nhận được video 1080p
                    premium_user_agents = [
                        # iPad Pro - thường được YouTube cung cấp video chất lượng cao
                        'Mozilla/5.0 (iPad; CPU OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
                        # Smart TV - thường được cung cấp video 1080p hoặc 4K
                        'Mozilla/5.0 (SMART-TV; LINUX; Tizen 7.0) AppleWebKit/605.1.15 (KHTML, like Gecko) SamsungBrowser/18.0 Chrome/102.0.5005.125 TV Safari/605.1.15',
                        # MacBook Pro - thường được cung cấp video chất lượng cao
                        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15'
                    ]

                    # Chọn ngẫu nhiên một User-Agent
                    import random
                    selected_user_agent = random.choice(premium_user_agents)

                    options['http_headers'] = {
                        'User-Agent': selected_user_agent,
                        'Accept-Language': 'en-US,en;q=0.9',
                        'Accept': '*/*',
                        'Referer': 'https://www.youtube.com/',  # Thêm Referer để tăng tính xác thực
                        'Origin': 'https://www.youtube.com',    # Thêm Origin để tăng tính xác thực
                        'Sec-Fetch-Dest': 'empty',              # Thêm các header Sec-Fetch để giả lập trình duyệt hiện đại
                        'Sec-Fetch-Mode': 'cors',
                        'Sec-Fetch-Site': 'same-site'
                    }

                    # Sử dụng aria2c để tải xuống nhanh hơn
                    options['external_downloader'] = 'aria2c'
                    options['external_downloader_args'] = {
                        'aria2c': [
                            '-x', '16',  # 16 kết nối song song
                            '-k', '1M',  # Kích thước phân chia 1M
                            '--retry-wait=2',
                            '--max-tries=10',
                            '--optimize-concurrent-downloads=true',
                            '--file-allocation=none',
                            '--continue=true',
                            '--summary-interval=0',
                            '--console-log-level=warn'
                        ]
                    }

                # Chỉ thử phương pháp trực tiếp - tỷ lệ thành công cao nhất
                found_1080p = False

                # Sử dụng client DIRECT_ANDROID
                current_client = clients_to_try[0]

                # Cấu hình client
                youtube_args = {
                    'player_skip': ['webpage', 'configs', 'js'],  # Bỏ qua nhiều hơn
                    'max_formats': 100  # Tăng số lượng định dạng tối đa
                }

                # Thêm các tham số
                youtube_args['player_client'] = current_client['player_client']
                youtube_args['player_client_name'] = current_client['name']
                youtube_args['po_token'] = current_client['po_token']
                youtube_args['formats'] = current_client['formats']
                youtube_args['innertube_client'] = current_client['innertube_client']
                youtube_args['innertube_key'] = current_client['innertube_key']
                youtube_args['innertube_context'] = current_client['innertube_context']

                options['extractor_args'] = {
                    'youtube': youtube_args
                }

                # Thêm các header đặc biệt cho client
                for header_name, header_value in current_client['client_headers'].items():
                    options['http_headers'][header_name] = header_value

                # Cập nhật format - sử dụng format từ client
                options['format'] = current_client['format']

                # Cập nhật User-Agent
                options['http_headers']['User-Agent'] = current_client['user_agent']

                print(f"🔍 Đang thử với phương pháp trực tiếp: {current_client['name']}")

                try:
                    # Tạo đường dẫn đến file log
                    log_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "log.txt")

                    # Tạo ydl mới với client hiện tại
                    options['progress_hooks'] = [self._progress_hook]  # Thêm progress hook
                    options['logger'] = None  # Tắt logger
                    options['quiet'] = True  # Chế độ yên lặng
                    options['no_warnings'] = True  # Không hiển thị cảnh báo
                    options['no_color'] = True  # Không sử dụng màu

                    # Thêm xử lý lỗi NoneType
                    options['ignoreerrors'] = True  # Bỏ qua lỗi thay vì dừng
                    options['socket_timeout'] = 30  # Tăng thời gian chờ kết nối

                    # Mở file log để ghi output của yt-dlp
                    with open(log_file, 'a', encoding='utf-8') as log_f:
                        log_f.write(f"\n\n=== YT-DLP CLIENT {current_client['name']} LOG: {self.url} ===\n")

                    # Tạo class logger tùy chỉnh để ghi log vào file
                    class FileLogger:
                        def __init__(self, log_file):
                            self.log_file = log_file

                        def debug(self, msg):
                            with open(self.log_file, 'a', encoding='utf-8') as f:
                                f.write(f"[DEBUG] {msg}\n")

                        def info(self, msg):
                            with open(self.log_file, 'a', encoding='utf-8') as f:
                                f.write(f"[INFO] {msg}\n")

                        def warning(self, msg):
                            with open(self.log_file, 'a', encoding='utf-8') as f:
                                f.write(f"[WARNING] {msg}\n")

                        def error(self, msg):
                            with open(self.log_file, 'a', encoding='utf-8') as f:
                                f.write(f"[ERROR] {msg}\n")

                    # Sử dụng logger tùy chỉnh
                    options['logger'] = FileLogger(log_file)
                    ydl = yt_dlp.YoutubeDL(options)

                    # Lấy thông tin video (không tải)
                    info = ydl.extract_info(self.url, download=False, process=True)

                    # Kiểm tra xem info có phải là None không
                    if info is None:
                        print(f"\n⚠️ Không thể lấy thông tin video với phương pháp trực tiếp")
                        # Thử phương pháp tải trực tiếp
                        direct_download_result = self._try_direct_download()
                        if direct_download_result:
                            return direct_download_result
                    else:
                        # Kiểm tra xem có định dạng 1080p không
                        formats = info.get('formats', [])
                        # Lọc bỏ các định dạng None trước khi kiểm tra
                        valid_formats = [fmt for fmt in formats if fmt is not None]
                        has_1080p = any(fmt.get('height') == 1080 for fmt in valid_formats)

                        if has_1080p:
                            print(f"✅ Đã tìm thấy định dạng 1080p với phương pháp trực tiếp!")
                            found_1080p = True

                            # Cập nhật thông tin video
                            self.video_info = info

                            # Hiển thị các định dạng có sẵn
                            available_formats = []
                            for fmt in formats:
                                if fmt.get('height') and fmt.get('width'):
                                    format_info = {
                                        'height': fmt.get('height'),
                                        'width': fmt.get('width'),
                                        'ext': fmt.get('ext', 'unknown'),
                                        'format_id': fmt.get('format_id', ''),
                                        'format_note': fmt.get('format_note', ''),
                                        'vcodec': fmt.get('vcodec', 'unknown'),
                                        'acodec': fmt.get('acodec', 'unknown'),
                                        'filesize': fmt.get('filesize', 0)
                                    }
                                    available_formats.append(format_info)

                            # Sắp xếp theo độ phân giải giảm dần - đảm bảo không có giá trị None
                            def safe_sort_key(x):
                                height = x.get('height', 0) or 0  # Nếu height là None, sử dụng 0
                                width = x.get('width', 0) or 0    # Nếu width là None, sử dụng 0
                                return (height, width)

                            available_formats.sort(key=safe_sort_key, reverse=True)

                            # Hiển thị thông tin về các định dạng có sẵn
                            print("\n🔍 Các định dạng video tốt nhất có sẵn:")
                            for i, fmt in enumerate(available_formats[:5], 1):
                                filesize_mb = fmt['filesize'] / 1024 / 1024 if fmt['filesize'] else 0
                                print(f"  {i}. {fmt['width']}x{fmt['height']} - {fmt['format_note']} ({fmt['ext']}) - {filesize_mb:.1f}MB")

                            # Cấu hình để tải định dạng tốt nhất có sẵn
                            # Sử dụng format ID cụ thể: 137 (video 1080p MP4) + 140 (audio M4A)
                            # Hoặc sử dụng format linh hoạt để đảm bảo lấy được 1080p
                            options['format'] = 'bestvideo[height=1080]+bestaudio/137+140/bestvideo[height<=1080]+bestaudio/best[height<=1080]/best'

                            # Tải video
                            ydl.download([self.url])
                        else:
                            print(f"⚠️ Không tìm thấy định dạng 1080p với phương pháp trực tiếp")
                            # Thử phương pháp tải trực tiếp
                            direct_download_result = self._try_direct_download()
                            if direct_download_result:
                                return direct_download_result

                except Exception as e:
                    error_msg = str(e)
                    # Ghi log lỗi chi tiết
                    with open(log_file, 'a', encoding='utf-8') as log_f:
                        log_f.write(f"[ERROR] Lỗi khi thử với phương pháp trực tiếp: {error_msg}\n")

                    print(f"❌ Lỗi khi thử với phương pháp trực tiếp: {error_msg[:100]}...")

                    # Thử phương pháp tải trực tiếp
                    direct_download_result = self._try_direct_download()
                    if direct_download_result:
                        return direct_download_result

                # Thử phương pháp trực tiếp trước tiên, vì đây là phương pháp duy nhất có thể tải được 1080p
                print(f"🔍 Đang thử phương pháp trực tiếp để tải video ở độ phân giải 1080p...")

                # Thử lấy thông tin chi tiết về các định dạng có sẵn
                print(f"🔍 Đang lấy thông tin chi tiết về các định dạng có sẵn...")
                formats_info = self._get_available_formats()

                # Nếu tìm thấy định dạng 1080p, sử dụng format ID cụ thể
                if formats_info and 'best_1080p_format' in formats_info and formats_info['best_1080p_format']:
                    best_format = formats_info['best_1080p_format']
                    best_audio = formats_info.get('best_audio_format')

                    print(f"✅ Đã tìm thấy định dạng 1080p: {best_format['format_id']} - {best_format['width']}x{best_format['height']}")
                    if best_audio:
                        print(f"✅ Đã tìm thấy định dạng audio tốt nhất: {best_audio['format_id']}")

                    # Tải video với format ID cụ thể
                    format_spec = f"{best_format['format_id']}+{best_audio['format_id']}" if best_audio else best_format['format_id']
                    direct_download_result = self._try_direct_download(format_spec=format_spec)
                else:
                    # Thử tải trực tiếp với lệnh yt-dlp và các tham số tối ưu nhất
                    # Tập trung vào format 1080p
                    format_options = [
                        # GPU-optimized formats for NVIDIA RTX 3070Ti
                        'bestvideo[height=1080][vcodec^=avc1][ext=mp4]+bestaudio[acodec=aac][ext=m4a][abr>=128][abr<=192]',  # H.264 + AAC
                        'bestvideo[height=1080][vcodec^=h264][ext=mp4]+bestaudio[acodec=aac][ext=m4a][abr>=128][abr<=192]',  # H.264 + AAC
                        '137+140',  # 1080p H.264 + 128k AAC
                        'bestvideo[height=1080][ext=mp4][vcodec!*=vp9][vcodec!*=vp8][vcodec!*=av01][vcodec!*=av1]+bestaudio[acodec=aac][ext=m4a]',  # Exclude problematic codecs
                        'best[height>=720][ext=mp4][vcodec!*=vp9][vcodec!*=vp8][vcodec!*=av01][vcodec!*=av1]'  # Fallback
                    ]

                    # Thử từng format cho đến khi thành công
                    for i, format_option in enumerate(format_options):
                        print(f"🔍 Thử tải với format {i+1}/{len(format_options)}: {format_option}")
                        direct_download_result = self._try_direct_download(format_spec=format_option)
                        if direct_download_result:
                            print(f"✅ Đã tải thành công video với format: {format_option}")
                            break

                if direct_download_result:
                    print(f"✅ Đã tải thành công video với phương pháp trực tiếp")
                    return direct_download_result

                # Nếu phương pháp trực tiếp không thành công, thử lại với phương pháp _try_direct_download
                if not found_1080p:
                    print(f"🔍 Thử lại với phương pháp tải trực tiếp...")
                    direct_download_result = self._try_direct_download()
                    if direct_download_result:
                        print(f"✅ Đã tải thành công video với phương pháp tải trực tiếp")
                        return direct_download_result

                # Lưu đường dẫn file tạm
                temp_file = f"{video_id}_temp.mp4"
                temp_file_path = os.path.join(os.getcwd(), temp_file)
                if not os.path.exists(temp_file_path):
                    # Kiểm tra các file có pattern tương tự
                    potential_files = glob.glob(f"{video_id}_temp.*")
                    potential_mp4_files = [f for f in potential_files if f.endswith('.mp4')]
                    if potential_mp4_files:
                        temp_file = potential_mp4_files[0]
                        temp_file_path = os.path.join(os.getcwd(), temp_file)
                    else:
                        raise Exception(f"File video tạm không tồn tại: {temp_file}")

                # Xóa các file phụ đề nếu có
                subtitle_files = glob.glob(f"{video_id}.*.*")
                subtitle_files.extend(glob.glob(f"{video_id}*.*"))
                subtitle_files = [f for f in subtitle_files if f.endswith('.vtt') or f.endswith('.srt')]

                if subtitle_files:
                    print(f"🗑️ Đang xóa {len(subtitle_files)} file phụ đề không cần thiết...")
                    for sub_file in subtitle_files:
                        try:
                            os.remove(sub_file)
                            logging.debug(f"Đã xóa file phụ đề: {sub_file}")
                        except Exception as e:
                            logging.error(f"Không thể xóa file phụ đề {sub_file}: {str(e)}")

                # Tính năng phụ đề đã bị vô hiệu hóa hoàn toàn để tăng tốc độ xử lý
                logging.info("Tính năng phụ đề đã bị vô hiệu hóa hoàn toàn để tăng tốc độ xử lý")

                # Kiểm tra file JSON thông tin video
                json_file = f"{video_id}_temp.info.json"
                if os.path.exists(json_file):
                    try:
                        with open(json_file, 'r', encoding='utf-8') as f:
                            json_info = json.load(f)
                            # Cập nhật thông tin chi tiết hơn từ file JSON
                            if 'title' in json_info:
                                self.video_title = json_info['title']
                                print(f"📝 Tiêu đề chính xác: {self.video_title}")

                            # Kiểm tra độ phân giải thực tế của video đã tải
                            actual_height = None
                            if 'resolution' in json_info:
                                print(f"🔍 Độ phân giải từ JSON: {json_info['resolution']}")
                                if 'x' in json_info['resolution']:
                                    actual_height = int(json_info['resolution'].split('x')[1])
                            elif 'height' in json_info and 'width' in json_info:
                                print(f"🔍 Kích thước video: {json_info['width']}x{json_info['height']}")
                                actual_height = json_info['height']

                            # Kiểm tra thông tin chi tiết hơn về các format
                            if 'formats' in json_info:
                                print(f"🔍 Thông tin chi tiết về các format:")
                                for fmt in json_info['formats']:
                                    if fmt.get('format_id') == '137':
                                        print(f"  - Format 137 (1080p): {fmt.get('width')}x{fmt.get('height')} - {fmt.get('ext')} - {fmt.get('filesize', 0) / 1024 / 1024:.1f}MB")
                                    elif fmt.get('format_id') == '140':
                                        print(f"  - Format 140 (audio): {fmt.get('ext')} - {fmt.get('filesize', 0) / 1024 / 1024:.1f}MB")

                            # Kiểm tra thông tin về format đã chọn
                            if 'format' in json_info:
                                print(f"🔍 Format đã chọn: {json_info['format']}")
                            if 'format_id' in json_info:
                                print(f"🔍 Format ID đã chọn: {json_info['format_id']}")

                            # Kiểm tra xem độ phân giải thực tế có phải là cao nhất không
                            # Đảm bảo available_heights tồn tại và không rỗng
                            if actual_height and 'available_heights' in locals() and available_heights and len(available_heights) > 0:
                                if actual_height < available_heights[0]:
                                    print(f"⚠️ Độ phân giải thực tế ({actual_height}p) thấp hơn độ phân giải cao nhất có sẵn ({available_heights[0]}p)")
                                    print(f"🔄 Đang thử lại với cấu hình khác...")
                                else:
                                    print(f"✅ Độ phân giải thực tế ({actual_height}p) là tốt nhất có sẵn")
                            elif actual_height:
                                print(f"ℹ️ Độ phân giải thực tế: {actual_height}p (không có thông tin về các độ phân giải có sẵn)")

                                # Xóa file tạm để tải lại
                                temp_files = glob.glob(f"{video_id}_temp.*")
                                for temp_file in temp_files:
                                    try:
                                        os.remove(temp_file)
                                        print(f"🗑️ Đã xóa file tạm: {temp_file}")
                                    except Exception as e:
                                        logging.error(f"❌ Không thể xóa file tạm {temp_file}: {str(e)}")

                                # Thử lại với cấu hình đơn giản nhất nhưng vẫn ưu tiên 1080p
                                options['format'] = 'bestvideo[height=1080]+bestaudio/137+140/bestvideo[height<=1080]+bestaudio/best[height<=1080]/best'
                                options['quiet'] = False
                                options['verbose'] = True

                                # Xóa các tùy chọn có thể gây lỗi
                                if 'extractor_args' in options:
                                    del options['extractor_args']

                                # Sử dụng User-Agent thông thường
                                options['http_headers'] = {
                                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36',
                                    'Accept-Language': 'en-US,en;q=0.9',
                                    'Accept': '*/*',
                                }

                                # Đảm bảo sử dụng cookies
                                if self.cookies_file and os.path.exists(self.cookies_file):
                                    options['cookiefile'] = self.cookies_file
                                    print(f"🔑 Sử dụng cookies từ file: {self.cookies_file}")

                                # Xóa tùy chọn lấy cookies từ trình duyệt để tránh lỗi
                                if 'cookiesfrombrowser' in options:
                                    del options['cookiesfrombrowser']
                                print("🔑 Không sử dụng cookies từ trình duyệt để tránh lỗi")

                                print(f"🔄 Thử lại với cấu hình đơn giản nhất: best")

                                # Tải lại video
                                ydl = yt_dlp.YoutubeDL(options)
                                ydl.download([self.url])
                    except Exception as json_error:
                        logging.error(f"❌ Lỗi khi đọc file JSON: {str(json_error)}")

                # Lưu đường dẫn file gốc
                self.original_video_path = temp_file

                # Kiểm tra độ phân giải thực tế của video đã tải
                video_resolution = self._check_video_resolution(temp_file)
                if video_resolution:
                    print(f"🔍 Độ phân giải thực tế của video đã tải: {video_resolution}")

                    # Nếu độ phân giải thấp hơn 1080p và có thể tải lại
                    if video_resolution.lower() != "1920x1080" and video_resolution.lower() != "1080p":
                        print(f"⚠️ Video đã tải có độ phân giải thấp: {video_resolution}")
                        print(f"💡 Gợi ý: Thử xóa file cache và tải lại với tùy chọn --no-cache")

                # Thêm video vào cache
                self._add_to_cache(self.url, temp_file)
                print(f"💾 Đã thêm video vào cache: {os.path.basename(temp_file)}")

                return temp_file

            except Exception as e:
                logging.error(f"❌ Lỗi không mong đợi: {str(e)}")
                logging.error(traceback.format_exc())
                self._add_to_failed(self.url, str(e))
                return None

        except Exception as e:
            logging.error(f"❌ Không thể tải video: {self.url}")
            logging.error(traceback.format_exc())
            self._add_to_failed(self.url, str(e))
            return None

    def _get_available_formats(self):
        """Lấy thông tin chi tiết về các định dạng có sẵn"""
        try:
            # Lấy video ID từ URL
            video_id = self._extract_video_id(self.url)
            if not video_id:
                print(f"❌ Không thể lấy video ID từ URL: {self.url}")
                return None

            # Tạo lệnh yt-dlp với tham số --dump-json
            cmd = [
                'yt-dlp',
                '--dump-json',
                '--no-check-certificate',
                '--prefer-insecure',
                '--geo-bypass',
                '--geo-bypass-country', 'US',
                '--force-ipv4',
                '--extractor-args', 'youtube:player_client=android',
                '--extractor-args', 'youtube:player_client_name=ANDROID',
                '--extractor-args', 'youtube:innertube_client=android',
                '--extractor-args', 'youtube:innertube_key=AIzaSyA8eiZmM1FaDVjRy-df2KTyQ_vz_yYM39w',
                '--extractor-args', 'youtube:formats=none',
                '--user-agent', 'com.google.android.youtube/18.20.35 (Linux; U; Android 13) gzip',
                '--add-header', 'X-YouTube-Client-Name:3',
                '--add-header', 'X-YouTube-Client-Version:18.20.35',
                '--add-header', 'Origin:https://www.youtube.com',
                '--add-header', 'Referer:https://www.youtube.com/',
                self.url
            ]

            # Thêm tham số cookies nếu có
            if self.cookies_file and os.path.exists(self.cookies_file):
                cmd.extend(['--cookies', self.cookies_file])

            # Chạy lệnh
            print(f"🔍 Đang lấy thông tin định dạng với client Android...")
            from src.utils.subprocess_wrapper import safe_run
            result = safe_run(cmd, capture_output=True, text=True)

            # Kiểm tra kết quả
            if result.returncode == 0 and result.stdout:
                try:
                    # Parse JSON output
                    video_info = json.loads(result.stdout)

                    # Lấy danh sách các định dạng
                    formats = video_info.get('formats', [])

                    # Lọc các định dạng video và audio
                    video_formats = [f for f in formats if f.get('vcodec') != 'none' and f.get('acodec') == 'none']
                    audio_formats = [f for f in formats if f.get('acodec') != 'none' and f.get('vcodec') == 'none']

                    # Sắp xếp theo độ phân giải giảm dần - thêm kiểm tra None để tránh lỗi so sánh
                    def safe_sort_key(x):
                        height = x.get('height', 0) or 0  # Nếu height là None, sử dụng 0
                        width = x.get('width', 0) or 0    # Nếu width là None, sử dụng 0
                        tbr = x.get('tbr', 0) or 0        # Nếu tbr là None, sử dụng 0
                        return (height, width, tbr)

                    video_formats.sort(key=safe_sort_key, reverse=True)
                    audio_formats.sort(key=lambda x: x.get('tbr', 0) or 0, reverse=True)

                    # Tìm định dạng 1080p tốt nhất
                    best_1080p_format = next((f for f in video_formats if f.get('height') == 1080), None)

                    # Tìm định dạng audio tốt nhất
                    best_audio_format = audio_formats[0] if audio_formats else None

                    # Hiển thị thông tin
                    print(f"🔍 Đã tìm thấy {len(video_formats)} định dạng video và {len(audio_formats)} định dạng audio")

                    # Hiển thị thông tin chi tiết về các định dạng tốt nhất
                    if video_formats:
                        best_format = video_formats[0]
                        print(f"🔍 Định dạng video tốt nhất: {best_format.get('format_id')} - {best_format.get('width')}x{best_format.get('height')} ({best_format.get('ext')})")

                    if best_1080p_format:
                        print(f"🔍 Định dạng 1080p tốt nhất: {best_1080p_format.get('format_id')} - {best_1080p_format.get('width')}x{best_1080p_format.get('height')} ({best_1080p_format.get('ext')})")

                    if best_audio_format:
                        print(f"🔍 Định dạng audio tốt nhất: {best_audio_format.get('format_id')} - {best_audio_format.get('acodec')} ({best_audio_format.get('ext')})")

                    # Trả về thông tin
                    return {
                        'video_formats': video_formats,
                        'audio_formats': audio_formats,
                        'best_format': video_formats[0] if video_formats else None,
                        'best_1080p_format': best_1080p_format,
                        'best_audio_format': best_audio_format
                    }
                except json.JSONDecodeError:
                    print(f"❌ Không thể parse JSON output")
                    return None
            else:
                print(f"❌ Không thể lấy thông tin định dạng: {result.stderr}")
                return None
        except Exception as e:
            print(f"❌ Lỗi khi lấy thông tin định dạng: {str(e)}")
            return None

    def _try_direct_download(self, format_spec=None):
        """Thử tải video bằng lệnh yt-dlp trực tiếp với các tham số tối ưu nhất"""
        try:
            # Lấy video ID từ URL
            video_id = self._extract_video_id(self.url)
            if not video_id:
                print(f"❌ Không thể lấy video ID từ URL: {self.url}")
                return None

            # Tạo tên file tạm
            temp_output_file = f"{video_id}_temp.mp4"

            # Tạo lệnh yt-dlp với các tham số tối ưu nhất cho phương pháp DIRECT_ANDROID
            cmd = [
                'yt-dlp',
                # Format linh hoạt hoặc format ID cụ thể - ưu tiên 1080p
                '-f', format_spec if format_spec else 'bestvideo[height=1080][vcodec^=avc1][ext=mp4]+bestaudio[acodec=aac][ext=m4a][abr>=128][abr<=192]/bestvideo[height=1080][vcodec^=h264][ext=mp4]+bestaudio[acodec=aac][ext=m4a][abr>=128][abr<=192]/137+140/bestvideo[height=1080][ext=mp4][vcodec!*=vp9][vcodec!*=vp8][vcodec!*=av01][vcodec!*=av1]+bestaudio[acodec=aac][ext=m4a]/best[height>=720][ext=mp4][vcodec!*=vp9][vcodec!*=vp8][vcodec!*=av01][vcodec!*=av1]',

                # User-Agent Android - tốt nhất cho việc tải video 1080p
                '--user-agent', 'com.google.android.youtube/18.20.35 (Linux; U; Android 13) gzip',

                # Tham số đầu ra
                '-o', temp_output_file,
                '--merge-output-format', 'mp4',

                # Tham số trích xuất - sử dụng client Android để có thể tải 1080p
                '--extractor-args', 'youtube:player_client=android',
                '--extractor-args', 'youtube:player_client_name=ANDROID',
                '--extractor-args', 'youtube:player_skip=webpage,configs,js',
                '--extractor-args', 'youtube:innertube_client=android',
                '--extractor-args', 'youtube:innertube_key=AIzaSyA8eiZmM1FaDVjRy-df2KTyQ_vz_yYM39w',
                '--extractor-args', 'youtube:formats=none',
                '--extractor-args', 'youtube:po_token=CjoSFEFuZHJvaWRZb3VUdWJlQXBwR0FGAT06GAoaCmFuZHJvaWRfYXBwIAEyAggBMgIIAzICCAI%3D',

                # Tham số HTTP - giả lập client Android
                '--add-header', 'X-YouTube-Client-Name:3',
                '--add-header', 'X-YouTube-Client-Version:18.20.35',
                '--add-header', 'Origin:https://www.youtube.com',
                '--add-header', 'Referer:https://www.youtube.com/',

                # Tham số vượt qua giới hạn
                '--geo-bypass',
                '--geo-bypass-country', 'US',
                '--no-check-certificate',
                '--prefer-insecure',

                # Tham số tải xuống - tối ưu số lần thử lại để cân bằng giữa tốc độ và độ tin cậy
                '--retries', '10',
                '--fragment-retries', '10',
                '--file-access-retries', '10',
                '--extractor-retries', '10',
                '--skip-unavailable-fragments',
                '--force-ipv4',

                # Tham số tối ưu tốc độ tải xuống
                '--concurrent-fragments', '16',
                '--throttled-rate', '100K',
                '--buffer-size', '16M',

                # Tham số tối ưu aria2c
                '--external-downloader', 'aria2c',
                '--external-downloader-args', 'aria2c:-x 16 -k 1M --retry-wait=1 --max-tries=10 --file-allocation=none --optimize-concurrent-downloads=true',

                # Tham số hiển thị
                '--quiet',
                '--no-warnings',
                '--no-color',
                '--no-progress',

                # Tham số phụ đề - không tải phụ đề để tăng tốc độ
                '--no-write-subs',
                '--no-write-auto-subs',
                '--no-embed-subs',

                # URL video
                self.url
            ]

            # Thêm tham số cookies nếu có
            if self.cookies_file and os.path.exists(self.cookies_file):
                cmd.extend(['--cookies', self.cookies_file])
                print(f"🔑 Sử dụng cookies từ file: {self.cookies_file}")

            # Tạo đường dẫn đến file log
            log_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "log.txt")

            # Ghi lệnh vào file log
            with open(log_file, 'a', encoding='utf-8') as log_f:
                log_f.write(f"\n\n=== YT-DLP DIRECT DOWNLOAD COMMAND: {self.url} ===\n")
                log_f.write(f"{' '.join(cmd)}\n")

            # Chạy lệnh
            print(f"🔄 Đang tải video với phương pháp trực tiếp DIRECT_ANDROID...")
            with open(log_file, 'a', encoding='utf-8') as log_f:
                log_f.write(f"\n=== YT-DLP DIRECT DOWNLOAD OUTPUT: {self.url} ===\n")
                download_result = subprocess.run(cmd, stdout=log_f, stderr=log_f, text=True)
                # Ghi mã trạng thái vào log
                log_f.write(f"\nExit code: {download_result.returncode}\n")

            # Kiểm tra kết quả
            if os.path.exists(temp_output_file):
                print(f"✅ Đã tải thành công video với phương pháp trực tiếp DIRECT_ANDROID")

                # Kiểm tra độ phân giải
                resolution = self._check_video_resolution(temp_output_file)
                if resolution:
                    print(f"🔍 Độ phân giải thực tế của video đã tải: {resolution}")

                # Lưu đường dẫn file gốc
                self.original_video_path = os.path.abspath(temp_output_file)
                return self.original_video_path
            else:
                print(f"❌ Không thể tải video với phương pháp trực tiếp DIRECT_ANDROID")
                return None
        except Exception as e:
            print(f"❌ Lỗi khi tải video với phương pháp trực tiếp DIRECT_ANDROID: {str(e)}")
            return None

    def _create_cookies_from_browser(self):
        """Tạo file cookies từ trình duyệt"""
        try:
            # Tạo đường dẫn đến file cookies
            cookies_file = os.path.join(os.getcwd(), "cookies.txt")

            # Tạo lệnh yt-dlp để xuất cookies từ trình duyệt
            cmd = [
                'yt-dlp',
                '--cookies-from-browser', 'chrome',
                '--cookies', cookies_file,
                '-f', 'best',
                '--skip-download',
                'https://www.youtube.com/watch?v=dQw4w9WgXcQ'  # Video bất kỳ để lấy cookies
            ]

            # Chạy lệnh
            print(f"🔑 Đang tạo cookies từ trình duyệt Chrome...")
            result = subprocess.run(cmd, capture_output=True, text=True)

            # Kiểm tra kết quả
            if os.path.exists(cookies_file):
                print(f"✅ Đã tạo cookies thành công: {cookies_file}")
                return cookies_file
            else:
                print(f"❌ Không thể tạo cookies từ trình duyệt: {result.stderr}")
                return None
        except Exception as e:
            print(f"❌ Lỗi khi tạo cookies từ trình duyệt: {str(e)}")
            return None

    def _check_video_resolution(self, video_path):
        """Kiểm tra độ phân giải thực tế của video đã tải"""
        try:
            if not os.path.exists(video_path):
                return None

            # Sử dụng ffprobe để lấy thông tin video
            cmd = [
                'ffprobe',
                '-v', 'error',
                '-select_streams', 'v:0',
                '-show_entries', 'stream=width,height,codec_name,bit_rate,avg_frame_rate,duration',
                '-of', 'json',
                video_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0 and result.stdout.strip():
                try:
                    # Parse JSON output
                    info = json.loads(result.stdout)
                    if 'streams' in info and len(info['streams']) > 0:
                        stream = info['streams'][0]
                        width = stream.get('width', 0)
                        height = stream.get('height', 0)
                        codec = stream.get('codec_name', 'unknown')
                        bit_rate = stream.get('bit_rate', 'unknown')
                        frame_rate = stream.get('avg_frame_rate', 'unknown')
                        duration = stream.get('duration', 'unknown')

                        # Hiển thị thông tin chi tiết
                        print(f"📊 Thông tin video đã tải:")
                        print(f"  - Độ phân giải: {width}x{height}")
                        print(f"  - Codec: {codec}")
                        if bit_rate != 'unknown':
                            bit_rate_mb = float(bit_rate) / 1024 / 1024
                            print(f"  - Bitrate: {bit_rate_mb:.2f} Mbps")
                        if frame_rate != 'unknown' and '/' in frame_rate:
                            num, den = frame_rate.split('/')
                            if int(den) > 0:  # Tránh chia cho 0
                                fps = int(num) / int(den)
                                print(f"  - Frame rate: {fps:.2f} fps")
                        if duration != 'unknown':
                            duration_sec = float(duration)
                            minutes = int(duration_sec // 60)
                            seconds = int(duration_sec % 60)
                            print(f"  - Thời lượng: {minutes}:{seconds:02d}")

                        return f"{width}x{height}"
                except json.JSONDecodeError:
                    # Fallback to old method if JSON parsing fails
                    cmd = [
                        'ffprobe',
                        '-v', 'error',
                        '-select_streams', 'v:0',
                        '-show_entries', 'stream=width,height',
                        '-of', 'csv=s=x:p=0',
                        video_path
                    ]
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    if result.returncode == 0 and result.stdout.strip():
                        return result.stdout.strip()
            return None
        except Exception as e:
            logging.error(f"❌ Lỗi khi kiểm tra độ phân giải video: {str(e)}")
            return None

    def _progress_hook(self, d):
        """Hiển thị tiến trình tải video trên console và ghi log vào file log.txt"""
        # Tạo đường dẫn đến file log
        log_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "log.txt")

        if d['status'] == 'downloading':
            try:
                total = d.get('total_bytes') or d.get('total_bytes_estimate', 0)
                downloaded = d.get('downloaded_bytes', 0)
                speed = d.get('speed', 0)
                eta = d.get('eta', 0)

                if total > 0:
                    percent = (downloaded / total) * 100
                    speed_mb = speed / 1024 / 1024 if speed else 0
                    total_mb = total / 1024 / 1024
                    downloaded_mb = downloaded / 1024 / 1024

                    # Tính thời gian còn lại
                    eta_str = ""
                    if eta:
                        minutes, seconds = divmod(eta, 60)
                        hours, minutes = divmod(minutes, 60)
                        if hours > 0:
                            eta_str = f" - ETA: {hours}h{minutes:02d}m{seconds:02d}s"
                        else:
                            eta_str = f" - ETA: {minutes:02d}m{seconds:02d}s"

                    # Tạo thanh tiến trình
                    progress_bar_length = 20
                    filled_length = int(progress_bar_length * percent / 100)
                    bar = '█' * filled_length + '░' * (progress_bar_length - filled_length)

                    # Hiển thị tiến trình trên console
                    if percent % 5 < 0.1:  # Hiển thị mỗi 5%
                        print(f"⬇️ Đang tải: [{bar}] {percent:.1f}% ({downloaded_mb:.1f}MB/{total_mb:.1f}MB) - {speed_mb:.1f}MB/s{eta_str}")

                    # Ghi tiến trình vào file log
                    with open(log_file, 'a', encoding='utf-8') as f:
                        f.write(f"[PROGRESS] {percent:.1f}% | {downloaded_mb:.1f}/{total_mb:.1f}MB | {speed_mb:.1f}MB/s | ETA: {eta}s\n")

                    # Hiển thị tiến trình trên console khi đạt mốc 25%, 50%, 75%, 100%
                    if int(percent) % 25 == 0 and int(percent) > 0:
                        # Lưu giá trị đã hiển thị để tránh hiển thị nhiều lần
                        last_percent = getattr(self, '_last_percent', -1)
                        current_percent = int(percent) // 25 * 25  # Làm tròn xuống 25%, 50%, 75%, 100%

                        if current_percent != last_percent:
                            print(f"⭐ Đạt mốc {current_percent}% hoàn thành")
                            setattr(self, '_last_percent', current_percent)
            except Exception as e:
                # Chỉ log lỗi nghiêm trọng
                if not str(e).startswith('Division by zero'):
                    logging.error(f"❌ Lỗi ghi log tiến trình: {str(e)}")
        elif d['status'] == 'finished':
            # Ghi thông báo hoàn thành vào file log
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write("[PROGRESS] Đã tải xong video!\n")

            print(f"✅ Đã tải xong video, đang xử lý...")

            # Đặt lại giá trị _last_percent
            setattr(self, '_last_percent', -1)
        elif d['status'] == 'error':
            # Ghi thông báo lỗi vào file log
            error_msg = d.get('error', 'Unknown error')
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(f"[ERROR] {error_msg}\n")

            print(f"❌ Lỗi khi tải video: {error_msg}")

    def _clean_tag(self, tag: str) -> str:
        """Clean and optimize tag"""
        # Loại bỏ các ký tự đặc biệt và khoảng trắng
        tag = re.sub(r'[^\w\s]', '', tag)
        # Chuyển về lowercase
        tag = tag.lower()
        # Loại bỏ số và năm
        tag = re.sub(r'\b\d{4}\b', '', tag)  # Loại bỏ năm (4 chữ số)
        tag = re.sub(r'\b\d+\b', '', tag)    # Loại bỏ các số khác
        # Loại bỏ khoảng trắng đầu cuối
        tag = tag.strip()

        # Từ khóa chủ đề chính thường gặp
        topic_keywords = {
            # Phương tiện phổ biến
            'cybertruck', 'tesla', 'ford', 'raptor', 'fordraptor', 'f150', 'fordf150',
            'rollsroyce', 'bmw', 'mercedes', 'audi', 'toyota', 'honda', 'nissan', 'lamborghini',
            'ferrari', 'porsche', 'bugatti', 'mclaren', 'jeep', 'landrover', 'rangerover',
            # Hoạt động/Dự án
            'build', 'building', 'diy', 'homemade', 'handmade', 'crafting', 'making',
            'restoration', 'restore', 'remake', 'recreate', 'create', 'creation', 'project',
            # Chủ đề phụ
            'wooden', 'wood', 'metal', 'plastic', 'carbon', 'steel', 'aluminum', 'fiberglass',
            # Chủ đề nước/lặn
            'scuba', 'diving', 'underwater', 'river', 'lake', 'ocean', 'sea', 'beach',
            # Chủ đề tìm kiếm
            'treasure', 'found', 'find', 'search', 'hunt', 'hunting', 'discovery',
            # Sở thích phổ biến
            'gaming', 'tech', 'technology', 'review', 'tutorial', 'howto', 'cooking', 'food',
            'travel', 'fitness', 'workout', 'music', 'art', 'fashion', 'beauty', 'makeup',
            'photography', 'drone', 'gopro', 'camera'
        }

        # Loại bỏ các từ phổ biến không cần thiết
        common_words = {
            'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
            'this', 'that', 'these', 'those', 'when', 'where', 'why', 'how', 'what', 'who',
            'very', 'just', 'more', 'most', 'other', 'some', 'such', 'only', 'then', 'also',
            'after', 'before', 'now', 'than', 'too', 'into', 'over', 'again', 'once', 'under',
            'further', 'while', 'away', 'together', 'already', 'still', 'both', 'each', 'doing',
            'using', 'used', 'been', 'being', 'having', 'done', 'about', 'gave',
            'anybody', 'everyone', 'someone', 'nobody', 'anybody', 'everybody', 'part',
            'here', 'there', 'anywhere', 'everywhere', 'somewhere',
            # Loại bỏ tag chung không có giá trị
            'video', 'youtube', 'watch', 'subscribe', 'channel', 'content', 'follow',
            'official', 'click', 'please', 'like', 'comment', 'share', 'check',
            'http', 'https', 'www', 'com', 'net', 'org'}

        # Từ điển các từ nên kết hợp
        compound_words = {
            'ford': {'f150': 'fordf150', 'raptor': 'fordraptor'},
            'f150': {'raptor': 'f150raptor'},
            'rolls': {'royce': 'rollsroyce'},
            'cyber': {'truck': 'cybertruck'},
            'range': {'rover': 'rangerover'},
            'land': {'rover': 'landrover'}
        }

        # Từ điển các từ nên tách
        words_to_split = {
            'buildrollsroyce': ['build', 'rollsroyce'],
            'buildfordraptor': ['build', 'fordraptor'],
            'buildfordf150': ['build', 'fordf150'],
            'buildcybertruck': ['build', 'cybertruck'],
            'woodentank': ['wooden', 'tank'],
            'woodencar': ['wooden', 'car'],
            'woodenjeep': ['wooden', 'jeep'],
            'restorationproject': ['restoration', 'project'],
            'homemadecar': ['homemade', 'car'],
            'diybuild': ['diy', 'build']
        }

        # Kiểm tra xem có cần tách tag không
        for compound, parts in words_to_split.items():
            if compound in tag:
                return parts[0]  # Trả về phần đầu tiên

        # Tách từ và phân loại
        words = tag.split()
        topic_words = []
        other_words = []

        # Kiểm tra các cặp từ nên kết hợp
        if len(words) >= 2:
            for i in range(len(words)-1):
                if words[i] in compound_words and words[i+1] in compound_words[words[i]]:
                    compound_word = compound_words[words[i]][words[i+1]]
                    if compound_word not in topic_words and compound_word in topic_keywords:
                        topic_words.append(compound_word)
                        words[i] = ""
                        words[i+1] = ""

        # Lọc các từ còn lại
        for word in words:
            word = word.strip()
            if word and len(word) >= 3 and word not in common_words:
                if word in topic_keywords:
                    if word not in topic_words:  # Tránh trùng lặp
                        topic_words.append(word)
                else:
                    if word not in other_words:  # Tránh trùng lặp
                        other_words.append(word)

        # Ưu tiên từ khóa chủ đề
        if topic_words:
            # Lấy từ khóa chủ đề đầu tiên, ưu tiên từ dài hơn
            topic_words.sort(key=len, reverse=True)
            return topic_words[0]
        elif other_words:
            # Lấy từ dài nhất trong các từ khác
            other_words.sort(key=len, reverse=True)
            return other_words[0]
        else:
            return ""

    def _remove_long_hashtags(self, tags: List[str]) -> List[str]:
        """Loại bỏ các hashtag quá dài hoặc hashtag được ghép từ nhiều từ"""
        result = []
        MAX_TAG_LENGTH = 20

        # Danh sách các pattern để nhận dạng hashtag ghép
        known_patterns = [
            r'fullvideo[a-z]+', # fullvideo + bất kỳ chuỗi nào
            r'buildcreative[a-z]+',
            r'fullbuild[a-z]+',
            r'buildingcreative[a-z]+',
            r'waterslideparkto[a-z]+',
            r'swimminpool[a-z]+'
        ]

        for tag in tags:
            # Kiểm tra độ dài
            if len(tag) > MAX_TAG_LENGTH:
                logging.debug(f"Bỏ qua tag quá dài: {tag}")
                continue

            # Kiểm tra các pattern đặc biệt
            skip = False
            for pattern in known_patterns:
                if re.match(pattern, tag.lower()):
                    logging.debug(f"Bỏ qua tag ghép từ các từ: {tag}")
                    skip = True
                    break

            if not skip:
                result.append(tag)

        return result

    def _get_video_info(self) -> Optional[Dict]:
        """Get video information"""
        try:
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
                'extract_flat': True,
                'writeinfojson': True,
                'skip_download': True,
                'writesubtitles': False,  # Không lưu phụ đề
                'writeautomaticsub': False,  # Không lưu phụ đề tự động
                'allsubtitles': False,  # Không lưu tất cả phụ đề
                'subtitleslangs': [],  # Không có ngôn ngữ phụ đề
                'embedsubtitles': False,  # Không nhúng phụ đề
                'http_headers': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Accept': '*/*',
                    'Accept-Encoding': 'gzip, deflate',
                    'Connection': 'keep-alive'
                }
            }

            if self.cookies_file:
                ydl_opts['cookiefile'] = self.cookies_file

            # Thêm cấu hình đặc biệt để chống bot
            ydl_opts.update({
                'extractor_args': {
                    'youtube': {
                        'player_client': ['android', 'web'],
                        'player_skip': ['webpage', 'configs', 'js'],
                        'player_client_name': 'ANDROID'
                    }
                }
            })

            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                try:
                    info = ydl.extract_info(self.url, download=False)
                except yt_dlp.utils.ExtractorError as e:
                    error_msg = str(e)
                    logging.error(f"Error getting video info: {error_msg}")
                    print(f"⚠️ Lỗi khi lấy thông tin video. Đang thử lại với cấu hình thay thế...")

                    # Thử với cấu hình khác
                    alt_ydl_opts = {
                        'quiet': True,
                        'no_warnings': True,
                        'extract_flat': True,
                        'skip_download': True,
                        'cookiefile': self.cookies_file,
                        'writesubtitles': False,  # Không lưu phụ đề
                        'writeautomaticsub': False,  # Không lưu phụ đề tự động
                        'allsubtitles': False,  # Không lưu tất cả phụ đề
                        'subtitleslangs': [],  # Không có ngôn ngữ phụ đề
                        'embedsubtitles': False,  # Không nhúng phụ đề
                        'http_headers': {
                            'User-Agent': 'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.6312.70 Mobile Safari/537.36',
                            'Accept': '*/*',
                            'Accept-Language': 'en-US,en;q=0.9',
                            'Accept-Encoding': 'gzip, deflate',
                            'Referer': 'https://www.youtube.com/',
                            'Origin': 'https://www.youtube.com'
                        },
                        'extractor_args': {
                            'youtube': {
                                'player_client': ['android'],
                                'player_skip': ['webpage', 'configs', 'js'],
                                'player_client_name': 'ANDROID_CREATOR'
                            }
                        }
                    }

                    try:
                        with yt_dlp.YoutubeDL(alt_ydl_opts) as alt_ydl:
                            info = alt_ydl.extract_info(self.url, download=False)
                    except Exception as alt_error:
                        # Nếu vẫn lỗi, tiếp tục với một số thông tin mặc định
                        logging.error(f"Error getting video info with alternative config: {str(alt_error)}")

                        # Tìm video ID để sử dụng làm tên mặc định
                        video_id = None
                        if 'youtube.com/watch?v=' in self.url:
                            video_id = self.url.split('watch?v=')[-1].split('&')[0]
                        elif 'youtu.be/' in self.url:
                            video_id = self.url.split('youtu.be/')[-1].split('?')[0]

                        # Tạo thông tin mặc định
                        return {
                            'id': video_id,
                            'title': f"YouTube Video {video_id}",
                            'channel': "Unknown Channel",
                            'uploader': "Unknown Uploader",
                            'is_shorts': '/shorts/' in self.url,
                            'tags': [],
                            'optimized_tags': [],
                            'categories': [],
                            'description': ""
                        }

            if not info:
                logging.error("Failed to extract video info")
                return None

            # Extract video metadata
            video_id = info.get('id', '')
            video_title = info.get('title', 'Unknown Title')
            channel_name = info.get('channel', 'Unknown Channel')
            uploader = info.get('uploader', 'Unknown Uploader')
            channel_id = info.get('channel_id', '')
            tags = info.get('tags', [])
            categories = info.get('categories', [])
            description = info.get('description', '')

            # Extract hashtags from description and title
            optimized_tags = self._get_video_tags(
                title=video_title,
                description=description,
                original_tags=tags
            )

            video_info = {
                'id': video_id,
                'title': video_title,
                'channel': channel_name,
                'uploader': uploader,
                'channel_id': channel_id,
                'tags': tags,
                'optimized_tags': optimized_tags,
                'categories': categories,
                'description': description
            }

            # Store all extracted info
            return video_info

        except Exception as e:
            logging.error(f"Error getting video info: {str(e)}")
            return None

    def _get_video_tags(self, title, description, original_tags=None):
        """
        Tạo danh sách hashtag tối ưu từ tiêu đề, mô tả và tag gốc của video
        Trả về danh sách các tag (không có dấu #)
        """
        logging.info("🔍 Tạo danh sách hashtag tối ưu...")

        # Số lượng hashtag mục tiêu
        TARGET_TAGS_COUNT = 6

        # Danh sách kết quả
        result_tags = []

        # Các từ không có ý nghĩa, không nên dùng làm hashtag
        meaningless_words = {'a', 'an', 'the', 'and', 'or', 'but', 'is', 'are', 'was', 'were', 'be', 'been',
            'has', 'have', 'had', 'do', 'does', 'did', 'to', 'for', 'from', 'with', 'in',
            'on', 'at', 'by', 'about', 'as', 'into', 'like', 'through', 'after', 'over',
            'between', 'out', 'of', 'during', 'before', 'among', 'above', 'below',
            'your', 'my', 'our', 'their', 'his', 'her', 'its', 'this', 'that', 'these', 'those',
            'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'them', 'us',
            'who', 'what', 'where', 'when', 'why', 'how', 'which', 'there', 'here',
            'all', 'any', 'both', 'each', 'few', 'more', 'most', 'no', 'some', 'such',
            'up', 'down', 'left', 'right', 'out', 'off', 'over', 'under', 'again', 'once',
            'should', 'would', 'could', 'cant', 'cannot', 'wont', 'will', 'shall',
            'can', 'get', 'got', 'like', 'make', 'many', 'take', 'while', 'year', 'good',
            'best', 'better', 'easy', 'hard', 'high', 'low', 'new', 'old', 'own', 'same',
            'used', 'able', 'done', 'sure', 'use', 'much', 'need', 'seen', 'want', 'does',
            'getting', 'making', 'taking', 'using', 'being', 'doing', 'having', 'looking',
            'going', 'coming', 'made', 'found', 'gave', 'feet', 'even', 'then',
            'video', 'youtube', 'watch', 'subscribe', 'channel', 'content', 'follow',
            'official', 'click', 'please', 'like', 'comment', 'share', 'check',
            'http', 'https', 'www', 'com', 'net', 'org'}

        # Danh sách các từ khóa có giá trị cao, nên ưu tiên sử dụng làm hashtag
        valuable_keywords = {
            'diy', 'howto', 'tutorial', 'guide', 'hack', 'trick', 'tips', 'review',
            'unboxing', 'test', 'comparison', 'versus', 'vs', 'best', 'top', 'worst',
            'funny', 'comedy', 'prank', 'challenge', 'fail', 'win', 'epic', 'amazing',
            'shocking', 'incredible', 'unbelievable', 'satisfying', 'relaxing', 'asmr',
            'sports', 'fitness', 'workout', 'exercise', 'health', 'yoga', 'meditation',
            'cooking', 'recipe', 'food', 'baking', 'vegan', 'vegetarian', 'keto', 'diet',
            'makeup', 'beauty', 'fashion', 'style', 'hair', 'skincare', 'clothing',
            'gaming', 'gameplay', 'walkthrough', 'speedrun', 'game', 'playthrough',
            'music', 'song', 'dance', 'choreography', 'concert', 'live', 'cover',
            'education', 'learn', 'school', 'college', 'university', 'study', 'science',
            'technology', 'tech', 'programming', 'coding', 'software', 'hardware', 'ai',
            'travel', 'vlog', 'tour', 'journey', 'adventure', 'trip', 'vacation',
            'news', 'politics', 'economy', 'business', 'finance', 'money', 'investing',
            'reaction', 'react', 'interview', 'podcast', 'debate', 'discussion',
            'motivation', 'inspiration', 'success', 'mindset', 'psychology', 'philosophy',
            'experiment', 'testing', 'restoration', 'transformation', 'building', 'craft',
            'animation', 'cartoon', 'anime', 'drawing', 'painting', 'art', 'design',
            'pets', 'animals', 'dogs', 'cats', 'wildlife', 'nature', 'environment',
            'history', 'documentary', 'biography', 'story', 'facts', 'mystery', 'crime'
        }

        # Danh sách các tag viral phổ biến
        viral_tags = [
            'trending', 'viral', 'popular', 'fyp', 'foryou', 'foryoupage',
            'explore', 'explorepage', 'recommended', 'viralvideo', 'trendingnow'
        ]

        # Ghi log thông tin chi tiết về dữ liệu đầu vào
        logging.info(f"🔍 Original tags count: {len(original_tags) if original_tags else 0}")
        logging.info(f"🔍 Title length: {len(title) if title else 0}")
        logging.info(f"🔍 Description length: {len(description) if description else 0}")

        # 1. Lấy tag từ danh sách các tag gốc trên YouTube (nếu có)
        used_original_tags = []
        if original_tags:
            # Lọc ra các tag có giá trị cao từ các tag gốc
            high_value_original_tags = []
            other_original_tags = []

            for tag in original_tags:
                tag = tag.lower().strip()
                if tag in valuable_keywords:
                    high_value_original_tags.append(tag)
                elif len(tag) >= 3 and tag not in meaningless_words and len(tag) <= 20:
                    other_original_tags.append(tag)

            # Ưu tiên sử dụng các tag có giá trị cao trước
            random.shuffle(high_value_original_tags)
            for tag in high_value_original_tags:
                if tag not in used_original_tags and tag not in result_tags:
                    result_tags.append(tag)
                    used_original_tags.append(tag)

                    # Nếu đã đủ số lượng tag cần thiết, dừng lại
                    if len(result_tags) >= TARGET_TAGS_COUNT - 1:  # -1 để dành chỗ cho viral tag
                        break

            # Nếu chưa đủ số lượng tag, tiếp tục lấy từ các tag khác
            if len(result_tags) < TARGET_TAGS_COUNT - 1:
                random.shuffle(other_original_tags)
                for tag in other_original_tags:
                    if tag not in used_original_tags and tag not in result_tags:
                        result_tags.append(tag)
                        used_original_tags.append(tag)

                        # Nếu đã đủ số lượng tag cần thiết, dừng lại
                        if len(result_tags) >= TARGET_TAGS_COUNT - 1:
                            break

            # Ghi log các tag đã thêm từ video gốc
            if used_original_tags:
                logging.info(f"👍 Đã thêm {len(used_original_tags)} tag từ video gốc: {', '.join(used_original_tags)}")

        # 2. Lấy tag từ tiêu đề video
        if title and len(result_tags) < TARGET_TAGS_COUNT - 1:
            # Trích xuất từ khóa từ tiêu đề
            title_words = re.findall(r'\b\w+\b', title.lower())

            # Lọc các từ có ý nghĩa
            meaningful_title_words = []
            for word in title_words:
                if (word in valuable_keywords or
                    (len(word) >= 3 and word not in meaningless_words and
                     not word.isdigit() and len(word) <= 20)):
                    meaningful_title_words.append(word)

            # Ưu tiên sử dụng các từ khóa có giá trị cao trong tiêu đề
            title_high_value_words = [w for w in meaningful_title_words if w in valuable_keywords]
            title_normal_words = [w for w in meaningful_title_words if w not in valuable_keywords]

            # Thêm từ khóa có giá trị cao
            title_tags_added = []
            for word in title_high_value_words:
                if word not in result_tags and word not in title_tags_added:
                    result_tags.append(word)
                    title_tags_added.append(word)

                    # Nếu đã đủ số lượng tag cần thiết, dừng lại
                    if len(result_tags) >= TARGET_TAGS_COUNT - 1:
                        break

            # Nếu chưa đủ tag, thêm các từ thông thường từ tiêu đề
            if len(result_tags) < TARGET_TAGS_COUNT - 1:
                for word in title_normal_words:
                    if word not in result_tags and word not in title_tags_added:
                        result_tags.append(word)
                        title_tags_added.append(word)

                        # Nếu đã đủ số lượng tag cần thiết, dừng lại
                        if len(result_tags) >= TARGET_TAGS_COUNT - 1:
                            break

            # Ghi log các tag đã thêm từ tiêu đề
            if title_tags_added:
                logging.info(f"👍 Đã thêm {len(title_tags_added)} tag từ tiêu đề: {', '.join(title_tags_added)}")

        # 3. Lấy tag từ mô tả video
        if description and len(result_tags) < TARGET_TAGS_COUNT - 1:
            # Trích xuất từ khóa từ mô tả
            desc_words = re.findall(r'\b\w+\b', description.lower())

            # Lọc các từ có ý nghĩa
            meaningful_desc_words = []
            for word in desc_words:
                if (word in valuable_keywords or
                    (len(word) >= 3 and word not in meaningless_words and
                     not word.isdigit() and len(word) <= 20)):
                    meaningful_desc_words.append(word)

            # Ưu tiên sử dụng các từ khóa có giá trị cao trong mô tả
            desc_high_value_words = [w for w in meaningful_desc_words if w in valuable_keywords]
            desc_normal_words = [w for w in meaningful_desc_words if w not in valuable_keywords]

            # Thêm từ khóa có giá trị cao
            desc_tags_added = []
            for word in desc_high_value_words:
                if word not in result_tags and word not in desc_tags_added:
                    result_tags.append(word)
                    desc_tags_added.append(word)

                    # Nếu đã đủ số lượng tag cần thiết, dừng lại
                    if len(result_tags) >= TARGET_TAGS_COUNT - 1:
                        break

            # Nếu chưa đủ tag, thêm các từ thông thường từ mô tả
            if len(result_tags) < TARGET_TAGS_COUNT - 1:
                for word in desc_normal_words:
                    if word not in result_tags and word not in desc_tags_added:
                        result_tags.append(word)
                        desc_tags_added.append(word)

                        # Nếu đã đủ số lượng tag cần thiết, dừng lại
                        if len(result_tags) >= TARGET_TAGS_COUNT - 1:
                            break

            # Ghi log các tag đã thêm từ mô tả
            if desc_tags_added:
                logging.info(f"👍 Đã thêm {len(desc_tags_added)} tag từ mô tả: {', '.join(desc_tags_added)}")

        # 4. Thêm các tag từ danh sách từ khóa có giá trị cao nếu chưa đủ số lượng
        if len(result_tags) < TARGET_TAGS_COUNT - 1:
            # Chuyển valuable_keywords từ set sang list để xáo trộn
            valuable_list = list(valuable_keywords)
            random.shuffle(valuable_list)

            auto_added_tags = []
            for keyword in valuable_list:
                if keyword not in result_tags and keyword not in auto_added_tags:
                    result_tags.append(keyword)
                    auto_added_tags.append(keyword)

                    # Nếu đã đủ số lượng tag cần thiết, dừng lại
                    if len(result_tags) >= TARGET_TAGS_COUNT - 1:
                        break

            # Ghi log các tag đã thêm tự động
            if auto_added_tags:
                logging.info(f"👍 Đã tự động thêm {len(auto_added_tags)} tag: {', '.join(auto_added_tags)}")

        # 5. Thêm 1 tag viral ngẫu nhiên
        random.shuffle(viral_tags)
        viral_tag = viral_tags[0]
        if viral_tag not in result_tags:
            result_tags.append(viral_tag)
            logging.info(f"👍 Đã thêm tag viral: {viral_tag}")

        # Xử lý các tag quá dài
        final_tags = []
        for tag in result_tags:
            # Giới hạn độ dài của tag là 20 ký tự
            if len(tag) > 20:
                shortened_tag = tag[:20]
                logging.info(f"📏 Tag quá dài, cắt bớt: {tag} -> {shortened_tag}")
                tag = shortened_tag

            # Chỉ thêm nếu tag hợp lệ
            if len(tag) >= 3 and tag not in final_tags:
                final_tags.append(tag)

        # Đảm bảo có đúng TARGET_TAGS_COUNT tag
        if len(final_tags) > TARGET_TAGS_COUNT:
            final_tags = final_tags[:TARGET_TAGS_COUNT]

        # Ghi log kết quả cuối cùng
        logging.info(f"🏷️ Danh sách {len(final_tags)} hashtag cuối cùng: {', '.join(['#' + tag for tag in final_tags])}")

        return final_tags

    def _ensure_video_info(self, is_shorts: bool) -> None:
        """Đảm bảo video_info có đầy đủ thông tin cần thiết bao gồm tên kênh"""
        try:
            # Lấy thông tin video_info mới nếu cần
            if not self.video_info:
                self.video_info = self._get_video_info()
                logging.info("✅ Đã lấy thông tin video mới từ _get_video_info()")

            # Kiểm tra xem video_info có tồn tại và có thuộc tính 'channel' không
            if self.video_info:
                channel_name = self.video_info.get('channel', None)
                if channel_name:
                    logging.info(f"✅ Đã lấy thông tin tên kênh từ video: {channel_name}")
                    print(f"👤 Kênh: {channel_name}")
                else:
                    logging.warning("⚠️ Không tìm thấy thông tin tên kênh trong video_info")

                    # Nếu có thông tin kênh từ channel_info nhưng không có trong video_info, thêm vào
                    if self.channel_info and 'name' in self.channel_info:
                        self.video_info['channel'] = self.channel_info['name']
                        logging.info(f"✅ Đã thêm tên kênh từ channel_info: {self.channel_info['name']}")
                        print(f"👤 Kênh (từ channel_info): {self.channel_info['name']}")
            else:
                logging.warning("⚠️ Không có thông tin video_info sau khi tải video")

                # Tạo video_info mặc định với thông tin cơ bản nếu không có
                if self.channel_info:
                    self.video_info = {
                        'title': self.video_title or 'Unknown Video',
                        'channel': self.channel_info.get('name', 'Unknown Channel'),
                        'channel_id': self.channel_info.get('id', ''),
                        'is_shorts': is_shorts
                    }
                    logging.info(f"✅ Đã tạo video_info mới với tên kênh: {self.video_info['channel']}")
                    print(f"👤 Kênh (tạo mới): {self.video_info['channel']}")
        except Exception as e:
            logging.error(f"❌ Lỗi khi đảm bảo thông tin video: {str(e)}")
            # Đảm bảo video_info luôn tồn tại
            if not self.video_info:
                self.video_info = {
                    'title': self.video_title or 'Unknown Video',
                    'channel': 'Unknown Channel',
                    'is_shorts': is_shorts
                }

    def _validate_downloaded_codec(self, video_path: str) -> bool:
        """
        Validate that the downloaded video uses GPU-compatible codec.

        Args:
            video_path: Path to the downloaded video file

        Returns:
            True if codec is GPU-compatible, False otherwise
        """
        try:
            # Use ffprobe to check video codec
            cmd = [
                'ffprobe',
                '-v', 'error',
                '-select_streams', 'v:0',
                '-show_entries', 'stream=codec_name',
                '-of', 'csv=p=0',
                video_path
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                codec = result.stdout.strip().lower()

                # Check if codec is GPU-compatible
                gpu_compatible_codecs = ['h264', 'avc1', 'h265', 'hevc', 'hvc1']
                problematic_codecs = ['vp9', 'vp09', 'av01', 'av1', 'vp8']

                is_compatible = any(compat in codec for compat in gpu_compatible_codecs)
                is_problematic = any(prob in codec for prob in problematic_codecs)

                if is_problematic:
                    print(f"⚠️ Downloaded video uses problematic codec: {codec} (may cause GPU processing issues)")
                    logging.warning(f"Downloaded video uses problematic codec: {codec}")
                    return False
                elif is_compatible:
                    print(f"✅ Downloaded video uses GPU-compatible codec: {codec}")
                    logging.info(f"Downloaded video uses GPU-compatible codec: {codec}")
                    return True
                else:
                    print(f"⚠️ Downloaded video uses unknown codec: {codec}")
                    logging.warning(f"Downloaded video uses unknown codec: {codec}")
                    return False
            else:
                print(f"Failed to detect video codec: {result.stderr}")
                logging.warning(f"Failed to detect video codec: {result.stderr}")
                return False

        except Exception as e:
            print(f"Error validating video codec: {e}")
            logging.error(f"Error validating video codec: {e}")
            return False







