import os
import re
import sys

def remove_title_code(file_path):
    """Loại bỏ tất cả các phần mã nguồn liên quan đến việc thêm tiêu đề vào video"""
    
    # Đọc nội dung file
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Thay thế hàm _get_title_filter bằng phiên bản đơn giản
    title_filter_pattern = r'def _get_title_filter\(.*?\):\s+""".*?"""\s+.*?return ""'
    title_filter_replacement = '''def _get_title_filter(self, font_path=None, font_size=70):
        """Tạo filter cho tiêu đề video - đã vô hiệu hóa

        Args:
            font_path: Đường dẫn đến font chữ
            font_size: Kích thước font chữ

        Returns:
            Chuỗi rỗng vì đã vô hiệu hóa việc thêm tiêu đề
        """
        # Không thêm tiêu đề nữa
        return ""'''
    
    # Sử dụng re.DOTALL để khớp với nhiều dòng
    content = re.sub(title_filter_pattern, title_filter_replacement, content, flags=re.DOTALL)
    
    # Loại bỏ các phần mã nguồn liên quan đến việc tìm font và hiển thị thông tin font
    font_pattern = r'# Tìm font chữ có sẵn cho watermark và tiêu đề.*?# Không thêm watermark nữa\s+watermark_filter = ""'
    font_replacement = '# Không sử dụng font, tiêu đề và watermark nữa\n            watermark_filter = ""'
    content = re.sub(font_pattern, font_replacement, content, flags=re.DOTALL)
    
    # Loại bỏ các phần mã nguồn liên quan đến việc chia tiêu đề thành nhiều dòng
    title_lines_pattern = r'# Nếu tiêu đề quá dài, chia thành nhiều dòng.*?self\.title_needs_two_lines = False'
    title_lines_replacement = '# Không sử dụng tiêu đề nữa'
    content = re.sub(title_lines_pattern, title_lines_replacement, content, flags=re.DOTALL)
    
    # Loại bỏ các phần mã nguồn liên quan đến việc đặt tiêu đề đơn giản
    simple_title_pattern = r'# Đặt tiêu đề đơn giản\s+self\.title_line1 = "Video"\s+self\.title_needs_two_lines = False'
    simple_title_replacement = '# Không sử dụng tiêu đề nữa'
    content = re.sub(simple_title_pattern, simple_title_replacement, content, flags=re.DOTALL)
    
    # Sửa các filter complex để loại bỏ watermark_filter và subtitle_filter
    filter_pattern = r'{watermark_filter}{subtitle_filter}\[v\];'
    filter_replacement = '[v];'
    content = re.sub(filter_pattern, filter_replacement, content)
    
    # Ghi nội dung đã sửa vào file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Đã loại bỏ mã nguồn liên quan đến việc thêm tiêu đề trong file {file_path}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        file_path = "src/processors/advanced_video_processor.py"
    
    remove_title_code(file_path)
