#!/usr/bin/env python3
"""
Test the exact FFmpeg command structure used by the video processor
"""
import subprocess
import time
import threading
import os

def monitor_gpu_detailed():
    """Monitor GPU with detailed encoding stats"""
    print("🎮 Monitoring GPU encoding...")
    encoding_detected = False
    
    for i in range(20):
        try:
            # Try nvidia-smi dmon first
            result = subprocess.run([
                'nvidia-smi', 'dmon', '-s', 'pucvmet', '-c', '1'
            ], capture_output=True, text=True, timeout=2)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                if len(lines) >= 2:
                    data_line = lines[-1]
                    values = data_line.split()
                    if len(values) >= 7:
                        gpu_util = int(values[1])
                        enc_util = int(values[3])
                        
                        if enc_util > 0:
                            print(f"  🎬 ENCODING DETECTED! GPU: {gpu_util}% | ENC: {enc_util}%")
                            encoding_detected = True
                        elif gpu_util > 20:
                            print(f"  🚀 GPU ACTIVE: {gpu_util}% | ENC: {enc_util}%")
                        
        except:
            # Fallback to basic nvidia-smi
            try:
                result = subprocess.run([
                    'nvidia-smi', '--query-gpu=utilization.gpu,memory.used',
                    '--format=csv,noheader,nounits'
                ], capture_output=True, text=True, timeout=2)
                
                if result.returncode == 0:
                    values = result.stdout.strip().split(', ')
                    gpu_util = int(values[0])
                    if gpu_util > 20:
                        print(f"  🚀 GPU: {gpu_util}%")
            except:
                pass
        
        time.sleep(0.5)
    
    return encoding_detected

def test_processor_style_command():
    """Test FFmpeg command similar to what the processor uses"""
    print("🧪 Testing processor-style FFmpeg command...")
    
    # Create test input if needed
    if not os.path.exists("test_input.mp4"):
        print("Creating test input...")
        subprocess.run([
            'ffmpeg', '-y', '-f', 'lavfi', '-i', 'testsrc2=duration=5:size=1920x1080:rate=30',
            '-f', 'lavfi', '-i', 'sine=frequency=1000:duration=5',
            '-c:v', 'libx264', '-c:a', 'aac', 'test_input.mp4'
        ], capture_output=True)
    
    # Build command similar to processor
    cmd = [
        'ffmpeg', '-y',
        '-hide_banner',
        '-ss', '0',
        '-to', '5',
        '-i', 'test_input.mp4',
        '-fps_mode', 'passthrough',
        # GPU encoding settings
        '-c:v', 'h264_nvenc',
        '-preset', 'fast',
        '-profile:v', 'high',
        '-cq', '23',
        '-b:v', '8000k',
        '-maxrate', '9600k',
        '-bufsize', '16000k',
        '-color_primaries', 'bt709',
        '-color_trc', 'bt709',
        '-colorspace', 'bt709',
        # Video filters (similar to processor)
        '-vf', 'fps=30,eq=gamma=1.0:contrast=1.0:brightness=0.0:saturation=1.0,scale=1920:1080:flags=lanczos:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2:black',
        # Audio settings
        '-c:a', 'aac', '-b:a', '128k', '-ar', '44100', '-ac', '2',
        # Container settings
        '-movflags', '+faststart',
        '-max_muxing_queue_size', '1024',
        '-avoid_negative_ts', 'make_zero',
        '-fflags', '+genpts',
        '-threads', '4',
        'test_processor_output.mp4'
    ]
    
    print(f"🔧 Command: {' '.join(cmd)}")
    
    # Start monitoring
    monitor_thread = threading.Thread(target=monitor_gpu_detailed)
    monitor_thread.daemon = True
    monitor_thread.start()
    
    # Run command
    start_time = time.time()
    result = subprocess.run(cmd, capture_output=True, text=True)
    end_time = time.time()
    
    print(f"\n📊 Results:")
    print(f"  Return code: {result.returncode}")
    print(f"  Duration: {end_time - start_time:.2f} seconds")
    
    if result.returncode == 0:
        print("  ✅ Processing successful!")
        
        # Check output
        if os.path.exists('test_processor_output.mp4'):
            file_size = os.path.getsize('test_processor_output.mp4')
            print(f"  📁 Output size: {file_size / (1024*1024):.2f} MB")
            
            # Check encoder
            probe_result = subprocess.run([
                'ffprobe', '-v', 'quiet', '-select_streams', 'v:0',
                '-show_entries', 'stream=encoder,codec_name,width,height',
                '-of', 'csv=p=0',
                'test_processor_output.mp4'
            ], capture_output=True, text=True)
            
            if probe_result.returncode == 0:
                info = probe_result.stdout.strip().split(',')
                if len(info) >= 4:
                    encoder = info[0]
                    codec = info[1]
                    width = info[2]
                    height = info[3]
                    print(f"  🔧 Encoder: {encoder}")
                    print(f"  📹 Codec: {codec}, Resolution: {width}x{height}")
                    
                    if 'nvenc' in encoder.lower():
                        print("  ✅ NVENC confirmed!")
                    else:
                        print("  ⚠️ NVENC not detected in encoder field")
    else:
        print("  ❌ Processing failed!")
        print(f"  Error: {result.stderr}")
    
    # Wait for monitoring
    monitor_thread.join(timeout=3)

def test_with_problematic_codec():
    """Test with VP9 input (problematic codec)"""
    print("\n🧪 Testing with VP9 input (problematic codec)...")
    
    # Create VP9 test input
    print("Creating VP9 test input...")
    vp9_cmd = [
        'ffmpeg', '-y', '-f', 'lavfi', '-i', 'testsrc2=duration=3:size=1920x1080:rate=30',
        '-f', 'lavfi', '-i', 'sine=frequency=1000:duration=3',
        '-c:v', 'libvpx-vp9', '-c:a', 'libopus', 'test_vp9_input.webm'
    ]
    
    vp9_result = subprocess.run(vp9_cmd, capture_output=True, text=True)
    
    if vp9_result.returncode != 0:
        print("  ❌ Failed to create VP9 input")
        return
    
    # Test NVENC with VP9 input
    cmd = [
        'ffmpeg', '-y',
        '-i', 'test_vp9_input.webm',
        '-c:v', 'h264_nvenc',
        '-preset', 'fast',
        '-cq', '23',
        '-c:a', 'aac',
        'test_vp9_to_h264_nvenc.mp4'
    ]
    
    print(f"🔧 VP9->H.264 NVENC: {' '.join(cmd)}")
    
    start_time = time.time()
    result = subprocess.run(cmd, capture_output=True, text=True)
    end_time = time.time()
    
    print(f"  Duration: {end_time - start_time:.2f} seconds")
    
    if result.returncode == 0:
        print("  ✅ VP9 to H.264 NVENC successful!")
    else:
        print("  ❌ VP9 to H.264 NVENC failed!")
        print(f"  Error: {result.stderr}")

if __name__ == "__main__":
    print("🎮 Processor Command Test")
    print("=" * 50)
    
    # Test processor-style command
    test_processor_style_command()
    
    # Test with problematic codec
    test_with_problematic_codec()
    
    print("\n🏁 All tests complete!")
