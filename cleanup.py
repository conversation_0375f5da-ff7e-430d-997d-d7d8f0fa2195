import os
import glob
import shutil
import re

def cleanup_files():
    """Xóa các file và thư mục không cần thiết"""
    
    # Danh s<PERSON>ch các file cần xóa
    files_to_remove = []
    
    # 1. Các file sửa lỗi tạm thời
    files_to_remove.extend(glob.glob("fix_*.py"))
    files_to_remove.extend(glob.glob("test_*.py"))
    files_to_remove.extend(glob.glob("create_test_*.py"))
    files_to_remove.extend(glob.glob("verify_*.py"))
    files_to_remove.extend(glob.glob("check_*.py"))
    files_to_remove.extend(glob.glob("improve_*.py"))
    files_to_remove.extend(glob.glob("add_*.py"))
    
    # 2. Các file backup không cần thiết
    files_to_remove.extend(glob.glob("*.bak"))
    files_to_remove.extend(glob.glob("*.bak2"))
    files_to_remove.extend(glob.glob("*.backup"))
    files_to_remove.extend(glob.glob("*.org"))
    
    # 3. Các file log và file tạm thời
    files_to_remove.append("log.txt")
    
    # Xóa các file trong thư mục src/processors
    processor_files_to_remove = []
    processor_files_to_remove.extend(glob.glob("src/processors/*.bak"))
    processor_files_to_remove.extend(glob.glob("src/processors/*.bak2"))
    processor_files_to_remove.extend(glob.glob("src/processors/*.backup"))
    processor_files_to_remove.extend(glob.glob("src/processors/*.org"))
    processor_files_to_remove.extend(glob.glob("src/processors/*.fixed"))
    processor_files_to_remove.extend(glob.glob("src/processors/*_backup.py"))
    processor_files_to_remove.extend(glob.glob("src/processors/*_fixed.py"))
    processor_files_to_remove.extend(glob.glob("src/processors/*_fix.py"))
    processor_files_to_remove.extend(glob.glob("src/processors/*_original.py"))
    
    # Thêm các file backup của youtube_downloader
    processor_files_to_remove.append("src/processors/youtube_downloader.py.backup")
    processor_files_to_remove.append("src/processors/youtube_downloader.py.fixed")
    processor_files_to_remove.append("src/processors/youtube_downloader.py_fixed")
    processor_files_to_remove.append("src/processors/youtube_downloader_original.py")
    
    # Thêm các file backup của video_processor
    processor_files_to_remove.append("src/processors/video_processor.py.backup")
    processor_files_to_remove.append("src/processors/video_processor.py.fixed")
    processor_files_to_remove.append("src/processors/video_processor.py.org")
    processor_files_to_remove.append("src/processors/video_processor_backup.py")
    processor_files_to_remove.append("src/processors/video_processor_fixed.py")
    
    # Xóa các file
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"✅ Đã xóa file: {file_path}")
            except Exception as e:
                print(f"❌ Không thể xóa file {file_path}: {str(e)}")
    
    # Xóa các file trong thư mục src/processors
    for file_path in processor_files_to_remove:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"✅ Đã xóa file: {file_path}")
            except Exception as e:
                print(f"❌ Không thể xóa file {file_path}: {str(e)}")
    
    # Xóa thư mục logs nếu tồn tại
    if os.path.exists("logs") and os.path.isdir("logs"):
        try:
            shutil.rmtree("logs")
            print(f"✅ Đã xóa thư mục: logs")
        except Exception as e:
            print(f"❌ Không thể xóa thư mục logs: {str(e)}")
    
    # Xóa thư mục __pycache__ trong src và các thư mục con
    for root, dirs, _ in os.walk("src"):
        for dir in dirs:
            if dir == "__pycache__":
                try:
                    pycache_path = os.path.join(root, dir)
                    shutil.rmtree(pycache_path)
                    print(f"✅ Đã xóa thư mục: {pycache_path}")
                except Exception as e:
                    print(f"❌ Không thể xóa thư mục {pycache_path}: {str(e)}")
    
    print("✅ Đã hoàn thành việc dọn dẹp các file và thư mục không cần thiết")

if __name__ == "__main__":
    # Xác nhận từ người dùng
    confirm = input("Bạn có chắc chắn muốn xóa các file và thư mục không cần thiết? (y/n): ")
    if confirm.lower() == 'y':
        cleanup_files()
    else:
        print("❌ Đã hủy việc dọn dẹp")
