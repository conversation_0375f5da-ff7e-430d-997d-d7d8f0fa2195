# YouTube to Tik<PERSON><PERSON> Converter (<PERSON><PERSON><PERSON> bản tối ưu)

Script tải video YouTube và xử lý thành format cho TikTok, Instagram Reels và YouTube Shorts với hiệu suất cao.

## C<PERSON><PERSON> tính năng chính

- Tải video từ YouTube ở chất lượng 1080p (8000-10000 kbps)
- Xử lý video theo định dạng:
  - Video ngang: Crop thành tỷ lệ 1:1 (1080x1080)
  - Video dọc: Giữ nguyên tỷ lệ (dành cho Shorts/Reels)
- Hiệu ứng video:
  - Tăng tốc ngẫu nhiên (tối đa 1.2x)
  - Lật ngang ngẫu nhiên mỗi 3-5 giây
  - Zoom nhẹ để tạo hiệu ứng chuyển động
- Tối ưu SEO:
  - Tự động trích xuất hashtags từ tiêu đề và tags YouTube
  - Tối ưu tên file với từ khóa SEO
- Phân đoạn video:
  - Video ngang: <PERSON><PERSON><PERSON> thành cá<PERSON> đo<PERSON>n 90-150 giây (1'30s-2'30s)
  - Video dọc/shorts: <PERSON><PERSON><PERSON> nguyên không cắt
- Tối ưu hiệu suất:
  - Sử dụng GPU NVENC để tăng tốc xử lý (tối ưu đặc biệt cho RTX 3070 Ti)
  - Xử lý song song với 2 luồng
  - Quản lý cache thông minh
  - Xử lý lỗi và tự động thử lại

## Cách sử dụng

### Xử lý video đơn lẻ

```
python run.py --url https://www.youtube.com/watch?v=VIDEO_ID
```

### Xử lý danh sách video

1. Tạo file `videos.txt` chứa các URL video YouTube (mỗi URL một dòng)
2. Chạy lệnh:

```
python run.py --videos_txt path/to/videos.txt
```

### Xử lý nhiều kênh

1. Tạo file `channel_list.txt` chứa đường dẫn đến các file videos.txt
2. Chạy lệnh:

```
python run.py --channel_list
```

## Yêu cầu hệ thống

- Python 3.8+
- FFmpeg (đã cài đặt trong E:\# GET-VIDEO\ffmpeg)
- NVIDIA GPU với hỗ trợ NVENC (tối ưu cho RTX 3070 Ti)
- Ít nhất 8GB RAM

## Cài đặt

1. Cài đặt các thư viện cần thiết:

```
pip install -r requirements.txt
```

2. Đảm bảo FFmpeg đã được cài đặt và cấu hình đúng

## Tối ưu hiệu suất

- Sử dụng SSD để lưu trữ video tạm thời
- Đảm bảo GPU driver đã được cập nhật
- Đóng các ứng dụng không cần thiết khi xử lý video
- Sử dụng tùy chọn `--gpu` để bật tăng tốc GPU

## Xử lý lỗi

Nếu gặp lỗi khi tải video, hãy thử các giải pháp sau:

1. Kiểm tra kết nối internet
2. Cập nhật yt-dlp: `pip install -U yt-dlp`
3. Kiểm tra URL video có hợp lệ không
4. Xóa cache: `python run.py --clear_cache`

## Liên hệ hỗ trợ

Nếu cần hỗ trợ thêm, vui lòng liên hệ qua email hoặc tạo issue trên GitHub.
