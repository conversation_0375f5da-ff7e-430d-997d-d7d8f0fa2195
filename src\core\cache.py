"""
Cache management for the video processor application.

Tối ưu hóa:
- <PERSON><PERSON><PERSON><PERSON> lý kích thước cache tự động
- <PERSON><PERSON>i thiện hiệu suất đọc/ghi
- Hỗ trợ nén dữ liệu để tiết kiệm không gian
- <PERSON><PERSON> chế khóa để tránh xung đột khi truy cập đồng thời
"""

import os
import json
import hashlib
import time
import logging
import shutil
import gzip
import threading
import tempfile
from typing import Optional, Dict, Any, Union, List, Tuple

logger = logging.getLogger("video_processor.cache")

# Default cache directory
DEFAULT_CACHE_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data", "cache")

# Cấu hình cache
DEFAULT_MAX_SIZE = 1024 * 1024 * 1024  # 1GB
DEFAULT_MAX_ENTRIES = 1000  # Số lượng entry tối đa
DEFAULT_COMPRESSION = True  # Bật nén mặc định


class Cache:
    """Cache manager for storing and retrieving data."""

    def __init__(self, cache_dir: Optional[str] = None, max_age: int = 86400,
                 max_size: int = DEFAULT_MAX_SIZE, max_entries: int = DEFAULT_MAX_ENTRIES,
                 compression: bool = DEFAULT_COMPRESSION):
        """
        Initialize the cache manager.

        Args:
            cache_dir: Directory to store cache files. If None, uses the default.
            max_age: Maximum age of cache entries in seconds (default: 24 hours).
            max_size: Maximum size of cache in bytes (default: 1GB).
            max_entries: Maximum number of cache entries (default: 1000).
            compression: Whether to compress cache data (default: True).
        """
        self.cache_dir = cache_dir or DEFAULT_CACHE_DIR
        self.max_age = max_age
        self.max_size = max_size
        self.max_entries = max_entries
        self.compression = compression
        self.index_file = os.path.join(self.cache_dir, "cache_index.json")
        self.lock = threading.RLock()  # Khóa tái nhập để tránh xung đột

        # Create cache directory if it doesn't exist
        os.makedirs(self.cache_dir, exist_ok=True)

        # Load cache index
        with self.lock:
            self.index = self._load_index()

            # Tự động dọn dẹp cache khi khởi tạo
            self.cleanup()

            # Kiểm tra và tối ưu kích thước cache
            self.optimize_cache()

    def _load_index(self) -> Dict[str, Dict[str, Any]]:
        """Load the cache index from disk."""
        if os.path.exists(self.index_file):
            try:
                with open(self.index_file, "r", encoding="utf-8") as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError) as e:
                logger.warning(f"Failed to load cache index: {e}")
        return {}

    def _save_index(self) -> None:
        """Save the cache index to disk."""
        try:
            with open(self.index_file, "w", encoding="utf-8") as f:
                json.dump(self.index, f, ensure_ascii=False, indent=2)
        except IOError as e:
            logger.warning(f"Failed to save cache index: {e}")

    def _get_cache_key(self, key: str) -> str:
        """Generate a cache key from the input key."""
        return hashlib.md5(key.encode()).hexdigest()

    def get(self, key: str) -> Optional[Dict[str, Any]]:
        """
        Get a value from the cache.

        Args:
            key: The cache key.

        Returns:
            The cached value, or None if not found or expired.
        """
        with self.lock:
            cache_key = self._get_cache_key(key)
            cache_info = self.index.get(cache_key)

            if not cache_info:
                return None

            # Check if cache entry is expired
            if time.time() - cache_info.get("timestamp", 0) > self.max_age:
                logger.debug(f"Cache entry expired for key: {key}")
                return None

            # Xác định tên file cache dựa vào compression
            file_ext = ".json.gz" if self.compression else ".json"
            cache_file = os.path.join(self.cache_dir, f"{cache_key}{file_ext}")

            # Nếu file nén không tồn tại, thử tìm file không nén
            if not os.path.exists(cache_file) and self.compression:
                cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")

            if not os.path.exists(cache_file):
                logger.warning(f"Cache file not found: {cache_file}")
                return None

            try:
                # Đọc file cache
                if cache_file.endswith('.gz'):
                    with gzip.open(cache_file, 'rt', encoding='utf-8') as f:
                        data = json.load(f)
                else:
                    with open(cache_file, "r", encoding="utf-8") as f:
                        data = json.load(f)

                # Cập nhật timestamp để đánh dấu entry này vừa được sử dụng
                self.index[cache_key]["timestamp"] = time.time()
                self._save_index()

                return data
            except (json.JSONDecodeError, IOError) as e:
                logger.warning(f"Failed to load cache file: {e}")
                return None

    def set(self, key: str, value: Dict[str, Any]) -> None:
        """
        Set a value in the cache.

        Args:
            key: The cache key.
            value: The value to cache.
        """
        with self.lock:
            cache_key = self._get_cache_key(key)

            # Xác định tên file cache dựa vào compression
            file_ext = ".json.gz" if self.compression else ".json"
            cache_file = os.path.join(self.cache_dir, f"{cache_key}{file_ext}")

            try:
                # Tạo file tạm để tránh lỗi khi ghi file
                with tempfile.NamedTemporaryFile(delete=False, mode='w' if not self.compression else 'wb') as temp_file:
                    temp_path = temp_file.name

                    # Ghi dữ liệu vào file tạm
                    if self.compression:
                        with gzip.open(temp_path, 'wt', encoding='utf-8') as f:
                            json.dump(value, f, ensure_ascii=False)
                    else:
                        with open(temp_path, 'w', encoding='utf-8') as f:
                            json.dump(value, f, ensure_ascii=False, indent=2)

                # Di chuyển file tạm vào vị trí cuối cùng
                shutil.move(temp_path, cache_file)

                # Cập nhật index
                self.index[cache_key] = {
                    "key": key,
                    "timestamp": time.time(),
                    "file": os.path.basename(cache_file),
                    "compressed": self.compression
                }

                # Lưu index
                self._save_index()

                # Kiểm tra và tối ưu cache nếu cần
                if len(self.index) % 10 == 0:  # Tối ưu sau mỗi 10 lần thêm mới
                    self.optimize_cache()

                logger.debug(f"Cached data for key: {key}")
            except IOError as e:
                logger.warning(f"Failed to cache data: {e}")
                # Xóa file tạm nếu có lỗi
                if 'temp_path' in locals() and os.path.exists(temp_path):
                    try:
                        os.remove(temp_path)
                    except:
                        pass

    def clear(self, key: Optional[str] = None) -> None:
        """
        Clear cache entries.

        Args:
            key: Specific key to clear. If None, clears all entries.
        """
        with self.lock:
            if key:
                cache_key = self._get_cache_key(key)
                if cache_key in self.index:
                    # Xác định tên file cache
                    cache_info = self.index[cache_key]
                    is_compressed = cache_info.get("compressed", False)

                    # Xóa cả file nén và không nén (nếu có)
                    for ext in [".json", ".json.gz"]:
                        cache_file = os.path.join(self.cache_dir, f"{cache_key}{ext}")
                        if os.path.exists(cache_file):
                            try:
                                os.remove(cache_file)
                                logger.debug(f"Removed cache file: {cache_file}")
                            except IOError as e:
                                logger.warning(f"Failed to remove cache file: {e}")

                    # Xóa khỏi index
                    del self.index[cache_key]
                    self._save_index()
                    logger.debug(f"Cleared cache for key: {key}")
            else:
                # Clear all cache entries
                for cache_key in list(self.index.keys()):
                    # Xóa cả file nén và không nén (nếu có)
                    for ext in [".json", ".json.gz"]:
                        cache_file = os.path.join(self.cache_dir, f"{cache_key}{ext}")
                        if os.path.exists(cache_file):
                            try:
                                os.remove(cache_file)
                                logger.debug(f"Removed cache file: {cache_file}")
                            except IOError as e:
                                logger.warning(f"Failed to remove cache file: {e}")

                # Xóa index
                self.index = {}
                self._save_index()
                logger.debug("Cleared all cache entries")

    def cleanup(self) -> None:
        """Remove expired cache entries."""
        with self.lock:
            current_time = time.time()
            expired_keys = []

            for cache_key, cache_info in self.index.items():
                if current_time - cache_info.get("timestamp", 0) > self.max_age:
                    expired_keys.append(cache_key)

            for cache_key in expired_keys:
                cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")
                if os.path.exists(cache_file):
                    try:
                        os.remove(cache_file)
                    except IOError as e:
                        logger.warning(f"Failed to remove expired cache file: {e}")

                del self.index[cache_key]

            if expired_keys:
                self._save_index()
                logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")

    def optimize_cache(self) -> None:
        """
        Tối ưu hóa kích thước cache bằng cách xóa các entry cũ nhất khi vượt quá giới hạn
        """
        with self.lock:
            try:
                # Kiểm tra số lượng entry
                if len(self.index) > self.max_entries:
                    # Sắp xếp theo thời gian, cũ nhất trước
                    sorted_entries = sorted(
                        [(k, v.get("timestamp", 0)) for k, v in self.index.items()],
                        key=lambda x: x[1]
                    )

                    # Xóa các entry cũ nhất
                    entries_to_remove = sorted_entries[:len(self.index) - self.max_entries]
                    for cache_key, _ in entries_to_remove:
                        cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")
                        if os.path.exists(cache_file):
                            try:
                                os.remove(cache_file)
                            except IOError as e:
                                logger.warning(f"Failed to remove cache file during optimization: {e}")

                        del self.index[cache_key]

                    logger.debug(f"Removed {len(entries_to_remove)} oldest cache entries to stay within limit")

                # Kiểm tra kích thước cache
                total_size = 0
                file_sizes = {}

                # Tính tổng kích thước cache
                for cache_key in self.index:
                    cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")
                    if os.path.exists(cache_file):
                        file_size = os.path.getsize(cache_file)
                        file_sizes[cache_key] = file_size
                        total_size += file_size

                # Nếu vượt quá giới hạn, xóa các file cũ nhất
                if total_size > self.max_size:
                    # Sắp xếp theo thời gian, cũ nhất trước
                    sorted_entries = sorted(
                        [(k, v.get("timestamp", 0)) for k, v in self.index.items()],
                        key=lambda x: x[1]
                    )

                    # Xóa các file cho đến khi kích thước cache giảm xuống dưới giới hạn
                    for cache_key, _ in sorted_entries:
                        if total_size <= self.max_size:
                            break

                        if cache_key in file_sizes:
                            cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")
                            if os.path.exists(cache_file):
                                try:
                                    file_size = file_sizes[cache_key]
                                    os.remove(cache_file)
                                    total_size -= file_size
                                    del self.index[cache_key]
                                    logger.debug(f"Removed cache file {cache_key} to reduce cache size")
                                except IOError as e:
                                    logger.warning(f"Failed to remove cache file during size optimization: {e}")

                # Lưu lại index sau khi tối ưu
                self._save_index()

                # Log thông tin cache
                cache_info = f"Cache stats: {len(self.index)} entries, {total_size / (1024*1024):.2f}MB"
                logger.debug(cache_info)

            except Exception as e:
                logger.error(f"Error optimizing cache: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """
        Lấy thông tin thống kê về cache

        Returns:
            Dict chứa thông tin thống kê về cache
        """
        with self.lock:
            total_size = 0
            oldest_timestamp = time.time()
            newest_timestamp = 0

            for cache_key, cache_info in self.index.items():
                cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")
                if os.path.exists(cache_file):
                    total_size += os.path.getsize(cache_file)

                timestamp = cache_info.get("timestamp", 0)
                oldest_timestamp = min(oldest_timestamp, timestamp)
                newest_timestamp = max(newest_timestamp, timestamp)

            return {
                "entries": len(self.index),
                "total_size_bytes": total_size,
                "total_size_mb": total_size / (1024 * 1024),
                "oldest_entry_age": time.time() - oldest_timestamp if self.index else 0,
                "newest_entry_age": time.time() - newest_timestamp if self.index else 0,
                "max_age": self.max_age,
                "max_size_mb": self.max_size / (1024 * 1024),
                "max_entries": self.max_entries,
                "compression": self.compression
            }
