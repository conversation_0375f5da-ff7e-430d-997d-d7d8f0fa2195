# Hướng dẫn khắc phục lỗi NVENC trong GET-VIDEO

Nếu bạn gặp lỗi `[h264_nvenc @ 000001a5584f5b80] [Eval @ 00000040279fd510] Undefined constant or missing '(' in 'fast...` khi chạy script run.py, hãy làm theo các bước sau để khắc phục:

## Bước 1: Sửa lỗi NVENC trong file advanced_video_processor.py

Chúng ta đã sửa lỗi này bằng cách:
1. Bỏ tham số `-tune fastdecode` trong lệnh FFmpeg
2. Đơn giản hóa cấu hình NVENC để tránh các tham số không được hỗ trợ

Bạn có thể chạy script `fix_nvenc.py` để tự động sửa lỗi này:

```
python fix_nvenc.py
```

## Bước 2: Chạy lại script với các tùy chọn đơn giản hơn

Chúng ta đã tạo script `run_simple.py` để chạy run.py với các tùy chọn đơn giản hơn:

```
python run_simple.py
```

Script này sẽ:
- Tự động thêm FFmpeg và Aria2c vào PATH
- Tắt các cảnh báo fontconfig
- Chạy run.py với các tùy chọn đơn giản hơn

## Bước 3: Nếu vẫn gặp lỗi

Nếu vẫn gặp lỗi, hãy thử các giải pháp sau:

### 3.1. Tắt GPU encoding

Nếu bạn vẫn gặp lỗi với NVENC, hãy sửa file `src/processors/advanced_video_processor.py` để tắt GPU encoding:

1. Mở file `src/processors/advanced_video_processor.py`
2. Tìm dòng `use_gpu = False` (khoảng dòng 1035)
3. Đảm bảo giá trị là `False` để tắt GPU encoding
4. Lưu file và chạy lại script

### 3.2. Sử dụng cấu hình CPU encoding đơn giản hơn

Nếu vẫn gặp lỗi, hãy sửa cấu hình CPU encoding để đơn giản hơn:

1. Mở file `src/processors/advanced_video_processor.py`
2. Tìm đoạn code sau (khoảng dòng 1127):
   ```python
   cmd.extend([
       '-c:v', 'libx264',
       '-preset', 'veryfast',  # Sử dụng veryfast để tăng tốc độ xử lý
       '-tune', 'film',      # Tối ưu hóa cho nội dung phim
       '-profile:v', 'main',  # Profile main cho tương thích tốt hơn
       '-pix_fmt', 'yuv420p',
       '-b:v', f"{bitrate}k",
       '-maxrate', f"{int(bitrate * 1.5)}k",  # Cho phép tăng bitrate ở các khung hình phức tạp
       '-bufsize', f"{bitrate * 2}k",  # Buffer size gấp đôi bitrate
       '-r', '30',  # Đặt tốc độ khung hình 30fps
       '-g', '30',  # Đặt GOP size là 30 frames
       '-bf', '2',  # Số lượng B-frames tối đa giữa các I/P-frames
       '-refs', '1',  # Giảm số lượng frame tham chiếu để tăng tốc
       '-threads', '0'  # Sử dụng tất cả các lõi CPU có sẵn
       # Bỏ các tham số không được hỗ trợ hoặc gây lỗi
   ])
   ```
3. Thay thế bằng đoạn code đơn giản hơn:
   ```python
   cmd.extend([
       '-c:v', 'libx264',
       '-preset', 'ultrafast',  # Sử dụng ultrafast để tăng tốc độ xử lý tối đa
       '-profile:v', 'baseline',  # Profile baseline cho tương thích tốt hơn
       '-pix_fmt', 'yuv420p',
       '-b:v', f"{bitrate}k",
       '-r', '30',  # Đặt tốc độ khung hình 30fps
       '-threads', '0'  # Sử dụng tất cả các lõi CPU có sẵn
   ])
   ```
4. Lưu file và chạy lại script

### 3.3. Kiểm tra phiên bản FFmpeg

Đảm bảo bạn đang sử dụng phiên bản FFmpeg mới nhất:

```
ffmpeg -version
```

Nếu phiên bản quá cũ, hãy tải phiên bản mới nhất từ [ffmpeg.org](https://ffmpeg.org/download.html) hoặc chạy lại script `install_components.bat`.

## Bước 4: Kiểm tra kết quả

Sau khi chạy lại script, kiểm tra thư mục đầu ra để xem video đã được tạo chưa:

1. Mở thư mục kênh (thư mục chứa file videos.txt)
2. Kiểm tra xem có file video nào được tạo không

## Liên hệ hỗ trợ

Nếu bạn vẫn gặp vấn đề sau khi thử tất cả các giải pháp trên, vui lòng liên hệ để được hỗ trợ.
