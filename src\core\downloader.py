"""
Video downloader module for the video processor application.
"""

import os
import subprocess
import logging
import time
import re
from typing import Optional, Dict, Any, List, Tuple, Callable

import yt_dlp

from src.core.exceptions import DownloadError, FormatNotFoundError, NetworkError
from src.core.models import VideoInfo, VideoFormat
from src.core.utils import extract_video_id, check_cookies_freshness, get_aria2c_path
from src.core.cache import Cache

logger = logging.getLogger("video_processor.downloader")


class VideoDownloader:
    """Video downloader for YouTube videos."""

    def __init__(self, url: str, config: Dict[str, Any], cache: Optional[Cache] = None):
        """
        Initialize the video downloader.

        Args:
            url: URL of the video to download.
            config: Configuration dictionary.
            cache: Cache instance for storing video info.
        """
        self.url = url
        self.config = config
        self.cache = cache or Cache()
        self.video_id = extract_video_id(url)
        self.video_info = None
        self.original_video_path = None

        # Get configuration values
        self.format = config.get("download", {}).get("format", "bestvideo[height<=1080][fps<=30][ext=mp4]+bestaudio[ext=m4a]/137+140/bestvideo[height<=1080][fps<=30]+bestaudio/best[height<=1080][fps<=30]/best")
        self.max_retries = config.get("download", {}).get("max_retries", 10)
        self.concurrent_fragments = config.get("download", {}).get("concurrent_fragments", 16)
        self.buffer_size = config.get("download", {}).get("buffer_size", "16M")
        self.use_aria2c = config.get("download", {}).get("use_aria2c", True)
        self.cookies_file = config.get("download", {}).get("cookies_file", "cookies.txt")
        self.cookies_max_age_days = config.get("download", {}).get("cookies_max_age_days", 7)

        # GPU optimization settings
        self.gpu_optimized = config.get("download", {}).get("gpu_optimized", True)
        self.prefer_h264 = config.get("download", {}).get("prefer_h264", True)
        self.avoid_vp9 = config.get("download", {}).get("avoid_vp9", True)
        self.avoid_av1 = config.get("download", {}).get("avoid_av1", True)

        # Smart download settings
        self.smart_retry = config.get("download", {}).get("smart_retry", True)
        self.retry_delay_base = config.get("download", {}).get("retry_delay_base", 2)
        self.fast_format_selection = config.get("download", {}).get("fast_format_selection", True)
        self.skip_unavailable_formats = config.get("download", {}).get("skip_unavailable_formats", True)
        self.connection_timeout = config.get("download", {}).get("connection_timeout", 15)
        self.read_timeout = config.get("download", {}).get("read_timeout", 30)
        self.aria2c_max_connections = config.get("download", {}).get("aria2c_max_connections", 8)

        # Validate URL and video ID
        if not self.video_id:
            logger.error(f"Invalid YouTube URL: {url}")
            raise ValueError(f"Invalid YouTube URL: {url}")

        # Check if cookies file exists and is fresh
        if self.cookies_file and os.path.exists(self.cookies_file):
            if not check_cookies_freshness(self.cookies_file, self.cookies_max_age_days):
                logger.warning(f"Cookies file is too old: {self.cookies_file}")
        else:
            logger.warning(f"Cookies file not found: {self.cookies_file}")

    def _get_gpu_optimized_format(self) -> str:
        """
        Generate GPU-optimized format selector for NVIDIA RTX 3070Ti.

        Prioritizes H.264/H.265 codecs and avoids VP9/AV1 for better GPU compatibility.

        Returns:
            Optimized format selector string.
        """
        if not self.gpu_optimized:
            return self.format

        # GPU-optimized format selector prioritizing H.264/H.265 for NVENC compatibility
        gpu_formats = []

        # 1. Highest priority: H.264 (AVC1) with AAC audio in MP4 container
        if self.prefer_h264:
            gpu_formats.extend([
                "bestvideo[height=1080][vcodec^=avc1][ext=mp4]+bestaudio[acodec=aac][ext=m4a]",
                "bestvideo[height=1080][vcodec^=h264][ext=mp4]+bestaudio[acodec=aac][ext=m4a]",
                "bestvideo[height=1080][vcodec*=avc1]+bestaudio[acodec=aac]",
                "bestvideo[height=1080][vcodec*=h264]+bestaudio[acodec=aac]"
            ])

        # 2. Second priority: H.265 (HEVC) with AAC audio
        gpu_formats.extend([
            "bestvideo[height=1080][vcodec^=hvc1][ext=mp4]+bestaudio[acodec=aac][ext=m4a]",
            "bestvideo[height=1080][vcodec^=h265][ext=mp4]+bestaudio[acodec=aac][ext=m4a]",
            "bestvideo[height=1080][vcodec*=hvc1]+bestaudio[acodec=aac]",
            "bestvideo[height=1080][vcodec*=h265]+bestaudio[acodec=aac]"
        ])

        # 3. Third priority: Specific format IDs known to be H.264
        gpu_formats.extend([
            "137+140",  # 1080p H.264 + AAC
            "299+140",  # 1080p60 H.264 + AAC
            "136+140",  # 720p H.264 + AAC (fallback)
        ])

        # 4. Fourth priority: Any MP4 format excluding VP9/AV1
        if self.avoid_vp9 and self.avoid_av1:
            gpu_formats.extend([
                "bestvideo[height=1080][ext=mp4][vcodec!*=vp9][vcodec!*=av01]+bestaudio[ext=m4a]",
                "bestvideo[height=1080][ext=mp4][vcodec!*=vp9][vcodec!*=av01]+bestaudio[acodec=aac]",
                "bestvideo[height=1080][vcodec!*=vp9][vcodec!*=av01]+bestaudio[acodec=aac]"
            ])
        elif self.avoid_vp9:
            gpu_formats.extend([
                "bestvideo[height=1080][ext=mp4][vcodec!*=vp9]+bestaudio[ext=m4a]",
                "bestvideo[height=1080][vcodec!*=vp9]+bestaudio[acodec=aac]"
            ])
        elif self.avoid_av1:
            gpu_formats.extend([
                "bestvideo[height=1080][ext=mp4][vcodec!*=av01]+bestaudio[ext=m4a]",
                "bestvideo[height=1080][vcodec!*=av01]+bestaudio[acodec=aac]"
            ])

        # 5. Fallback: Standard 1080p formats
        gpu_formats.extend([
            "bestvideo[height=1080][ext=mp4]+bestaudio[ext=m4a]",
            "bestvideo[height=1080]+bestaudio",
            "best[height=1080]",
            "best"
        ])

        # Join all formats with '/' separator
        optimized_format = "/".join(gpu_formats)

        logger.info("Using GPU-optimized format selector (prioritizing H.264/H.265, avoiding VP9/AV1)")

        # Display GPU optimization info to console
        try:
            from rich.console import Console
            console = Console()
            console.print("[green]🚀 GPU-optimized download: Prioritizing H.264/H.265 for NVIDIA RTX 3070Ti[/green]")
        except:
            pass

        return optimized_format

    def get_info(self) -> VideoInfo:
        """
        Get information about the video.

        Returns:
            VideoInfo object with video information.

        Raises:
            DownloadError: If video information cannot be retrieved.
        """
        # Check cache first
        cached_info = self.cache.get(self.url)
        if cached_info:
            logger.info(f"Using cached video info for: {self.url}")
            self.video_info = VideoInfo.from_yt_dlp_info(cached_info)
            return self.video_info

        # Set up yt-dlp options with GPU-optimized format
        gpu_format = self._get_gpu_optimized_format()
        options = {
            'format': gpu_format,
            'quiet': True,
            'no_warnings': True,
            'no_color': True,
            'no_progress': True,
            'ignoreerrors': True,
            'socket_timeout': 30,
            'retries': self.max_retries,
            'fragment_retries': self.max_retries,
            'file_access_retries': self.max_retries,
            'extractor_retries': self.max_retries,
            'skip_unavailable_fragments': True,
            'force_ipv4': True,
            'http_headers': {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'X-YouTube-Client-Name': '3',
                'X-YouTube-Client-Version': '18.20.35',
                'Origin': 'https://www.youtube.com',
                'Referer': 'https://www.youtube.com/'
            },
            # Thêm tùy chọn mới để vượt qua hạn chế
            'ignore_no_formats_error': True,
            'noplaylist': True,
            'check_formats': False,
            'geo_bypass': True,
            'geo_bypass_country': 'US',
            'no_check_certificate': True,
            'prefer_insecure': True
        }

        # Add cookies if available
        if self.cookies_file and os.path.exists(self.cookies_file):
            options['cookiefile'] = self.cookies_file

        # Configure client for 1080p access - sử dụng nhiều client khác nhau
        youtube_args = {
            'player_skip': ['webpage', 'configs', 'js'],
            'max_formats': 100,
            'player_client': ['android', 'web', 'mweb'],
            'player_client_name': ['ANDROID', 'WEB_REMIX', 'MWEB'],
            'po_token': 'CjoSFEFuZHJvaWRZb3VUdWJlQXBwR0FGAT06GAoaCmFuZHJvaWRfYXBwIAEyAggBMgIIAzICCAI%3D',
            'formats': 'none',
            'innertube_client': ['android', 'web', 'mweb'],
            'innertube_key': 'AIzaSyA8eiZmM1FaDVjRy-df2KTyQ_vz_yYM39w',
            'innertube_context': '{"client":{"clientName":"ANDROID","clientVersion":"18.20.35","androidSdkVersion":33,"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","hl":"en","timeZone":"UTC","utcOffsetMinutes":0}}'
        }

        options['extractor_args'] = {
            'youtube': youtube_args
        }

        try:
            logger.info(f"Retrieving video info for: {self.url}")
            with yt_dlp.YoutubeDL(options) as ydl:
                info = ydl.extract_info(self.url, download=False, process=True)

            if not info:
                logger.error(f"Failed to retrieve video info: {self.url}")
                raise DownloadError(f"Failed to retrieve video info: {self.url}")

            # Cache the video info
            self.cache.set(self.url, info)

            # Create VideoInfo object
            self.video_info = VideoInfo.from_yt_dlp_info(info)
            logger.info(f"Retrieved video info: {self.video_info.title}")
            return self.video_info

        except Exception as e:
            logger.error(f"Error retrieving video info: {e}")
            raise DownloadError(f"Error retrieving video info: {e}")

    def download_with_retry(self, output_dir: Optional[str] = None, progress_callback: Optional[Callable[[float], None]] = None, max_retries: int = 3) -> str:
        """
        Tải video với cơ chế thử lại tự động khi gặp lỗi.

        Args:
            output_dir: Thư mục lưu video. Nếu None, sử dụng thư mục hiện tại.
            progress_callback: Hàm callback để cập nhật tiến trình.
            max_retries: Số lần thử lại tối đa.

        Returns:
            Đường dẫn đến file video đã tải.

        Raises:
            DownloadError: Nếu tất cả các lần thử đều thất bại.
        """
        last_error = None

        for attempt in range(max_retries):
            try:
                logger.info(f"Tải video (lần thử {attempt+1}/{max_retries}): {self.url}")

                # Tăng thời gian chờ giữa các lần thử
                if attempt > 0:
                    wait_time = 2 * (attempt + 1)
                    logger.info(f"Chờ {wait_time} giây trước khi thử lại...")
                    time.sleep(wait_time)

                # Thử tải video
                return self.download(output_dir, progress_callback)

            except NetworkError as e:
                # Lỗi mạng, có thể thử lại
                last_error = e
                logger.warning(f"Lỗi mạng khi tải video (lần {attempt+1}/{max_retries}): {str(e)}")
                continue

            except FormatNotFoundError as e:
                # Lỗi không tìm thấy định dạng, thử lại với định dạng khác
                last_error = e
                logger.warning(f"Không tìm thấy định dạng (lần {attempt+1}/{max_retries}): {str(e)}")
                # Thay đổi định dạng cho lần thử tiếp theo
                self.format = "bestvideo[height<=720]+bestaudio/best[height<=720]/best" if attempt == 0 else "best"
                continue

            except DownloadError as e:
                # Lỗi tải video khác, thử lại
                last_error = e
                logger.warning(f"Lỗi tải video (lần {attempt+1}/{max_retries}): {str(e)}")
                continue

            except Exception as e:
                # Lỗi không xác định, thử lại
                last_error = e
                logger.warning(f"Lỗi không xác định (lần {attempt+1}/{max_retries}): {str(e)}")
                continue

        # Nếu đã thử hết số lần mà vẫn thất bại
        error_msg = f"Không thể tải video sau {max_retries} lần thử. Lỗi cuối cùng: {last_error}"
        logger.error(error_msg)
        raise DownloadError(error_msg)

    def download(self, output_dir: Optional[str] = None, progress_callback: Optional[Callable[[float], None]] = None) -> str:
        """
        Download the video.

        Args:
            output_dir: Directory to save the video. If None, uses current directory.
            progress_callback: Callback function for progress updates.

        Returns:
            Path to the downloaded video file.

        Raises:
            DownloadError: If video download fails.
        """
        # Get video info if not already retrieved
        if not self.video_info:
            self.get_info()

        # Create output directory if it doesn't exist
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

        # Set up output filename
        output_file = f"{self.video_id}_temp.mp4"
        if output_dir:
            output_file = os.path.join(output_dir, output_file)

        # Check if file already exists (from previous download)
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            if file_size > 1024 * 1024:  # File is larger than 1MB, likely a valid video
                logger.info(f"Using existing downloaded file: {output_file}")
                self.original_video_path = os.path.abspath(output_file)
                return self.original_video_path

        # Try multiple download methods to bypass bot detection
        download_methods = [
            ("direct_download", self._try_direct_download),
            ("bypass_method_1", self._try_bypass_download_1),
            ("bypass_method_2", self._try_bypass_download_2)
        ]

        for method_name, method_func in download_methods:
            try:
                logger.info(f"Attempting {method_name} for: {self.url}")
                result = method_func(output_file, progress_callback)

                if result and os.path.exists(result):
                    logger.info(f"Successfully downloaded with {method_name}")
                    self.original_video_path = result
                    return result
            except Exception as e:
                logger.warning(f"{method_name} failed: {e}")
                continue

        # Fall back to yt-dlp download
        logger.info(f"Falling back to yt-dlp download for: {self.url}")

        # Set up yt-dlp options with GPU-optimized format
        gpu_format = self._get_gpu_optimized_format()
        options = {
            'format': gpu_format,
            'outtmpl': output_file,
            'merge_output_format': 'mp4',
            'quiet': True,
            'no_warnings': True,
            'no_color': True,
            'retries': self.max_retries,
            'fragment_retries': self.max_retries,
            'file_access_retries': self.max_retries,
            'extractor_retries': self.max_retries,
            'skip_unavailable_fragments': True,
            'force_ipv4': True,
            'concurrent_fragments': self.concurrent_fragments,
            'buffer_size': self.buffer_size,
            'http_headers': {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'X-YouTube-Client-Name': '3',
                'X-YouTube-Client-Version': '18.20.35',
                'Origin': 'https://www.youtube.com',
                'Referer': 'https://www.youtube.com/'
            },
            # Thêm tùy chọn mới để vượt qua hạn chế
            'ignoreerrors': True,
            'ignore_no_formats_error': True,
            'noplaylist': True,
            'no_simulate': True,
            'check_formats': False,
            'force_overwrites': True,
            'geo_bypass': True,
            'geo_bypass_country': 'US',
            'no_check_certificate': True,
            'prefer_insecure': True
        }

        # Add cookies if available
        if self.cookies_file and os.path.exists(self.cookies_file):
            options['cookiefile'] = self.cookies_file

        # Configure client for 1080p access - sử dụng nhiều client khác nhau
        youtube_args = {
            'player_skip': ['webpage', 'configs', 'js'],
            'max_formats': 100,
            'player_client': ['android', 'web', 'mweb'],
            'player_client_name': ['ANDROID', 'WEB_REMIX', 'MWEB'],
            'po_token': 'CjoSFEFuZHJvaWRZb3VUdWJlQXBwR0FGAT06GAoaCmFuZHJvaWRfYXBwIAEyAggBMgIIAzICCAI%3D',
            'formats': 'none',
            'innertube_client': ['android', 'web', 'mweb'],
            'innertube_key': 'AIzaSyA8eiZmM1FaDVjRy-df2KTyQ_vz_yYM39w',
            'innertube_context': '{"client":{"clientName":"ANDROID","clientVersion":"18.20.35","androidSdkVersion":33,"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","hl":"en","timeZone":"UTC","utcOffsetMinutes":0}}'
        }

        options['extractor_args'] = {
            'youtube': youtube_args
        }

        # Use aria2c as external downloader if enabled
        if self.use_aria2c and os.path.exists(get_aria2c_path()):
            options['external_downloader'] = 'aria2c'
            options['external_downloader_args'] = [
                'aria2c:-x', '16',
                '-k', '1M',
                '--retry-wait=1',
                '--max-tries=10',
                '--file-allocation=none',
                '--optimize-concurrent-downloads=true'
            ]

        # Add progress hook if callback is provided
        if progress_callback:
            def progress_hook(d):
                if d['status'] == 'downloading':
                    if 'total_bytes' in d and d['total_bytes'] > 0:
                        percent = d['downloaded_bytes'] / d['total_bytes'] * 100
                    elif 'total_bytes_estimate' in d and d['total_bytes_estimate'] > 0:
                        percent = d['downloaded_bytes'] / d['total_bytes_estimate'] * 100
                    else:
                        percent = 0
                    progress_callback(percent)

            options['progress_hooks'] = [progress_hook]

        # Try multiple GPU-optimized formats, prioritizing H.264/H.265 and avoiding VP9/AV1
        formats_to_try = []

        # 1. GPU-optimized formats (H.264/H.265 priority)
        if self.gpu_optimized:
            formats_to_try.extend([
                # H.264 with AAC in MP4 container (best for NVENC)
                "bestvideo[height=1080][vcodec^=avc1][ext=mp4]+bestaudio[acodec=aac][ext=m4a]",
                "bestvideo[height=1080][vcodec^=h264][ext=mp4]+bestaudio[acodec=aac][ext=m4a]",
                "bestvideo[height=1080][vcodec*=avc1]+bestaudio[acodec=aac]",
                # H.265 with AAC (also good for NVENC)
                "bestvideo[height=1080][vcodec^=hvc1][ext=mp4]+bestaudio[acodec=aac][ext=m4a]",
                "bestvideo[height=1080][vcodec^=h265][ext=mp4]+bestaudio[acodec=aac][ext=m4a]",
                # Specific H.264 format IDs
                "137+140",  # 1080p H.264 + AAC
                "299+140",  # 1080p60 H.264 + AAC
                "136+140",  # 720p H.264 + AAC (fallback)
            ])

        # 2. Standard formats avoiding problematic codecs
        if self.avoid_vp9 and self.avoid_av1:
            formats_to_try.extend([
                "bestvideo[height=1080][ext=mp4][vcodec!*=vp9][vcodec!*=av01]+bestaudio[ext=m4a]",
                "bestvideo[height=1080][vcodec!*=vp9][vcodec!*=av01]+bestaudio[acodec=aac]",
                "bestvideo[height=720][ext=mp4][vcodec!*=vp9][vcodec!*=av01]+bestaudio[ext=m4a]",
                "bestvideo[height=720][vcodec!*=vp9][vcodec!*=av01]+bestaudio[acodec=aac]"
            ])
        elif self.avoid_vp9:
            formats_to_try.extend([
                "bestvideo[height=1080][ext=mp4][vcodec!*=vp9]+bestaudio[ext=m4a]",
                "bestvideo[height=1080][vcodec!*=vp9]+bestaudio[acodec=aac]"
            ])
        elif self.avoid_av1:
            formats_to_try.extend([
                "bestvideo[height=1080][ext=mp4][vcodec!*=av01]+bestaudio[ext=m4a]",
                "bestvideo[height=1080][vcodec!*=av01]+bestaudio[acodec=aac]"
            ])

        # 3. Standard fallback formats
        formats_to_try.extend([
            "bestvideo[height=1080][ext=mp4]+bestaudio[ext=m4a]",
            "bestvideo[height=1080]+bestaudio",
            gpu_format,  # Use the GPU-optimized format
            "bestvideo[height=720][ext=mp4]+bestaudio[ext=m4a]",
            "bestvideo[height=720]+bestaudio",
            "best[height>=720]",
            "bestvideo[height=480][ext=mp4]+bestaudio[ext=m4a]",
            "bestvideo[height=480]+bestaudio",
            "best"
        ])

        last_error = None
        for fmt in formats_to_try:
            try:
                options['format'] = fmt
                logger.info(f"Trying format: {fmt}")

                with yt_dlp.YoutubeDL(options) as ydl:
                    ydl.download([self.url])

                if os.path.exists(output_file):
                    logger.info(f"Downloaded video to: {output_file}")
                    self.original_video_path = os.path.abspath(output_file)

                    # Validate downloaded codec for GPU compatibility
                    is_gpu_compatible = self._validate_downloaded_codec(output_file)

                    # Verify the video resolution and codec compatibility
                    try:
                        # Use ffprobe to check video resolution and codec
                        cmd = [
                            'ffprobe',
                            '-v', 'error',
                            '-select_streams', 'v:0',
                            '-show_entries', 'stream=width,height,r_frame_rate,codec_name',
                            '-of', 'csv=p=0',
                            output_file
                        ]
                        result = subprocess.run(cmd, capture_output=True, text=True)
                        if result.returncode == 0:
                            output = result.stdout.strip()
                            parts = output.split(',')
                            if len(parts) >= 3:
                                width, height = map(int, parts[:2])
                                fps_str = parts[2] if len(parts) > 2 else "30/1"
                                codec = parts[3] if len(parts) > 3 else "unknown"

                                # Parse FPS
                                try:
                                    if "/" in fps_str:
                                        num, den = map(int, fps_str.split("/"))
                                        fps = num / den if den != 0 else 30
                                    else:
                                        fps = float(fps_str)
                                except:
                                    fps = 30

                                logger.info(f"Video: {width}x{height}, {fps:.1f}fps, codec: {codec}")

                                # Check if codec is GPU-compatible
                                gpu_compatible = codec.lower() in ['h264', 'avc1', 'h265', 'hevc', 'hvc1']
                                if gpu_compatible:
                                    logger.info(f"✅ GPU-compatible codec detected: {codec}")
                                else:
                                    logger.warning(f"⚠️ Non-optimal codec for GPU: {codec} (may fallback to CPU)")

                                # If height is at least 720, we're good enough
                                if height >= 1080:
                                    logger.info(f"Successfully downloaded 1080p video")
                                    return self.original_video_path
                                elif height >= 720:
                                    logger.info(f"Downloaded 720p video (acceptable)")
                                    return self.original_video_path
                                else:
                                    logger.warning(f"Downloaded video is low resolution: {height}p")
                                    # Continue to try other formats if this isn't the last one
                                    if fmt != formats_to_try[-1]:
                                        # Delete the low-res file and try another format
                                        try:
                                            os.remove(output_file)
                                            logger.info(f"Deleted low-res video, trying another format")
                                        except Exception as e:
                                            logger.warning(f"Failed to delete low-res video: {e}")
                                        continue
                    except Exception as e:
                        logger.warning(f"Failed to check video properties: {e}")

                    # If we couldn't verify resolution or this is the last format, return the path anyway
                    return self.original_video_path

            except Exception as e:
                last_error = e
                error_type = type(e).__name__
                error_msg = str(e)

                # Log detailed error information
                logger.warning(f"Failed to download with format {fmt}")
                logger.warning(f"Error type: {error_type}")
                logger.warning(f"Error message: {error_msg}")

                # Check for specific YouTube errors
                if "Sign in to confirm your age" in error_msg:
                    logger.error("Age-restricted video detected - requires authentication")
                elif "Video unavailable" in error_msg:
                    logger.error("Video is unavailable or private")
                elif "This video is not available" in error_msg:
                    logger.error("Video not available in your region")
                elif "HTTP Error 403" in error_msg:
                    logger.error("Access forbidden - possible bot detection")
                elif "HTTP Error 429" in error_msg:
                    logger.error("Rate limited - too many requests")
                elif "Unable to extract" in error_msg:
                    logger.error("YouTube extraction failed - possible bot detection")

                # Continue to the next format

        # If we get here, all formats failed
        if last_error:
            error_msg = f"All download methods failed. Last error: {str(last_error)}"
        else:
            error_msg = "All download methods failed. No specific error details available."

        logger.error(error_msg)
        logger.error(f"Tried {len(formats_to_try)} different formats for URL: {self.url}")

        # Log the formats that were tried
        logger.error(f"Formats attempted: {formats_to_try[:5]}{'...' if len(formats_to_try) > 5 else ''}")

        raise DownloadError(error_msg)

    def _try_direct_download(self, output_file: str, progress_callback: Optional[Callable[[float], None]] = None) -> Optional[str]:
        """
        Try to download the video using direct method with yt-dlp command line.

        Args:
            output_file: Path to save the video.
            progress_callback: Callback function for progress updates.

        Returns:
            Path to the downloaded video file or None if download fails.
        """
        if not self.video_id:
            logger.error("No video ID available for direct download")
            return None

        # Create GPU-optimized format for direct download
        # Prioritize H.264/H.265 codecs for NVIDIA RTX 3070Ti compatibility
        gpu_format = self._get_gpu_optimized_format()

        logger.info("Using GPU-optimized format for direct download (H.264/H.265 priority, avoiding VP9/AV1)")

        cmd = [
            'yt-dlp',
            # Format - use GPU-optimized format
            '-f', gpu_format,
            # User-Agent - sử dụng User-Agent mới
            '--user-agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            # Output
            '-o', output_file,
            '--merge-output-format', 'mp4',
            # Extractor args - sử dụng nhiều client khác nhau
            '--extractor-args', 'youtube:player_client=android,web,mweb',
            '--extractor-args', 'youtube:player_client_name=ANDROID,WEB_REMIX,MWEB',
            '--extractor-args', 'youtube:player_skip=webpage,configs,js',
            '--extractor-args', 'youtube:innertube_client=android,web,mweb',
            '--extractor-args', 'youtube:innertube_key=AIzaSyA8eiZmM1FaDVjRy-df2KTyQ_vz_yYM39w',
            '--extractor-args', 'youtube:formats=none',
            '--extractor-args', 'youtube:po_token=CjoSFEFuZHJvaWRZb3VUdWJlQXBwR0FGAT06GAoaCmFuZHJvaWRfYXBwIAEyAggBMgIIAzICCAI%3D',
            # HTTP headers - sử dụng headers mới
            '--add-header', 'X-YouTube-Client-Name:3',
            '--add-header', 'X-YouTube-Client-Version:18.20.35',
            '--add-header', 'Origin:https://www.youtube.com',
            '--add-header', 'Referer:https://www.youtube.com/',
            # Bypass options
            '--geo-bypass',
            '--geo-bypass-country', 'US',
            '--no-check-certificate',
            '--prefer-insecure',
            # Thêm tùy chọn mới để vượt qua hạn chế
            '--ignore-errors',
            '--ignore-no-formats-error',
            '--no-playlist',
            '--no-simulate',
            '--no-check-formats',
            '--force-overwrites',
            # Retry options
            '--retries', str(self.max_retries),
            '--fragment-retries', str(self.max_retries),
            '--file-access-retries', str(self.max_retries),
            '--extractor-retries', str(self.max_retries),
            '--skip-unavailable-fragments',
            '--force-ipv4',
            # Download options
            '--concurrent-fragments', str(self.concurrent_fragments),
            '--throttled-rate', '100K',
            '--buffer-size', self.buffer_size,
            # Display options
            '--quiet',
            '--no-warnings',
            '--no-color',
            '--no-progress',
            # Subtitle options
            '--no-write-subs',
            '--no-write-auto-subs',
            '--no-embed-subs',
            # URL
            self.url
        ]

        # Add cookies if available
        if self.cookies_file and os.path.exists(self.cookies_file):
            cmd.extend(['--cookies', self.cookies_file])

        # Use aria2c as external downloader if enabled
        if self.use_aria2c and os.path.exists(get_aria2c_path()):
            cmd.extend([
                '--external-downloader', 'aria2c',
                '--external-downloader-args', 'aria2c:-x 16 -k 1M --retry-wait=1 --max-tries=10 --file-allocation=none --optimize-concurrent-downloads=true'
            ])

        # Create log file
        log_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "log.txt")

        try:
            logger.info(f"Starting direct download for: {self.url}")

            # Run the command
            from src.utils.subprocess_wrapper import safe_popen
            process = safe_popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            # Monitor progress if callback is provided
            if progress_callback:
                while process.poll() is None:
                    time.sleep(1)
                    # Check if file exists and update progress based on file size
                    if os.path.exists(output_file):
                        file_size = os.path.getsize(output_file)
                        # Estimate progress (very rough estimate)
                        # Assume average 1080p video is around 100MB
                        estimated_size = 100 * 1024 * 1024
                        percent = min(file_size / estimated_size * 100, 99)
                        progress_callback(percent)

            # Wait for process to complete
            stdout, stderr = process.communicate()

            # Log output
            with open(log_file, 'a', encoding='utf-8') as log_f:
                log_f.write(f"\n\n=== YT-DLP DIRECT DOWNLOAD OUTPUT: {self.url} ===\n")
                log_f.write(f"Command: {' '.join(cmd)}\n")
                log_f.write(f"Exit code: {process.returncode}\n")
                log_f.write(f"STDOUT: {stdout}\n")
                log_f.write(f"STDERR: {stderr}\n")

            # Check if download was successful
            if process.returncode == 0 and os.path.exists(output_file):
                logger.info(f"Direct download successful: {output_file}")
                return os.path.abspath(output_file)
            else:
                logger.warning(f"Direct download failed with exit code: {process.returncode}")
                return None

        except Exception as e:
            logger.error(f"Error during direct download: {e}")
            return None

    def _get_gpu_optimized_format(self) -> str:
        """
        Get GPU-optimized format string for NVIDIA RTX 3070Ti.
        Prioritizes H.264/H.265 codecs and AAC audio for optimal GPU processing.

        Returns:
            Format string optimized for GPU processing
        """
        # Enhanced format string for NVIDIA RTX 3070Ti optimization
        # Strictly prioritize H.264/H.265 and avoid VP9/VP8/AV1
        return (
            # 1080p H.264 (avc1) with AAC audio - highest priority
            "bestvideo[height=1080][vcodec^=avc1][ext=mp4]+bestaudio[acodec=aac][ext=m4a][abr>=128][abr<=192]/"
            "bestvideo[height=1080][vcodec^=h264][ext=mp4]+bestaudio[acodec=aac][ext=m4a][abr>=128][abr<=192]/"

            # 1080p H.265 (hvc1) with AAC audio - second priority
            "bestvideo[height=1080][vcodec^=hvc1][ext=mp4]+bestaudio[acodec=aac][ext=m4a][abr>=128][abr<=192]/"
            "bestvideo[height=1080][vcodec^=h265][ext=mp4]+bestaudio[acodec=aac][ext=m4a][abr>=128][abr<=192]/"

            # YouTube format IDs for guaranteed H.264 + AAC
            "137+140/"  # 137=1080p H.264, 140=128k AAC

            # 720p H.264/H.265 fallback with AAC audio
            "bestvideo[height=720][vcodec^=avc1][ext=mp4]+bestaudio[acodec=aac][ext=m4a][abr>=128][abr<=192]/"
            "bestvideo[height=720][vcodec^=h264][ext=mp4]+bestaudio[acodec=aac][ext=m4a][abr>=128][abr<=192]/"
            "bestvideo[height=720][vcodec^=hvc1][ext=mp4]+bestaudio[acodec=aac][ext=m4a][abr>=128][abr<=192]/"
            "bestvideo[height=720][vcodec^=h265][ext=mp4]+bestaudio[acodec=aac][ext=m4a][abr>=128][abr<=192]/"

            # Strict exclusion of problematic codecs
            "bestvideo[height=1080][ext=mp4][vcodec!*=vp9][vcodec!*=vp8][vcodec!*=av01][vcodec!*=av1]+bestaudio[acodec=aac][ext=m4a]/"
            "bestvideo[height=720][ext=mp4][vcodec!*=vp9][vcodec!*=vp8][vcodec!*=av01][vcodec!*=av1]+bestaudio[acodec=aac][ext=m4a]/"

            # Final fallback - any compatible format
            "best[height>=720][ext=mp4][vcodec!*=vp9][vcodec!*=vp8][vcodec!*=av01][vcodec!*=av1]"
        )

    def _validate_downloaded_codec(self, video_path: str) -> bool:
        """
        Validate that the downloaded video uses GPU-compatible codec.

        Args:
            video_path: Path to the downloaded video file

        Returns:
            True if codec is GPU-compatible, False otherwise
        """
        try:
            # Use ffprobe to check video codec
            cmd = [
                'ffprobe',
                '-v', 'error',
                '-select_streams', 'v:0',
                '-show_entries', 'stream=codec_name',
                '-of', 'csv=p=0',
                video_path
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                codec = result.stdout.strip().lower()

                # Check if codec is GPU-compatible
                gpu_compatible_codecs = ['h264', 'avc1', 'h265', 'hevc', 'hvc1']
                problematic_codecs = ['vp9', 'vp09', 'av01', 'av1', 'vp8']

                is_compatible = any(compat in codec for compat in gpu_compatible_codecs)
                is_problematic = any(prob in codec for prob in problematic_codecs)

                if is_problematic:
                    logger.warning(f"⚠️ Downloaded video uses problematic codec: {codec} (may cause GPU processing issues)")
                    return False
                elif is_compatible:
                    logger.info(f"✅ Downloaded video uses GPU-compatible codec: {codec}")
                    return True
                else:
                    logger.warning(f"⚠️ Downloaded video uses unknown codec: {codec}")
                    return False
            else:
                logger.warning(f"Failed to detect video codec: {result.stderr}")
                return False

        except Exception as e:
            logger.warning(f"Error validating video codec: {e}")
            return False

    def cleanup(self) -> None:
        """Clean up downloaded files."""
        if self.original_video_path and os.path.exists(self.original_video_path):
            try:
                # Try multiple times with a delay to handle file being used by another process
                max_attempts = 3
                for attempt in range(max_attempts):
                    try:
                        os.remove(self.original_video_path)
                        logger.info(f"Cleaned up original video: {self.original_video_path}")
                        break
                    except OSError as e:
                        if attempt < max_attempts - 1:
                            logger.debug(f"Cleanup attempt {attempt+1} failed, retrying in 2 seconds: {e}")
                            time.sleep(2)  # Wait 2 seconds before retrying
                        else:
                            # Last attempt failed
                            logger.warning(f"Failed to clean up original video after {max_attempts} attempts: {e}")
            except Exception as e:
                logger.warning(f"Failed to clean up original video: {e}")

    def _try_bypass_download_1(self, output_file: str, progress_callback: Optional[Callable[[float], None]] = None) -> Optional[str]:
        """
        Try to download using bypass method 1 - Different user agent and headers
        """
        try:
            logger.info(f"Attempting bypass method 1 for: {self.url}")

            # Use different user agent to bypass detection
            bypass_options = {
                'format': self._get_gpu_optimized_format(),
                'outtmpl': output_file,
                'merge_output_format': 'mp4',
                'quiet': True,
                'no_warnings': True,
                'no_color': True,
                'retries': 3,
                'fragment_retries': 3,
                'extractor_retries': 3,
                'http_headers': {
                    'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'DNT': '1',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                    'Sec-Fetch-Dest': 'document',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-Site': 'none',
                    'Sec-Fetch-User': '?1'
                },
                'sleep_interval': 1,
                'max_sleep_interval': 3,
                'sleep_interval_requests': 0.5
            }

            # Add cookies if available
            if self.cookies_file and os.path.exists(self.cookies_file):
                bypass_options['cookiefile'] = self.cookies_file

            with yt_dlp.YoutubeDL(bypass_options) as ydl:
                ydl.download([self.url])

            if os.path.exists(output_file) and os.path.getsize(output_file) > 1024 * 1024:
                logger.info(f"Bypass method 1 successful: {output_file}")
                return os.path.abspath(output_file)

        except Exception as e:
            logger.warning(f"Bypass method 1 failed: {e}")

        return None

    def _try_bypass_download_2(self, output_file: str, progress_callback: Optional[Callable[[float], None]] = None) -> Optional[str]:
        """
        Try to download using bypass method 2 - Mobile user agent and different approach
        """
        try:
            logger.info(f"Attempting bypass method 2 for: {self.url}")

            # Use mobile user agent to bypass detection
            mobile_options = {
                'format': 'best[height<=720]/best',  # Lower quality to avoid detection
                'outtmpl': output_file,
                'merge_output_format': 'mp4',
                'quiet': True,
                'no_warnings': True,
                'no_color': True,
                'retries': 2,
                'fragment_retries': 2,
                'extractor_retries': 2,
                'http_headers': {
                    'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Connection': 'keep-alive'
                },
                'extractor_args': {
                    'youtube': {
                        'player_client': ['android', 'web'],
                        'player_skip': ['webpage', 'configs'],
                        'skip': ['hls', 'dash']
                    }
                },
                'sleep_interval': 2,
                'max_sleep_interval': 5
            }

            # Add cookies if available
            if self.cookies_file and os.path.exists(self.cookies_file):
                mobile_options['cookiefile'] = self.cookies_file

            with yt_dlp.YoutubeDL(mobile_options) as ydl:
                ydl.download([self.url])

            if os.path.exists(output_file) and os.path.getsize(output_file) > 1024 * 1024:
                logger.info(f"Bypass method 2 successful: {output_file}")
                return os.path.abspath(output_file)

        except Exception as e:
            logger.warning(f"Bypass method 2 failed: {e}")

        return None

    def __del__(self):
        """Destructor to ensure cleanup."""
        try:
            self.cleanup()
        except Exception as e:
            logger.warning(f"Error during cleanup in destructor: {e}")
