#!/usr/bin/env python
"""
Script để chạy chương trình bằng cách nhấp đúp
"""
import os
import sys
import time
import logging
import platform
import argparse

# Kiểm tra phiên bản Python
if sys.version_info < (3, 7):
    print("❌ Lỗi: Chương trình yêu cầu Python 3.7 trở lên")
    print(f"Phiên bản hiện tại: {platform.python_version()}")
    input("Nhấn Enter để thoát...")
    sys.exit(1)

# Chuyển hướng stderr vào file log.txt
log_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "log.txt")
try:
    sys.stderr = open(log_file, 'w', encoding='utf-8')
except Exception as e:
    print(f"⚠️ Không thể mở file log: {str(e)}")
    print("Tiếp tục mà không ghi log...")

# Thêm thư mục hiện tại vào sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import các module cần thiết
try:
    from src.config.settings import LOGGING
    from src.utils.helpers import setup_logging
    from src.main import main
except ImportError as e:
    print(f"❌ Lỗi khi import module: {str(e)}")
    print("Vui lòng đảm bảo đã cài đặt đầy đủ các thư viện cần thiết")
    input("Nhấn Enter để thoát...")
    sys.exit(1)

# Thiết lập logging
try:
    setup_logging(
        LOGGING.get('FILE', 'log.txt'),
        LOGGING.get('LEVEL', 'DEBUG'),
        LOGGING.get('FORMAT', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
        LOGGING.get('CONSOLE_LEVEL', 'ERROR')
    )
except Exception as e:
    print(f"⚠️ Không thể thiết lập logging: {str(e)}")
    print("Tiếp tục mà không có logging...")

def check_required_files(cookies_file, channels_file):
    """Kiểm tra các file cần thiết cho chương trình"""
    # Lấy thư mục hiện tại
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # Đường dẫn đầy đủ đến các file
    cookies_path = os.path.join(current_dir, cookies_file)
    channels_path = os.path.join(current_dir, channels_file)

    # Kiểm tra file channel_list.txt
    if not os.path.exists(channels_path):
        print(f"\n❌ Không tìm thấy file {channels_file}")
        print(f"Vui lòng tạo file {channels_file} và thêm đường dẫn đến các file videos.txt (mỗi đường dẫn một dòng)")
        input("Nhấn Enter để thoát...")
        sys.exit(1)

    # Kiểm tra file cookies.txt
    if not os.path.exists(cookies_path):
        print(f"\n⚠️ Không tìm thấy file {cookies_file}")
        print("Chương trình vẫn chạy nhưng có thể không tải được một số video yêu cầu đăng nhập")

    return channels_path, cookies_path

def parse_arguments():
    """Phân tích tham số dòng lệnh"""
    parser = argparse.ArgumentParser(description="Tải và xử lý video từ YouTube")

    parser.add_argument("--cookies", "-c", default="cookies.txt",
                        help="Đường dẫn đến file cookies.txt (mặc định: cookies.txt)")

    parser.add_argument("--channels", "-ch", default="channel_list.txt",
                        help="Đường dẫn đến file channel_list.txt (mặc định: channel_list.txt)")

    parser.add_argument("--output", "-o", default="output",
                        help="Thư mục đầu ra cho video đã xử lý (mặc định: output)")

    parser.add_argument("--no-advanced", action="store_true",
                        help="Tắt chế độ xử lý nâng cao")

    parser.add_argument("--no-gpu", action="store_true",
                        help="Tắt sử dụng GPU")

    parser.add_argument("--debug", action="store_true",
                        help="Bật chế độ debug")

    return parser.parse_args()

if __name__ == "__main__":
    try:
        # Phân tích tham số dòng lệnh
        args = parse_arguments()

        # Khởi tạo các biến
        cookies_file = args.cookies
        channels_file = args.channels

        # Kiểm tra các file cần thiết và khởi tạo
        channels_path, cookies_path = check_required_files(cookies_file, channels_file)

        # Thiết lập các tham số dòng lệnh cho main()
        sys_args = ['run.py', '--channel-list', channels_path, '--cookies-file', cookies_path]

        # Thêm tham số nâng cao nếu không bị tắt
        if not args.no_advanced:
            sys_args.append('--advanced')

        # Thêm tham số tắt GPU nếu được yêu cầu
        if args.no_gpu:
            sys_args.append('--no-gpu')

        # Thêm tham số debug nếu được yêu cầu
        if args.debug:
            sys_args.append('--verbose')

        # Thiết lập tham số dòng lệnh
        sys.argv = sys_args

        # Hiển thị banner và thông tin phiên bản
        start_time = time.time()
        main()
        elapsed_time = time.time() - start_time
        print(f"\n✅ Hoàn thành trong {elapsed_time:.2f} giây")

    except KeyboardInterrupt:
        print("\n⚠️ Đã hủy bởi người dùng")
        sys.exit(1)
    except Exception as e:
        logging.error(f"❌ Lỗi không mong muốn: {str(e)}")
        print(f"\n❌ Lỗi không mong muốn: {str(e)}")
        # In thông tin chi tiết về lỗi
        import traceback
        traceback.print_exc()
        input("Nhấn Enter để thoát...")
        sys.exit(1)