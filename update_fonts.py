"""
Script để cập nhật cấu hình font để hỗ trợ tốt hơn cho ký tự Cyrillic
"""
import os
import re
import shutil
import sys

def update_settings_file():
    """Cập nhật file settings.py để thay đổi thứ tự ưu tiên font"""
    settings_path = os.path.join('src', 'config', 'settings.py')
    
    if not os.path.exists(settings_path):
        print(f"❌ Không tìm thấy file {settings_path}")
        return False
    
    # Tạo bản sao lưu
    backup_path = settings_path + '.bak'
    shutil.copy2(settings_path, backup_path)
    print(f"✅ Đã tạo bản sao lưu tại {backup_path}")
    
    with open(settings_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Tìm phần cấu hình font
    font_config_pattern = r'# Cấu hình font chữ\s*FONT_PATHS\s*=\s*\[(.*?)\]'
    font_config_match = re.search(font_config_pattern, content, re.DOTALL)
    
    if not font_config_match:
        print("❌ Không tìm thấy cấu hình font trong file settings.py")
        return False
    
    # Cấu hình font mới - ưu tiên các font hỗ trợ tốt cho Cyrillic
    new_font_config = """# Cấu hình font chữ - Đã cập nhật để hỗ trợ tốt hơn cho ký tự Cyrillic (tiếng Nga)
FONT_PATHS = [
    "C:/Windows/Fonts/arial.ttf",       # Arial Regular - hỗ trợ tốt cho Cyrillic
    "C:/Windows/Fonts/arialbd.ttf",     # Arial Bold - hỗ trợ tốt cho Cyrillic
    "C:/Windows/Fonts/tahoma.ttf",      # Tahoma Regular - hỗ trợ tốt cho Cyrillic
    "C:/Windows/Fonts/tahomabd.ttf",    # Tahoma Bold - hỗ trợ tốt cho Cyrillic
    "C:/Windows/Fonts/segoeui.ttf",     # Segoe UI - hỗ trợ tốt cho Cyrillic
    "C:/Windows/Fonts/segoeuib.ttf",    # Segoe UI Bold - hỗ trợ tốt cho Cyrillic
    "C:/Windows/Fonts/times.ttf",       # Times New Roman - hỗ trợ tốt cho Cyrillic
    "C:/Windows/Fonts/timesbd.ttf",     # Times New Roman Bold - hỗ trợ tốt cho Cyrillic
    "C:/Windows/Fonts/calibri.ttf",     # Calibri Regular - hỗ trợ tốt cho Cyrillic
    "C:/Windows/Fonts/calibrib.ttf",    # Calibri Bold - hỗ trợ tốt cho Cyrillic
    "C:/Windows/Fonts/verdana.ttf",     # Verdana - hỗ trợ tốt cho Cyrillic
    "C:/Windows/Fonts/verdanab.ttf",    # Verdana Bold - hỗ trợ tốt cho Cyrillic
    "C:/Windows/Fonts/impact.ttf",      # Impact - có thể không hỗ trợ đầy đủ Cyrillic
    "C:/Windows/Fonts/ariblk.ttf"       # Arial Black - có thể không hỗ trợ đầy đủ Cyrillic
]"""
    
    # Thay thế cấu hình font cũ bằng cấu hình mới
    updated_content = re.sub(font_config_pattern, new_font_config, content, flags=re.DOTALL)
    
    with open(settings_path, 'w', encoding='utf-8') as f:
        f.write(updated_content)
    
    print("✅ Đã cập nhật cấu hình font trong file settings.py")
    return True

def create_cyrillic_fix_module():
    """Tạo module hỗ trợ hiển thị ký tự Cyrillic"""
    module_path = os.path.join('src', 'config', 'cyrillic_fix.py')
    
    content = """\"\"\"
Module hỗ trợ hiển thị ký tự Cyrillic (tiếng Nga) trong video
\"\"\"
import os
import re
import logging
from typing import Optional

def get_font_param(font_path: Optional[str]) -> str:
    \"\"\"
    Tạo tham số font cho filter drawtext của FFmpeg
    Sử dụng fontfile thay vì font để hỗ trợ tốt hơn cho các ký tự Cyrillic
    
    Args:
        font_path: Đường dẫn đến font
        
    Returns:
        Chuỗi tham số font cho filter drawtext
    \"\"\"
    if font_path and os.path.exists(font_path):
        # Sử dụng fontfile thay vì font để hỗ trợ tốt hơn cho các ký tự Cyrillic
        # Đường dẫn đầy đủ đến font sẽ giúp ffmpeg tìm đúng font và hiển thị đúng các ký tự đặc biệt
        return f":fontfile='{font_path}'"
    else:
        return ""

def clean_text_for_display(text: str) -> str:
    \"\"\"
    Làm sạch text để hiển thị trong video, giữ lại các ký tự Cyrillic
    
    Args:
        text: Văn bản cần làm sạch
        
    Returns:
        Văn bản đã làm sạch
    \"\"\"
    if not text:
        return ""
        
    # Loại bỏ các ký tự có thể gây lỗi trong filter FFmpeg
    # Giữ lại các ký tự Cyrillic (U+0400 đến U+04FF)
    # Giữ lại các ký tự Latin, số, khoảng trắng và một số ký tự đặc biệt phổ biến
    
    # Thay thế dấu nháy đơn và nháy kép để tránh lỗi với FFmpeg
    text = text.replace("'", "").replace('"', "").replace('\\\\', "")
    
    # Loại bỏ khoảng trắng thừa
    text = re.sub(r'\\s+', ' ', text).strip()
    
    return text
"""
    
    # Tạo thư mục nếu chưa tồn tại
    os.makedirs(os.path.dirname(module_path), exist_ok=True)
    
    with open(module_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ Đã tạo module hỗ trợ Cyrillic tại {module_path}")
    return True

def update_helpers_file():
    """Cập nhật file helpers.py để thay đổi thứ tự ưu tiên font trong fallback_fonts"""
    helpers_path = os.path.join('src', 'utils', 'helpers.py')
    
    if not os.path.exists(helpers_path):
        print(f"❌ Không tìm thấy file {helpers_path}")
        return False
    
    # Tạo bản sao lưu
    backup_path = helpers_path + '.bak'
    shutil.copy2(helpers_path, backup_path)
    print(f"✅ Đã tạo bản sao lưu tại {backup_path}")
    
    with open(helpers_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Tìm phần fallback_fonts
    fallback_pattern = r'fallback_fonts\s*=\s*\[(.*?)\]'
    fallback_match = re.search(fallback_pattern, content, re.DOTALL)
    
    if not fallback_match:
        print("❌ Không tìm thấy cấu hình fallback_fonts trong file helpers.py")
        return False
    
    # Cấu hình fallback_fonts mới - ưu tiên các font hỗ trợ tốt cho Cyrillic
    new_fallback = """    # Nếu không tìm thấy font nào trong danh sách, thử tìm các font phổ biến khác có hỗ trợ Cyrillic
    fallback_fonts = [
        "C:/Windows/Fonts/arial.ttf",       # Arial - hỗ trợ tốt cho Cyrillic
        "C:/Windows/Fonts/tahoma.ttf",      # Tahoma - hỗ trợ tốt cho Cyrillic
        "C:/Windows/Fonts/segoeui.ttf",     # Segoe UI - hỗ trợ tốt cho Cyrillic
        "C:/Windows/Fonts/times.ttf",       # Times New Roman - hỗ trợ tốt cho Cyrillic
        "C:/Windows/Fonts/arialbd.ttf",     # Arial Bold - hỗ trợ tốt cho Cyrillic
        "C:/Windows/Fonts/tahomabd.ttf",    # Tahoma Bold - hỗ trợ tốt cho Cyrillic
        "C:/Windows/Fonts/impact.ttf",      # Impact - có thể không hỗ trợ đầy đủ Cyrillic
        "C:/Windows/Fonts/ariblk.ttf",      # Arial Black - có thể không hỗ trợ đầy đủ Cyrillic
    ]"""
    
    # Thay thế cấu hình fallback_fonts cũ bằng cấu hình mới
    updated_content = re.sub(fallback_pattern, new_fallback, content, flags=re.DOTALL)
    
    with open(helpers_path, 'w', encoding='utf-8') as f:
        f.write(updated_content)
    
    print("✅ Đã cập nhật cấu hình fallback_fonts trong file helpers.py")
    return True

def main():
    """Hàm chính để cập nhật cấu hình font"""
    print("🔤 Bắt đầu cập nhật cấu hình font để hỗ trợ ký tự Cyrillic...")
    
    # Cập nhật file settings.py
    update_settings_file()
    
    # Tạo module hỗ trợ Cyrillic
    create_cyrillic_fix_module()
    
    # Cập nhật file helpers.py
    update_helpers_file()
    
    print("✅ Đã hoàn thành cập nhật cấu hình font!")
    print("🔄 Vui lòng chạy lại chương trình để áp dụng các thay đổi.")

if __name__ == "__main__":
    main()
