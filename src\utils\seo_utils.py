"""
Module xử lý SEO và keywords - phiên bản tối ưu

Cải tiến:
- X<PERSON> lý tốt hơn các ký tự Unicode và đa ngôn ngữ
- Tối ưu hóa trích xuất từ khóa cho SEO
- Hỗ trợ tạo hashtag tối ưu cho TikTok, Instagram, YouTube Shorts
- Phân tích ngữ nghĩa cơ bản để chọn từ khóa có giá trị
"""
import re
import random
from typing import List
import unicodedata
import logging

# Danh sách stop words mở rộng cho nhiều ngôn ngữ
STOP_WORDS = {
    # Tiếng Anh
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'as',
    'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
    'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'my', 'your',
    'his', 'her', 'its', 'our', 'their', 'me', 'him', 'us', 'them', 'what', 'who', 'which', 'whom',
    'whose', 'when', 'where', 'why', 'how', 'all', 'any', 'both', 'each', 'few', 'more', 'most',
    'other', 'some', 'such', 'no', 'nor', 'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very',
    'can', 'will', 'just', 'should', 'now', 'part', 'one', 'two', 'three', 'four', 'five',

    # Các ký hiệu phổ biến
    '|', '-', '_', '&', '+', '=', '@', '#', '$', '%', '^', '*', '(', ')', '[', ']', '{', '}',

    # Các từ không có giá trị SEO
    'video', 'watch', 'subscribe', 'channel', 'like', 'comment', 'share', 'follow',
    'youtube', 'youtu', 'http', 'https', 'www', 'com', 'net', 'org'
}

# Từ khóa chung có giá trị cao cho SEO
HIGH_VALUE_KEYWORDS = [
    'tutorial', 'howto', 'guide', 'tips', 'tricks', 'review', 'best', 'top', 'new', 'latest',
    'amazing', 'awesome', 'incredible', 'ultimate', 'essential', 'must', 'perfect', 'easy',
    'simple', 'quick', 'fast', 'professional', 'expert', 'master', 'complete', 'full'
]

# Từ khóa theo chủ đề
TOPIC_KEYWORDS = {
    'tech': ['technology', 'gadget', 'smartphone', 'laptop', 'computer', 'software', 'hardware', 'app'],
    'food': ['recipe', 'cooking', 'baking', 'delicious', 'tasty', 'yummy', 'homemade', 'healthy'],
    'travel': ['destination', 'adventure', 'explore', 'journey', 'vacation', 'trip', 'tour', 'visit'],
    'beauty': ['makeup', 'skincare', 'haircare', 'cosmetics', 'beauty', 'fashion', 'style', 'trend'],
    'fitness': ['workout', 'exercise', 'fitness', 'gym', 'training', 'muscle', 'weight', 'health'],
    'gaming': ['gameplay', 'playthrough', 'walkthrough', 'strategy', 'tips', 'tricks', 'cheat', 'mod']
}

def clean_text(text: str) -> str:
    """Làm sạch text, thay _ bằng dấu cách và chuẩn hóa"""
    try:
        # Thay _ bằng dấu cách
        text = text.replace('_', ' ')

        # Chuyển về dạng NFKD để tách dấu
        text = unicodedata.normalize('NFKD', text)

        # Chuyển các ký tự không phải ASCII thành ASCII gần nhất
        # Nhưng giữ lại các từ có nghĩa
        words = text.split()
        cleaned_words = []

        for word in words:
            # Nếu từ chỉ chứa chữ cái và số, giữ nguyên
            if word.replace('-', '').isalnum():
                cleaned_words.append(word)
            else:
                # Chuyển về ASCII và loại bỏ ký tự đặc biệt
                ascii_word = word.encode('ascii', 'ignore').decode()
                if ascii_word:
                    cleaned_words.append(ascii_word)

        # Nối các từ lại với nhau
        text = ' '.join(cleaned_words)

        # Loại bỏ các ký tự đặc biệt còn lại
        text = re.sub(r'[^\w\s-]', '', text)

        # Loại bỏ khoảng trắng thừa
        text = ' '.join(text.split())

        # Viết hoa chữ cái đầu mỗi từ cho tiêu đề
        text = text.title()

        # Nếu text rỗng hoặc chỉ có số, trả về "Video"
        if not text or text.isdigit():
            return "Video"

        return text

    except Exception as e:
        logging.error(f"Lỗi khi làm sạch text: {str(e)}")
        return "Video"

def extract_keywords(title: str, max_keywords: int = 6) -> List[str]:
    """
    Trích xuất keywords từ tiêu đề video - phiên bản tối ưu

    Args:
        title: Tiêu đề video
        max_keywords: Số lượng keywords tối đa (mặc định: 6)

    Returns:
        List[str]: Danh sách keywords phù hợp với SEO TikTok
    """
    # Làm sạch tiêu đề
    clean_title = clean_text(title)

    # Tách thành các từ
    words = clean_title.split()

    # Loại bỏ các từ stop words và từ quá ngắn
    words = [w for w in words if w.lower() not in STOP_WORDS and len(w) >= 3]

    # Tạo các từ đơn có ý nghĩa
    single_words = []
    for w in words:
        # Chuyển về lowercase để so sánh
        w_lower = w.lower()
        # Kiểm tra xem từ có phải là số không
        if not w_lower.isdigit() and 3 <= len(w_lower) <= 15:
            # Ưu tiên từ khóa có giá trị cao
            if w_lower in HIGH_VALUE_KEYWORDS:
                # Thêm 2 lần để tăng khả năng được chọn
                single_words.append(w)
                single_words.append(w)
            else:
                single_words.append(w)

    # Tạo các cụm từ có ý nghĩa (2-3 từ)
    phrases = []
    for i in range(len(words)):
        # Cụm 2 từ
        if i < len(words) - 1:
            phrase = f"{words[i]}{words[i+1]}"
            if 5 <= len(phrase) <= 20:
                phrases.append(phrase)
        # Cụm 3 từ
        if i < len(words) - 2:
            phrase = f"{words[i]}{words[i+1]}{words[i+2]}"
            if 5 <= len(phrase) <= 25:
                phrases.append(phrase)

    # Kết hợp từ đơn và cụm từ, ưu tiên từ đơn
    all_keywords = single_words + phrases

    # Loại bỏ trùng lặp và sắp xếp theo độ dài
    unique_keywords = []
    seen = set()
    for kw in all_keywords:
        kw_lower = kw.lower()
        if kw_lower not in seen and kw_lower not in STOP_WORDS:
            seen.add(kw_lower)
            unique_keywords.append(kw)

    # Sắp xếp theo độ dài và chọn số lượng keywords phù hợp
    sorted_keywords = sorted(unique_keywords, key=len)

    # Đảm bảo có ít nhất một từ khóa có giá trị cao nếu có thể
    final_keywords = []
    high_value_found = False

    for kw in sorted_keywords:
        if len(final_keywords) >= max_keywords:
            break

        # Kiểm tra xem từ khóa có phải là từ khóa có giá trị cao không
        if not high_value_found and kw.lower() in HIGH_VALUE_KEYWORDS:
            high_value_found = True

        final_keywords.append(kw)

    # Nếu không tìm thấy từ khóa có giá trị cao, thêm một từ khóa ngẫu nhiên
    if not high_value_found and len(final_keywords) < max_keywords and HIGH_VALUE_KEYWORDS:
        random_high_value = random.choice(HIGH_VALUE_KEYWORDS)
        final_keywords.append(random_high_value.title())

    return final_keywords

def optimize_hashtags(title: str, tags: List[str] = None, max_hashtags: int = 5) -> str:
    """
    Tối ưu hashtag từ tiêu đề và tags

    Args:
        title: Tiêu đề video
        tags: Danh sách tags từ YouTube (nếu có)
        max_hashtags: Số lượng hashtag tối đa

    Returns:
        str: Chuỗi hashtag tối ưu cho SEO
    """
    # Trích xuất từ khóa từ tiêu đề
    title_keywords = extract_keywords(title, max_keywords=max_hashtags*2)

    # Kết hợp với tags từ YouTube nếu có
    all_tags = []

    # Thêm từ khóa từ tiêu đề
    all_tags.extend(title_keywords)

    # Thêm tags từ YouTube nếu có
    if tags:
        # Làm sạch tags
        cleaned_tags = []
        for tag in tags:
            # Bỏ qua các tag quá ngắn hoặc quá dài
            if 3 <= len(tag) <= 20 and tag.lower() not in STOP_WORDS:
                # Loại bỏ khoảng trắng và ký tự đặc biệt
                cleaned_tag = re.sub(r'[^\w]', '', tag)
                if cleaned_tag:
                    # Ưu tiên các tag từ YouTube (thêm 2 lần)
                    cleaned_tags.append(cleaned_tag)
                    if cleaned_tag.lower() in HIGH_VALUE_KEYWORDS:
                        # Ưu tiên gấp đôi cho từ khóa có giá trị cao
                        cleaned_tags.append(cleaned_tag)

        all_tags.extend(cleaned_tags)

    # Loại bỏ trùng lặp
    unique_tags = []
    seen = set()

    for tag in all_tags:
        tag_lower = tag.lower()
        if tag_lower not in seen and tag_lower not in STOP_WORDS:
            # Loại bỏ các tag không mong muốn
            if tag_lower not in ['shorts', 'vertical', 'tiktok', 'reels', 'fyp']:
                seen.add(tag_lower)
                unique_tags.append(tag)

    # Chọn ngẫu nhiên các tag để tránh trùng lặp giữa các video
    if len(unique_tags) > max_hashtags:
        # Đảm bảo có ít nhất một từ khóa có giá trị cao
        high_value_tags = [tag for tag in unique_tags if tag.lower() in HIGH_VALUE_KEYWORDS]
        other_tags = [tag for tag in unique_tags if tag.lower() not in HIGH_VALUE_KEYWORDS]

        selected_tags = []

        # Chọn một từ khóa có giá trị cao nếu có
        if high_value_tags:
            selected_tags.append(random.choice(high_value_tags))

        # Chọn ngẫu nhiên các tag còn lại
        remaining_slots = max_hashtags - len(selected_tags)
        if remaining_slots > 0 and other_tags:
            # Đảm bảo không chọn quá số lượng tag có sẵn
            num_to_select = min(remaining_slots, len(other_tags))
            selected_tags.extend(random.sample(other_tags, num_to_select))

        unique_tags = selected_tags

    # Định dạng hashtag
    hashtags = []
    for tag in unique_tags[:max_hashtags]:
        # Loại bỏ khoảng trắng và ký tự đặc biệt
        clean_tag = re.sub(r'[^\w]', '', tag)
        if clean_tag:
            hashtags.append('#' + clean_tag)

    # Trả về chuỗi hashtag
    return ' '.join(hashtags)