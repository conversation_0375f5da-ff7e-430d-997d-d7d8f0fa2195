"""
Batch processor module for processing multiple videos from videos.txt files.
"""

import os
import sys
import time
import logging
import re
import unicodedata
from pathlib import Path
from typing import List, Optional, Dict, Any, Tuple
from concurrent.futures import ThreadPoolExecutor

from rich.console import Console
from rich.progress import Progress, TextColumn, BarColumn, TimeElapsedColumn, TimeRemainingColumn
from rich.panel import Panel
from rich.text import Text

from src.core.downloader import VideoDownloader
from src.core.processor import VideoProcessor
from src.core.cache import Cache
from src.core.exceptions import VideoProcessor<PERSON>rror, DownloadError, ProcessingError

console = Console()
logger = logging.getLogger("video_processor.batch")

def sanitize_path(path: str) -> str:
    """
    Sanitize a path to remove special characters but preserve Unicode and drive letter.

    Args:
        path: The path to sanitize.

    Returns:
        Sanitized path.
    """
    try:
        # Extract drive letter (if any)
        drive = ""
        if len(path) >= 2 and path[1] == ':':
            drive = path[:2]
            path = path[2:]

        # Normalize Unicode
        path = unicodedata.normalize('NFC', path)

        # Remove invalid characters in Windows filenames
        # Keep valid Unicode characters
        path = re.sub(r'[<>:"|?*]', '_', path)

        # Rejoin with drive letter
        return drive + path
    except Exception as e:
        logger.error(f"❌ Error sanitizing path: {str(e)}")
        return path

def normalize_path(path: str) -> str:
    """
    Normalize a path to handle Unicode and special characters.

    Args:
        path: The path to normalize.

    Returns:
        Normalized path.
    """
    try:
        # Remove quotes from path
        path = path.strip('"\'')

        # Sanitize path
        path = sanitize_path(path)

        # Normalize slashes
        path = path.replace('/', '\\')

        return path
    except Exception as e:
        logger.error(f"❌ Error normalizing path: {str(e)}")
        return path

def count_mp4_files(directory: str) -> int:
    """
    Count the number of MP4 files in a directory.

    Args:
        directory: Directory to count MP4 files in.

    Returns:
        Number of MP4 files.
    """
    try:
        if not os.path.exists(directory):
            return 0

        count = 0
        for file in os.listdir(directory):
            if file.lower().endswith('.mp4'):
                count += 1
        return count
    except Exception as e:
        logger.error(f"❌ Error counting MP4 files: {str(e)}")
        return 0

def process_single_url(url: str, output_dir: str, config: Dict[str, Any], cache: Cache, videos_txt_path: str = None) -> bool:
    """
    Process a single YouTube URL with smart retry mechanism.

    Args:
        url: YouTube URL to process.
        output_dir: Directory to save processed video.
        config: Configuration dictionary.
        cache: Cache instance.
        videos_txt_path: Path to the videos.txt file (for saving completed URLs).

    Returns:
        True if processing was successful, False otherwise.
    """
    # Improve error handling and resilience
    retry_count = 0
    max_retries = 3
    backoff_time = 2  # Initial wait time (seconds)

    while retry_count <= max_retries:
        try:
            # Create output directory if it doesn't exist
            os.makedirs(output_dir, exist_ok=True)

            # Create VideoDownloader
            downloader = VideoDownloader(url, config, cache)

            # Check if URL has been successfully processed before
            completed_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "completed_urls.txt")
            if os.path.exists(completed_file):
                with open(completed_file, 'r', encoding='utf-8') as f:
                    completed_urls = [line.strip() for line in f if line.strip()]
                    if url in completed_urls:
                        # Không hiển thị thông báo bỏ qua URL
                        return True

            # Get video info
            video_info = downloader.get_info()

            # Download video
            downloaded_path = downloader.download(output_dir)

            # Check if download failed
            if not downloaded_path or not os.path.exists(downloaded_path):
                raise DownloadError(f"Failed to download video: {url}")

            # Process video
            try:
                processor = VideoProcessor(downloaded_path, video_info, config)
                output_path = processor.process(output_dir)
                console.print("[green]Video đã được xử lý thành công.[/green]")
            except Exception as e:
                # If processing fails, use original video
                console.print(f"[yellow]Lỗi khi xử lý video: {str(e)}[/yellow]")
                console.print("[green]Sử dụng video gốc làm đầu ra.[/green]")
                output_path = downloaded_path

            # Clean up original video
            downloader.cleanup()
            console.print("[green]Đã xóa video gốc[/green]")

            # Mark URL as completed
            try:
                if videos_txt_path:
                    # Get the directory of the videos.txt file
                    videos_txt_dir = os.path.dirname(videos_txt_path)
                    # Create completed_urls.txt in the same directory as videos.txt
                    completed_file = os.path.join(videos_txt_dir, "completed_urls.txt")

                    # Immediately remove URL from videos.txt
                    try:
                        # Read all URLs from videos.txt
                        with open(videos_txt_path, 'r', encoding='utf-8') as f:
                            all_urls = [line.strip() for line in f if line.strip()]

                        # Remove the processed URL
                        if url in all_urls:
                            all_urls.remove(url)

                            # Write back the remaining URLs
                            with open(videos_txt_path, 'w', encoding='utf-8') as f:
                                for remaining_url in all_urls:
                                    f.write(f"{remaining_url}\n")
                            console.print(f"✅ URL removed from videos.txt: {url}")
                    except Exception as e:
                        logger.warning(f"⚠️ Could not remove URL from videos.txt: {str(e)}")
                else:
                    # If no videos.txt path provided, save in the data directory
                    completed_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data", "completed_urls.txt")

                # Append URL to completed_urls.txt
                with open(completed_file, 'a', encoding='utf-8') as f:
                    f.write(f"{url}\n")
                console.print("[green]Đã di chuyển URL video sang completed_urls.txt[/green]")
            except Exception as e:
                logger.warning(f"⚠️ Could not mark URL as completed: {str(e)}")

            return True

        except Exception as e:
            retry_count += 1
            if retry_count <= max_retries:
                # Calculate wait time with exponential backoff
                wait_time = backoff_time * (2 ** (retry_count - 1))
                # Add jitter to avoid thundering herd
                import random
                wait_time *= random.uniform(0.8, 1.2)

                console.print(f"⚠️ Error: {str(e)}. Retrying {retry_count}/{max_retries} in {wait_time:.2f}s")
                time.sleep(wait_time)
            else:
                # Out of retries
                logger.error(f"❌ Error processing URL {url} after {max_retries} attempts: {str(e)}")

                # Try to clean up original video if it exists
                try:
                    if 'downloader' in locals() and hasattr(downloader, 'cleanup'):
                        downloader.cleanup()
                except Exception as cleanup_error:
                    logger.error(f"⚠️ Could not clean up original video: {str(cleanup_error)}")

                # Mark URL as failed
                try:
                    failed_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "failed_urls.txt")
                    with open(failed_file, 'a', encoding='utf-8') as f:
                        f.write(f"{url} # Error: {str(e)}\\n")
                except Exception as mark_error:
                    logger.error(f"❌ Could not mark URL as failed: {str(mark_error)}")

                return False

    # Should never reach here, but to satisfy type checking
    return False

def process_videos_txt(videos_txt_path: str, config: Dict[str, Any], cache: Cache) -> None:
    """
    Process a videos.txt file containing YouTube URLs.

    Args:
        videos_txt_path: Path to the videos.txt file.
        config: Configuration dictionary.
        cache: Cache instance.
    """
    # Check if file exists
    if not os.path.exists(videos_txt_path):
        logger.error(f"❌ videos.txt file not found: {videos_txt_path}")
        return

    try:
        # Read URLs from file
        with open(videos_txt_path, 'r', encoding='utf-8') as f:
            urls = [line.strip() for line in f if line.strip()]

        if not urls:
            console.print(f"⚠️ No URLs in file: {videos_txt_path}")
            return

        # Get output directory - use the parent directory of the parent directory of videos.txt
        # For example: F:\190 Inventor 101\new folder\videos.txt -> F:\190 Inventor 101
        parent_dir = os.path.dirname(videos_txt_path)
        if parent_dir:
            # Use the parent directory of the parent directory as output
            output_dir = sanitize_path(os.path.dirname(parent_dir))
        else:
            # If videos.txt is in the root directory, use "output" as output directory
            output_dir = sanitize_path(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data", "output"))

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        console.print(f"📂 Output directory: {output_dir}")

        # Check current video count
        current_video_count = count_mp4_files(output_dir)

        if current_video_count >= 50:
            console.print(f"⚠️ Reached limit of 50 videos, moving to next channel")
            return

        # Number of videos that can still be downloaded
        remaining_slots = 50 - current_video_count

        # Limit number of URLs to process
        total = min(len(urls), remaining_slots)
        console.print(f"📊 Video count: {current_video_count}/50 | Processing: {total}/{len(urls)} URLs")

        if total == 0:
            return

        # Improved memory management: Process in optimized batches
        batch_size = 10  # Increase number of URLs processed in a batch to 10
        total_batches = (total + batch_size - 1) // batch_size  # Round up

        # Chỉ hiển thị thông tin chính, không hiển thị thanh tiến trình
        console.print(f"[green]Bắt đầu xử lý {total} video...[/green]")

        # Initialize counters
        success = 0
        processed_urls = []

        # Start processing time
        start_time = time.time()

        for batch_idx in range(total_batches):
                # Calculate start and end indices for batch
                start_idx = batch_idx * batch_size
                end_idx = min(start_idx + batch_size, total)
                batch_urls = urls[start_idx:end_idx]

                console.print(f"🔄 Batch {batch_idx+1}/{total_batches}")

                # Check current video count (only check once before each batch)
                current_video_count = count_mp4_files(output_dir)
                if current_video_count >= 50:
                    console.print(f"\n⚠️ Reached limit of 50 videos, moving to next channel")
                    # Stop processing current batch and move to next channel immediately
                    break

                # Process each URL in batch
                for i, url in enumerate(batch_urls, start_idx + 1):
                    try:
                        # Check current video count after each URL to ensure stopping immediately when limit is reached
                        current_video_count = count_mp4_files(output_dir)
                        if current_video_count >= 50:
                            console.print(f"\n⚠️ Reached limit of 50 videos, moving to next channel")
                            # Stop processing current URL and move to next channel immediately
                            break

                        # Calculate elapsed time and estimated time remaining
                        elapsed_time = time.time() - start_time
                        if i > start_idx + 1:  # If at least 1 URL has been processed
                            avg_time_per_url = elapsed_time / (i - start_idx)
                            remaining_urls = total - i
                            estimated_time_left = avg_time_per_url * remaining_urls
                            # Convert to hours:minutes:seconds
                            hours, remainder = divmod(estimated_time_left, 3600)
                            minutes, seconds = divmod(remainder, 60)
                            time_left_str = f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
                        else:
                            time_left_str = "--:--:--"

                        # Hiển thị thông tin đơn giản
                        console.print(f"🔄 Đang xử lý video {i}/{total}")

                        if process_single_url(url, output_dir, config, cache, videos_txt_path=videos_txt_path):
                            success += 1
                            processed_urls.append(url)
                            console.print(f"[green]Xử lý xong video {i}/{total}[/green]")

                        # Hiển thị thông tin tiến trình đơn giản
                        console.print(f"[green]Tiến trình: {success}/{i} video thành công[/green]", end="\r")
                    except Exception as e:
                        logger.error(f"❌ Error processing URL {url}: {str(e)}")
                        # Mark URL as failed but don't remove from videos.txt
                        try:
                            failed_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "failed_urls.txt")
                            with open(failed_file, 'a', encoding='utf-8') as f:
                                f.write(f"{url} # Unexpected error: {str(e)}\\n")
                        except Exception as inner_e:
                            logger.error(f"❌ Could not mark URL as failed: {str(inner_e)}")

                        # Hiển thị thông tin tiến trình đơn giản
                        console.print(f"[green]Tiến trình: {success}/{i} video thành công[/green]", end="\r")

                # Free memory after each batch
                import gc
                gc.collect()

                # Pause for 1 second to allow system to read/write files and free resources
                time.sleep(1)

        # Hiển thị thông tin hoàn thành
        console.print(f"[bold green]Đã xử lý xong {success}/{total} video[/bold green]")

        # Check remaining URLs in videos.txt
        try:
            if os.path.exists(videos_txt_path):
                # Read all URLs in file
                with open(videos_txt_path, 'r', encoding='utf-8') as f:
                    remaining_urls = [line.strip() for line in f if line.strip()]

                # Report remaining URLs
                if remaining_urls:
                    console.print(f"\n⚠️ {len(remaining_urls)} URLs remain unprocessed in videos.txt")
                    console.print("📋 These will be kept for the next processing run")
                else:
                    console.print("\n✅ All URLs in videos.txt have been processed")
        except Exception as e:
            logger.error(f"❌ Error checking remaining URLs in videos.txt: {str(e)}")
    except Exception as e:
        logger.error(f"❌ Error processing videos.txt file: {str(e)}")

def process_channel_list(channel_list_path: str, config: Dict[str, Any], cache: Cache) -> Tuple[int, int]:
    """
    Process a channel_list.txt file containing paths to videos.txt files.

    Tối ưu hóa với:
    - Sắp xếp thông minh các kênh để xử lý hiệu quả
    - Cải thiện xử lý lỗi và khả năng phục hồi
    - Hiển thị thông tin chi tiết hơn về tiến trình
    - Quản lý tài nguyên tốt hơn

    Args:
        channel_list_path: Path to the channel_list.txt file.
        config: Configuration dictionary.
        cache: Cache instance.

    Returns:
        Tuple of (total_channels, success_channels).
    """
    # Giới hạn số luồng xử lý đồng thời (không hiển thị)

    # Không hiển thị thông tin hệ thống chi tiết

    # Check if file exists
    if not os.path.exists(channel_list_path):
        logger.error(f"❌ Channel list file not found: {channel_list_path}")
        return (0, 0)

    # Read paths to videos.txt files
    with open(channel_list_path, 'r', encoding='utf-8') as f:
        # Remove quotes from paths
        videos_txt_paths = [line.strip().strip('"\'') for line in f if line.strip()]

    # Phân tích và sắp xếp các kênh để xử lý hiệu quả
    channel_info = []

    for videos_txt in videos_txt_paths:
        try:
            # Normalize path
            normalized_path = normalize_path(videos_txt)

            # Kiểm tra file tồn tại
            if not Path(normalized_path).exists():
                channel_info.append({
                    'path': normalized_path,
                    'exists': False,
                    'url_count': 0,
                    'video_count': 0,
                    'priority': 0  # Ưu tiên thấp nhất
                })
                continue

            # Đọc số lượng URL trong file
            url_count = 0
            try:
                with open(normalized_path, 'r', encoding='utf-8') as f:
                    urls = [line.strip() for line in f if line.strip()]
                    url_count = len(urls)
            except Exception:
                url_count = 0

            # Xác định thư mục đầu ra
            parent_dir = os.path.dirname(normalized_path)
            if parent_dir:
                output_dir = sanitize_path(os.path.dirname(parent_dir))
            else:
                output_dir = sanitize_path(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data", "output"))

            # Đếm số lượng video hiện có
            video_count = count_mp4_files(output_dir)

            # Tính toán độ ưu tiên: ưu tiên các kênh có ít video nhưng nhiều URL
            remaining_slots = max(0, 50 - video_count)
            priority = url_count * (1 + remaining_slots/50) if remaining_slots > 0 else 0

            channel_info.append({
                'path': normalized_path,
                'exists': True,
                'url_count': url_count,
                'video_count': video_count,
                'output_dir': output_dir,
                'priority': priority
            })

        except Exception as e:
            logger.error(f"❌ Error analyzing channel {videos_txt}: {str(e)}")
            channel_info.append({
                'path': videos_txt,
                'exists': False,
                'url_count': 0,
                'video_count': 0,
                'priority': 0
            })

    # Sắp xếp kênh theo độ ưu tiên giảm dần
    channel_info.sort(key=lambda x: x['priority'], reverse=True)

    # Hiển thị thông tin tổng quan đơn giản
    valid_channels = sum(1 for c in channel_info if c['exists'])
    total_channels = len(channel_info)
    total_urls = sum(c['url_count'] for c in channel_info)

    console.print(f"[green]Tổng số kênh: {total_channels} (hợp lệ: {valid_channels})[/green]")
    console.print(f"[green]Tổng số URL: {total_urls}[/green]")

    # Xử lý từng kênh theo thứ tự ưu tiên
    success_channels = 0
    processed_urls = 0

    try:
        for i, channel in enumerate(channel_info, 1):
            try:
                # Hiển thị thông tin kênh
                if channel['exists']:
                    # Hiển thị thông tin kênh đơn giản
                    console.print(f"🔄 Kênh {i}/{total_channels}: {os.path.basename(os.path.dirname(channel['path']))}")

                    # Kiểm tra số lượng video
                    if channel['video_count'] >= 50:
                        console.print(f"⚠️ Kênh đã có 50 video, chuyển sang kênh tiếp theo")
                        success_channels += 1
                        continue

                    # Xử lý kênh
                    process_videos_txt(channel['path'], config, cache)
                    success_channels += 1

                    # Hiển thị thông báo xử lý xong kênh
                    console.print(f"[green]Xử lý xong kênh {os.path.basename(os.path.dirname(channel['path']))}, chuyển sang kênh tiếp theo[/green]")

                    # Cập nhật số lượng URL đã xử lý
                    new_url_count = 0
                    try:
                        with open(channel['path'], 'r', encoding='utf-8') as f:
                            new_url_count = len([line.strip() for line in f if line.strip()])
                        processed_urls += (channel['url_count'] - new_url_count)
                    except Exception:
                        pass

                    # Hiển thị tiến độ đơn giản
                    console.print(f"✅ Đã xử lý: {processed_urls}/{total_urls} URLs")

                    # Giải phóng bộ nhớ sau mỗi kênh
                    import gc
                    gc.collect()

                else:
                    error_msg = f"❌ File not found: {channel['path']}"
                    console.print(error_msg)
                    logger.error(error_msg)

                    # Simplified error message
                    parent_dir = os.path.dirname(channel['path'])
                    if not os.path.exists(parent_dir):
                        console.print(f"❌ Parent directory does not exist: {parent_dir}")

            except Exception as path_error:
                error_msg = f"❌ Error processing channel {channel['path']}: {str(path_error)}"
                console.print(error_msg)
                logger.error(error_msg)

    except Exception as e:
        logger.error(f"❌ Error during processing: {str(e)}")

    # Kiểm tra số lượng URL còn lại khi kết thúc
    total_remaining = 0
    for channel in channel_info:
        if channel['exists']:
            try:
                with open(channel['path'], 'r', encoding='utf-8') as f:
                    remaining_urls = [line.strip() for line in f if line.strip()]
                if remaining_urls:
                    total_remaining += len(remaining_urls)
            except Exception as e:
                logger.error(f"❌ Error checking {channel['path']}: {str(e)}")

    # Hiển thị thông tin tổng kết đơn giản

    if total_remaining > 0:
        console.print(f"⚠️ URL chưa xử lý: {total_remaining}")

    console.print(f"✅ Đã xử lý thành công {success_channels}/{total_channels} kênh")

    return (total_channels, success_channels)
