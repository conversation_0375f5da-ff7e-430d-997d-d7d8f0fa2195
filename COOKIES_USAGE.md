# Hướng dẫn sử dụng cookies để tải video YouTube

## Vấn đề "Sign in to confirm you're not a bot"

<PERSON>hi bạn thấy lỗi sau trong log:
```
ERROR: [youtube] XXX: Sign in to confirm you're not a bot. Use --cookies-from-browser or --cookies for the authentication.
```

YouTube đang yêu cầu xác thực để chứng minh bạn không phải là bot. Có hai cách chính để giải quyết vấn đề này:

## Cách 1: Sử dụng cookies từ trình duyệt đang mở

```bash
# Sử dụng cookies từ Chrome (mặc định)
python improved_youtube_downloader.py --use-browser-cookies --single-url URL_YOUTUBE

# Sử dụng cookies từ Firefox
python improved_youtube_downloader.py --use-browser-cookies --browser-type firefox --single-url URL_YOUTUBE

# Sử dụng cookies từ Edge
python improved_youtube_downloader.py --use-browser-cookies --browser-type edge --single-url URL_YOUTUBE
```

## Cách 2: Sử dụng file cookies.txt

### Bước 1: Xuất cookies từ trình duyệt

1. **Chrome/Edge**: Cài đặt tiện ích ["Get cookies.txt"](https://chrome.google.com/webstore/detail/get-cookiestxt/bgaddhkoddajcdgocldbbfleckgcbcid)
2. **Firefox**: Cài đặt tiện ích ["cookies.txt"](https://addons.mozilla.org/en-US/firefox/addon/cookies-txt/)
3. Đăng nhập vào YouTube trên trình duyệt của bạn
4. Mở tiện ích đã cài đặt, xuất cookies và lưu thành file `cookies.txt` trong thư mục cùng cấp với script

### Bước 2: Sử dụng file cookies

```bash
python improved_youtube_downloader.py --cookies-file cookies.txt --single-url URL_YOUTUBE
```

## Xử lý danh sách video với cookies

```bash
# Xử lý danh sách video trong file
python improved_youtube_downloader.py --use-browser-cookies --video-list videos.txt

# Xử lý danh sách kênh với cookies từ file
python improved_youtube_downloader.py --cookies-file cookies.txt --channel-list channel_list.txt
```

## Lưu ý quan trọng

1. Đảm bảo bạn đã đăng nhập vào tài khoản YouTube trong trình duyệt trước khi sử dụng tùy chọn `--use-browser-cookies`
2. Cookies có thể hết hạn sau một thời gian, khi đó bạn cần đăng nhập lại hoặc xuất cookies mới
3. Không chia sẻ file `cookies.txt` của bạn vì nó chứa thông tin đăng nhập của bạn

## Cách kiểm tra cấu hình cookies hiện tại

Khi chạy script, bạn sẽ thấy thông báo về cấu hình cookies đang sử dụng:

```
🍪 Sử dụng cookies từ trình duyệt: chrome
```

hoặc

```
🍪 Sử dụng cookies từ file: cookies.txt
```

hoặc

```
✅ Sử dụng phương thức đơn giản không xác thực (đã kiểm tra hoạt động tốt)...
```

## Tắt sử dụng cookies

Nếu bạn muốn tắt việc sử dụng cookies:

```bash
python improved_youtube_downloader.py --no-cookies --single-url URL_YOUTUBE
``` 