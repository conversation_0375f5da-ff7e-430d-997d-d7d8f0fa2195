# YouTube to TikTok Downloader - Setup Guide

This is a cleaned-up version of the YouTube to TikTok Downloader project. This document explains the files and how to use the tool.

## File Structure

- `youtube_downloader.py`: The main script that downloads and processes YouTube videos
- `watermark_fix.py`: Helper module for handling text encoding in FFmpeg commands
- `requirements.txt`: List of Python dependencies
- `README.md`: General information about the project
- `COOKIES_GUIDE.md`: Guide on how to set up cookies for YouTube authentication
- `run_with_utf8.bat`: Batch file to run the script with UTF-8 encoding (for Windows)
- `channel_list.txt`: Example file containing channels to process

## How to Use

1. **Install requirements**:
   ```
   pip install -r requirements.txt
   ```

2. **Basic Usage**:
   ```
   python youtube_downloader.py -u "https://www.youtube.com/watch?v=VIDEOID"
   ```

3. **Process a file containing multiple YouTube URLs**:
   ```
   python youtube_downloader.py -f path/to/urls.txt
   ```

4. **Process a list of channels**:
   ```
   python youtube_downloader.py -cl channel_list.txt
   ```

5. **Use CPU instead of GPU**:
   ```
   python youtube_downloader.py -u "URL" -c
   ```

## Features

- Downloads YouTube videos and converts them to TikTok-friendly format
- Supports processing shorts videos
- Handles multiple videos and channels
- Adds appropriate hashtags based on video content
- Optimized for both GPU and CPU processing
- Adds watermarks with channel names

## Troubleshooting

If you encounter any issues:
1. Make sure FFmpeg and yt-dlp are installed and accessible in your PATH
2. Check that all dependencies are installed
3. For permission issues on YouTube videos, refer to the COOKIES_GUIDE.md file
4. Review the youtube_downloader.log file for detailed error information 