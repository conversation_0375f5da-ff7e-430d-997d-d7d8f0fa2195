{"download": {"format": "bestvideo[height=1080][vcodec^=avc1]+bestaudio[acodec=aac]/bestvideo[height=1080][vcodec^=hvc1]+bestaudio[acodec=aac]/bestvideo[height=1080][vcodec^=h264]+bestaudio[acodec=aac]/bestvideo[height=1080][vcodec^=h265]+bestaudio[acodec=aac]/bestvideo[height=1080][ext=mp4][vcodec!*=vp9][vcodec!*=av01]+bestaudio[ext=m4a]/137+140/299+140/bestvideo[height=1080][vcodec!*=vp9][vcodec!*=av01]+bestaudio/bestvideo[height=1080]+bestaudio/best[height=1080]/best", "max_retries": 5, "smart_retry": true, "retry_delay_base": 2, "concurrent_fragments": 16, "buffer_size": "64M", "use_aria2c": true, "aria2c_max_connections": 8, "cookies_file": "cookies.txt", "cookies_max_age_days": 7, "gpu_optimized": true, "prefer_h264": true, "avoid_vp9": true, "avoid_av1": true, "fast_format_selection": true, "skip_unavailable_formats": true, "connection_timeout": 15, "read_timeout": 30}, "processing": {"add_title": false, "add_channel_name": false, "title_font_size": 70, "title_font": "Impact", "channel_font": "Impact", "max_title_width": 864, "title_position": "center", "channel_position": "bottom", "title_color": "white", "title_background": false, "title_background_color": "black", "max_segments": 40, "min_segment_duration": 90, "max_segment_duration": 150, "output_format": "mp4", "max_bitrate": 10000, "max_fps": 30, "max_parallel_processes": 3, "segment_parallel_limit": 4, "gpu_memory_limit": 6144, "adaptive_processing": true, "use_gpu": true, "gpu_optimization_level": "high", "prefer_gpu_filters": true, "gpu_memory_management": true, "apply_copyright_effects": true, "use_hflip": true, "use_rotation": true, "use_zoom": true, "use_color_adjustment": true, "use_border": true, "use_speed_adjustment": true}, "paths": {"output_dir": "data/output", "cache_dir": "data/cache", "log_file": "data/video_processor.log", "fonts_dir": "resources/fonts", "temp_dir": "data/temp"}, "performance": {"memory_threshold": 85, "disk_threshold": 90, "cleanup_interval": 30, "adaptive_resource_management": true, "smart_buffer_management": true, "gpu_memory_optimization": true, "auto_parallel_adjustment": true}, "user_experience": {"minimal_output": false, "show_system_info": true, "progress_update_interval": 2.0, "show_codec_info": true, "show_gpu_status": true, "hide_debug_info": true}, "error_handling": {"smart_retry": true, "adaptive_fallback": true, "error_classification": true, "auto_recovery": true, "max_error_threshold": 5}}