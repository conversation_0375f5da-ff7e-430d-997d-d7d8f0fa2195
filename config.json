{"download": {"format": "bestvideo[height=1080][vcodec^=avc1][ext=mp4]+bestaudio[acodec=aac][ext=m4a][abr>=128][abr<=192]/bestvideo[height=1080][vcodec^=h264][ext=mp4]+bestaudio[acodec=aac][ext=m4a][abr>=128][abr<=192]/bestvideo[height=1080][vcodec^=hvc1][ext=mp4]+bestaudio[acodec=aac][ext=m4a][abr>=128][abr<=192]/bestvideo[height=1080][vcodec^=h265][ext=mp4]+bestaudio[acodec=aac][ext=m4a][abr>=128][abr<=192]/137+140/bestvideo[height=720][vcodec^=avc1][ext=mp4]+bestaudio[acodec=aac][ext=m4a][abr>=128][abr<=192]/bestvideo[height=720][vcodec^=h264][ext=mp4]+bestaudio[acodec=aac][ext=m4a][abr>=128][abr<=192]/bestvideo[height=720][vcodec^=hvc1][ext=mp4]+bestaudio[acodec=aac][ext=m4a][abr>=128][abr<=192]/bestvideo[height=720][vcodec^=h265][ext=mp4]+bestaudio[acodec=aac][ext=m4a][abr>=128][abr<=192]/bestvideo[height=1080][ext=mp4][vcodec!*=vp9][vcodec!*=vp8][vcodec!*=av01][vcodec!*=av1]+bestaudio[acodec=aac][ext=m4a]/bestvideo[height=720][ext=mp4][vcodec!*=vp9][vcodec!*=vp8][vcodec!*=av01][vcodec!*=av1]+bestaudio[acodec=aac][ext=m4a]/best[height>=720][ext=mp4][vcodec!*=vp9][vcodec!*=vp8][vcodec!*=av01][vcodec!*=av1]", "max_retries": 5, "smart_retry": true, "retry_delay_base": 2, "concurrent_fragments": 16, "buffer_size": "64M", "use_aria2c": true, "aria2c_max_connections": 8, "cookies_file": "cookies.txt", "cookies_max_age_days": 7, "gpu_optimized": true, "prefer_h264": true, "avoid_vp9": true, "avoid_av1": true, "avoid_vp8": true, "prefer_aac_audio": true, "nvidia_rtx_3070ti_optimized": true, "preferred_audio_bitrate": "128-192", "preferred_sample_rate": "44100,48000", "strict_codec_filtering": true, "validate_downloaded_codec": true, "fast_format_selection": true, "skip_unavailable_formats": true, "connection_timeout": 15, "read_timeout": 30}, "processing": {"add_title": false, "add_channel_name": false, "title_font_size": 70, "title_font": "Impact", "channel_font": "Impact", "max_title_width": 864, "title_position": "center", "channel_position": "bottom", "title_color": "white", "title_background": false, "title_background_color": "black", "max_segments": 40, "min_segment_duration": 90, "max_segment_duration": 150, "output_format": "mp4", "max_bitrate": 10000, "max_fps": 30, "max_parallel_processes": 3, "segment_parallel_limit": 4, "gpu_memory_limit": 6144, "adaptive_processing": true, "use_gpu": true, "gpu_optimization_level": "high", "prefer_gpu_filters": true, "gpu_memory_management": true, "nvidia_rtx_3070ti_settings": {"preferred_encoder": "h264_nvenc", "fallback_encoder": "libx264", "preset": "fast", "profile": "high", "level": "4.1", "max_bitrate": 10000, "buffer_size": 20000, "gpu_memory_usage": 6144}, "apply_copyright_effects": true, "use_hflip": true, "use_rotation": true, "use_zoom": true, "use_color_adjustment": true, "use_border": true, "use_speed_adjustment": true}, "paths": {"output_dir": "data/output", "cache_dir": "data/cache", "log_file": "data/video_processor.log", "fonts_dir": "resources/fonts", "temp_dir": "data/temp"}, "performance": {"memory_threshold": 85, "disk_threshold": 90, "cleanup_interval": 30, "adaptive_resource_management": true, "smart_buffer_management": true, "gpu_memory_optimization": true, "auto_parallel_adjustment": true}, "user_experience": {"minimal_output": false, "show_system_info": true, "progress_update_interval": 2.0, "show_codec_info": true, "show_gpu_status": true, "hide_debug_info": true}, "error_handling": {"smart_retry": true, "adaptive_fallback": true, "error_classification": true, "auto_recovery": true, "max_error_threshold": 5}}