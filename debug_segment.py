#!/usr/bin/env python3
"""
Debug script to test segment processing directly
"""
import subprocess
import os
import json
import tempfile

def test_segment_processing():
    """Test segment processing with our test video"""
    
    test_video = "test_input.mp4"
    
    if not os.path.exists(test_video):
        print("❌ Test video not found")
        return
    
    print(f"🎬 Using test video: {test_video}")
    
    # Check input streams
    print("\n🔍 Checking input streams:")
    probe_cmd = ['ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_streams', test_video]
    try:
        result = subprocess.run(probe_cmd, capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            streams_info = json.loads(result.stdout)
            video_streams = [s for s in streams_info.get('streams', []) if s.get('codec_type') == 'video']
            audio_streams = [s for s in streams_info.get('streams', []) if s.get('codec_type') == 'audio']
            print(f"📹 Input has {len(video_streams)} video stream(s) and {len(audio_streams)} audio stream(s)")
            if video_streams:
                v = video_streams[0]
                print(f"   Video: {v.get('width')}x{v.get('height')}, {v.get('codec_name')}")
            if audio_streams:
                a = audio_streams[0]
                print(f"   Audio: {a.get('codec_name')}, {a.get('channels')} channels")
        else:
            print(f"❌ Failed to probe input: {result.stderr}")
            return
    except Exception as e:
        print(f"❌ Error probing input: {e}")
        return
    
    # Test segment extraction (similar to what the processor does)
    print("\n🧪 Testing segment extraction:")
    
    # Create a segment from 0 to 5 seconds
    segment_output = "test_segment.mp4"
    
    # Build FFmpeg command similar to what the processor uses
    cmd = [
        'ffmpeg', '-y',
        '-ss', '0',
        '-to', '5',
        '-i', test_video,
        '-fps_mode', 'passthrough'
    ]
    
    # Add GPU encoding if available
    cmd.extend([
        '-c:v', 'h264_nvenc',
        '-preset', 'fast',
        '-profile:v', 'high',
        '-cq', '23',
        '-b:v', '8000k',
        '-maxrate', '9600k',
        '-bufsize', '16000k',
        '-color_primaries', 'bt709',
        '-color_trc', 'bt709',
        '-colorspace', 'bt709'
    ])
    
    # Add video filters
    video_filters = [
        'fps=30',
        'eq=gamma=1.0:contrast=1.0:brightness=0.0:saturation=1.0',
        'scale=1920:1080:flags=lanczos:force_original_aspect_ratio=decrease',
        'pad=1920:1080:(ow-iw)/2:(oh-ih)/2:black'
    ]
    
    cmd.extend(['-vf', ','.join(video_filters)])
    
    # Add audio settings
    cmd.extend([
        '-c:a', 'aac', '-b:a', '128k', '-ar', '44100', '-ac', '2'
    ])
    
    # Add container settings
    cmd.extend([
        '-movflags', '+faststart',
        '-max_muxing_queue_size', '1024',
        '-avoid_negative_ts', 'make_zero',
        '-fflags', '+genpts'
    ])
    
    cmd.append(segment_output)
    
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        if result.returncode == 0:
            print(f"✅ Success: {segment_output}")
            
            # Check output streams
            probe_result = subprocess.run(['ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_streams', segment_output], 
                                        capture_output=True, text=True, timeout=10)
            if probe_result.returncode == 0:
                streams_info = json.loads(probe_result.stdout)
                video_streams = [s for s in streams_info.get('streams', []) if s.get('codec_type') == 'video']
                audio_streams = [s for s in streams_info.get('streams', []) if s.get('codec_type') == 'audio']
                print(f"   Output: {len(video_streams)} video, {len(audio_streams)} audio streams")
                if video_streams:
                    v = video_streams[0]
                    print(f"   Video: {v.get('width')}x{v.get('height')}, {v.get('codec_name')}")
                if not video_streams:
                    print("   ❌ NO VIDEO STREAM IN OUTPUT!")
                    print(f"   FFmpeg stderr: {result.stderr}")
            else:
                print(f"   ⚠️ Could not probe output")
        else:
            print(f"❌ Failed: {result.stderr}")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_segment_processing()
