# Hướng dẫn sử dụng cookie cho YouTube Downloader

## <PERSON><PERSON><PERSON> thức hoạt động tự động của script

Script đã được cải tiến để tự động thử lấy cookie theo thứ tự ưu tiên sau:

1. **File cookies.txt** - Nếu bạn đã tạo file này thủ công
2. **Trình duyệt Firefox** - Firefox thường không khóa cơ sở dữ liệu cookie
3. **Microsoft Edge** - Thử lấy cookie từ Edge
4. **Chrome** - Nếu có plugin ChromeCookieUnlock được cài đặt

Nếu không tìm thấy cookie từ bất kỳ nguồn nào, script sẽ sử dụng chế độ không xác thực.

## Cách thủ công: Tạo file cookies.txt từ Chrome

Nếu quá trình tự động không hoạt động, bạn vẫn có thể tạo file cookies.txt thủ công:

1. Cài đặt tiện ích 'cookies.txt' hoặc 'EditThisCookie' cho Chrome
   - Cookies.txt: https://chrome.google.com/webstore/detail/cookiestxt/njabckikapfpffapmjgojcnbfjonfjfg
   - EditThisCookie: https://chrome.google.com/webstore/detail/editthiscookie/fngmhnnpilhplaeedifhccceomclgfbg

2. Đối với 'EditThisCookie':
   - Đi tới Options và thay đổi định dạng xuất sang 'Netscape HTTP Cookie File'
   - Đăng nhập vào YouTube
   - Nhấp vào biểu tượng 'EditThisCookie'
   - Nhấp vào 'Export' và lưu nội dung vào file 'cookies.txt' trong cùng thư mục với script

3. Đối với 'cookies.txt':
   - Đăng nhập vào YouTube
   - Truy cập youtube.com
   - Nhấp vào biểu tượng tiện ích và chọn 'Export'
   - Lưu file dưới tên 'cookies.txt' trong cùng thư mục với script

## Cài đặt plugin ChromeCookieUnlock (tùy chọn)

Để cho phép script tự động truy cập cookie từ Chrome ngay cả khi Chrome đang mở:

1. Cài đặt plugin với lệnh:
   ```
   yt-dlp --update-to nightly
   pip install git+https://github.com/seproDev/yt-dlp-ChromeCookieUnlock.git
   ```

2. Khởi động lại script - Nó sẽ tự động phát hiện và sử dụng plugin này

## Lưu ý quan trọng
- Cookie có thể hết hạn sau một thời gian, khi đó bạn có thể cần đăng nhập lại vào YouTube
- YouTube có thể phát hiện và hạn chế hoạt động tự động, vì vậy không nên tải quá nhiều video trong thời gian ngắn

# Hướng dẫn xuất cookies để tải video YouTube

Khi bạn gặp lỗi như sau:
```
ERROR: [youtube] XXX: Sign in to confirm you're not a bot. Use --cookies-from-browser or --cookies for the authentication.
```

YouTube đang yêu cầu bạn đăng nhập để xác minh bạn không phải là bot. Đây là cách xuất cookies từ trình duyệt để sử dụng với yt-dlp:

## Cách 1: Sử dụng cookies từ trình duyệt đang mở

Thêm tham số `--cookies-from-browser` vào lệnh yt-dlp:

```bash
# Đối với Chrome:
yt-dlp --cookies-from-browser chrome https://www.youtube.com/watch?v=XXXX

# Đối với Firefox:
yt-dlp --cookies-from-browser firefox https://www.youtube.com/watch?v=XXXX

# Đối với Edge:
yt-dlp --cookies-from-browser edge https://www.youtube.com/watch?v=XXXX
```

## Cách 2: Xuất cookies sang file và sử dụng file đó

### Bước 1: Cài đặt tiện ích mở rộng cho trình duyệt

- **Chrome/Edge**: Cài đặt tiện ích ["Get cookies.txt"](https://chrome.google.com/webstore/detail/get-cookiestxt/bgaddhkoddajcdgocldbbfleckgcbcid)
- **Firefox**: Cài đặt tiện ích ["cookies.txt"](https://addons.mozilla.org/en-US/firefox/addon/cookies-txt/)

### Bước 2: Xuất cookies

1. Mở trình duyệt và đăng nhập vào YouTube
2. Truy cập trang YouTube.com
3. Mở tiện ích đã cài đặt
4. Xuất cookies và lưu thành file (ví dụ: `cookies.txt`)

### Bước 3: Sử dụng file cookies trong script

Thêm tham số `--cookies` vào lệnh yt-dlp:

```bash
yt-dlp --cookies cookies.txt https://www.youtube.com/watch?v=XXXX
```

## Cách cấu hình trong script python

Trong file `improved_youtube_downloader.py`, tìm và sửa các lệnh yt-dlp để thêm tùy chọn cookies:

```python
# Sử dụng cookies từ file
cmd = ["yt-dlp", "--cookies", "cookies.txt", ...]

# Hoặc sử dụng cookies từ trình duyệt
cmd = ["yt-dlp", "--cookies-from-browser", "chrome", ...]
```

## Lưu ý quan trọng

1. Đảm bảo bạn đã đăng nhập vào tài khoản YouTube trong trình duyệt trước khi xuất cookies
2. Cookies có thể hết hạn sau một thời gian, nên cần xuất lại nếu gặp lỗi
3. Không chia sẻ file cookies.txt vì nó chứa thông tin đăng nhập của bạn 