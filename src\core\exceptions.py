"""
Custom exceptions for the video processor application.
"""

class VideoProcessorError(Exception):
    """Base exception for all video processor errors."""
    pass


class DownloadError(VideoProcessorError):
    """Raised when a video download fails."""
    pass


class ProcessingError(VideoProcessorError):
    """Raised when video processing fails."""
    pass


class FormatNotFoundError(DownloadError):
    """Raised when the requested video format is not available."""
    pass


class NetworkError(DownloadError):
    """Raised when network issues prevent downloading."""
    pass


class FFmpegError(ProcessingError):
    """Raised when FFmpeg encounters an error during processing."""
    pass


class ConfigError(VideoProcessorError):
    """Raised when there's an issue with configuration."""
    pass
