"""
Data models for the video processor application.
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any


@dataclass
class VideoFormat:
    """Represents a video format option."""
    format_id: str
    height: Optional[int] = None
    width: Optional[int] = None
    ext: str = "mp4"
    filesize: Optional[int] = None
    fps: Optional[int] = None
    vcodec: Optional[str] = None
    acodec: Optional[str] = None

    @property
    def resolution(self) -> str:
        """Get the resolution as a string."""
        if self.width and self.height:
            return f"{self.width}x{self.height}"
        return "unknown"

    @property
    def filesize_mb(self) -> float:
        """Get the filesize in MB."""
        if self.filesize:
            return self.filesize / (1024 * 1024)
        return 0.0


@dataclass
class VideoInfo:
    """Represents information about a video."""
    id: str
    title: str
    url: str
    duration: float
    formats: List[VideoFormat] = field(default_factory=list)
    thumbnail: Optional[str] = None
    description: Optional[str] = None
    upload_date: Optional[str] = None
    uploader: Optional[str] = None
    channel_id: Optional[str] = None
    view_count: Optional[int] = None
    like_count: Optional[int] = None
    tags: List[str] = field(default_factory=list)  # YouTube tags/keywords

    @property
    def duration_formatted(self) -> str:
        """Get the duration as a formatted string (HH:MM:SS)."""
        minutes, seconds = divmod(self.duration, 60)
        hours, minutes = divmod(minutes, 60)
        return f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"

    @classmethod
    def from_yt_dlp_info(cls, info: Dict[str, Any]) -> 'VideoInfo':
        """Create a VideoInfo instance from yt-dlp info dictionary."""
        formats = []
        for fmt in info.get('formats', []):
            if fmt and isinstance(fmt, dict):
                formats.append(VideoFormat(
                    format_id=fmt.get('format_id', ''),
                    height=fmt.get('height'),
                    width=fmt.get('width'),
                    ext=fmt.get('ext', 'mp4'),
                    filesize=fmt.get('filesize'),
                    fps=fmt.get('fps'),
                    vcodec=fmt.get('vcodec'),
                    acodec=fmt.get('acodec')
                ))

        # Extract tags from info
        tags = []
        if 'tags' in info and isinstance(info['tags'], list):
            tags = info['tags']
        elif 'keywords' in info and isinstance(info['keywords'], list):
            tags = info['keywords']

        return cls(
            id=info.get('id', ''),
            title=info.get('title', 'Unknown Title'),
            url=info.get('webpage_url', info.get('url', '')),
            duration=info.get('duration', 0.0),
            formats=formats,
            thumbnail=info.get('thumbnail'),
            description=info.get('description'),
            upload_date=info.get('upload_date'),
            uploader=info.get('uploader'),
            channel_id=info.get('channel_id'),
            view_count=info.get('view_count'),
            like_count=info.get('like_count'),
            tags=tags
        )


@dataclass
class ProcessingOptions:
    """Options for video processing."""
    # Title and channel options
    add_title: bool = True
    add_channel_name: bool = True
    title_font_size: int = 70
    title_font: str = "Impact"
    channel_font: str = "Impact"
    max_title_width: int = 864
    title_position: str = "center"
    channel_position: str = "bottom"
    title_color: str = "white"
    title_background: bool = False
    title_background_color: str = "black"

    # Segment options
    max_segments: int = 3
    min_segment_duration: int = 90
    max_segment_duration: int = 150

    # Output options
    output_format: str = "mp4"
    max_bitrate: int = 6000
    max_fps: int = 30

    # GPU acceleration
    use_gpu: bool = True

    # Copyright circumvention effects
    apply_copyright_effects: bool = True
    use_hflip: bool = True          # Horizontal flip
    use_rotation: bool = True       # Slight rotation
    use_zoom: bool = True           # Slight zoom
    use_color_adjustment: bool = True  # Slight color adjustment
    use_border: bool = True         # Add slight border
    use_speed_adjustment: bool = True  # Slight speed adjustment
