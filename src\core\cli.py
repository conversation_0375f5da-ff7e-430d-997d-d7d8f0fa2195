"""
Command-line interface for the video processor application.
"""

import os
import sys
import argparse
import logging
from typing import Dict, Any, List, Optional

from rich.console import Console
from rich.progress import Progress, TextColumn, BarColumn, TimeElapsedColumn, TimeRemainingColumn
from rich.prompt import Prompt, Confirm
from rich.table import Table
from rich.panel import Panel
from rich.text import Text

from src.core.models import VideoInfo
from src.core.utils import format_duration, format_size, check_ffmpeg, check_aria2c
from src.core.exceptions import VideoProcessorError, DownloadError, ProcessingError

console = Console()


def parse_args() -> argparse.Namespace:
    """
    Parse command-line arguments.

    Returns:
        Parsed arguments.
    """
    parser = argparse.ArgumentParser(
        description="Video Processor - Công cụ tải và xử lý video YouTube chuyên nghiệp",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    parser.add_argument(
        "url",
        nargs="?",
        help="URL của video YouTube cần tải"
    )

    parser.add_argument(
        "-o", "--output",
        help="Thư mục đầu ra"
    )

    parser.add_argument(
        "-r", "--resolution",
        default="1080",
        help="Độ phân giải video (720, 1080, ...)"
    )

    parser.add_argument(
        "--no-process",
        action="store_true",
        help="Chỉ tải video, không xử lý"
    )

    parser.add_argument(
        "--no-title",
        action="store_true",
        help="Không thêm tiêu đề vào video"
    )

    parser.add_argument(
        "--no-channel",
        action="store_true",
        help="Không thêm tên kênh vào video"
    )

    parser.add_argument(
        "--segments",
        type=int,
        default=3,
        help="Số đoạn video tối đa"
    )

    parser.add_argument(
        "--min-duration",
        type=int,
        default=90,
        help="Thời lượng tối thiểu của mỗi đoạn (giây)"
    )

    parser.add_argument(
        "--max-duration",
        type=int,
        default=150,
        help="Thời lượng tối đa của mỗi đoạn (giây)"
    )

    parser.add_argument(
        "--font",
        default="Impact",
        help="Font chữ cho tiêu đề"
    )

    parser.add_argument(
        "--font-size",
        type=int,
        default=70,
        help="Kích thước font chữ cho tiêu đề"
    )

    parser.add_argument(
        "--cookies",
        help="Đường dẫn đến file cookies"
    )

    parser.add_argument(
        "--config",
        help="Đường dẫn đến file cấu hình"
    )

    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Hiển thị thông tin chi tiết"
    )

    parser.add_argument(
        "--interactive",
        action="store_true",
        help="Chế độ tương tác"
    )

    parser.add_argument(
        "--version",
        action="store_true",
        help="Hiển thị phiên bản"
    )

    return parser.parse_args()


def check_dependencies() -> bool:
    """
    Check if required dependencies are installed.

    Returns:
        True if all dependencies are installed, False otherwise.
    """
    # Check FFmpeg silently
    if not check_ffmpeg():
        console.print("❌ [red]FFmpeg[/red]: Chưa cài đặt")
        console.print("[yellow]Vui lòng cài đặt FFmpeg để sử dụng ứng dụng này.[/yellow]")
        return False

    # Check aria2c silently
    if not check_aria2c():
        # Không hiển thị thông báo về aria2c không bắt buộc
        pass

    return True


def display_video_info(video_info: VideoInfo) -> None:
    """
    Display video information.

    Args:
        video_info: VideoInfo object with video information.
    """
    # Create panel with video info
    title = Text(video_info.title, style="bold green")

    info_text = Text()
    info_text.append("\n")

    if video_info.uploader:
        info_text.append("Kênh: ", style="bold")
        info_text.append(f"{video_info.uploader}\n", style="yellow")

    info_text.append("Thời lượng: ", style="bold")
    info_text.append(f"{video_info.duration_formatted}\n")

    if video_info.view_count:
        info_text.append("Lượt xem: ", style="bold")
        info_text.append(f"{video_info.view_count:,}\n")

    if video_info.like_count:
        info_text.append("Lượt thích: ", style="bold")
        info_text.append(f"{video_info.like_count:,}\n")

    if video_info.upload_date:
        info_text.append("Ngày đăng: ", style="bold")
        upload_date = video_info.upload_date
        formatted_date = f"{upload_date[0:4]}-{upload_date[4:6]}-{upload_date[6:8]}"
        info_text.append(f"{formatted_date}\n")

    panel = Panel(
        info_text,
        title=title,
        border_style="blue",
        expand=False
    )

    console.print(panel)

    # Display available formats
    if video_info.formats:
        table = Table(title="Các định dạng có sẵn")
        table.add_column("ID", style="cyan")
        table.add_column("Độ phân giải", style="green")
        table.add_column("FPS", style="yellow")
        table.add_column("Định dạng", style="magenta")
        table.add_column("Kích thước", style="blue")

        # Filter and sort formats
        video_formats = [fmt for fmt in video_info.formats if fmt.height and fmt.width]
        video_formats.sort(key=lambda x: (x.height or 0, x.width or 0), reverse=True)

        # Display top 5 formats
        for fmt in video_formats[:5]:
            table.add_row(
                fmt.format_id,
                fmt.resolution,
                str(fmt.fps) if fmt.fps else "N/A",
                fmt.ext,
                format_size(fmt.filesize) if fmt.filesize else "N/A"
            )

        console.print(table)


def create_progress_bar() -> Progress:
    """
    Create a minimal progress bar without visual indicators, percentages, or time displays.
    Only shows a simple text description of the current task.

    Returns:
        Progress object.
    """
    return Progress(
        TextColumn("[bold blue]{task.description}"),
        console=console,
        transient=True,  # Không giữ lại các dòng tiến trình cũ
        refresh_per_second=1  # Giảm tần suất cập nhật xuống 1 lần/giây
    )


def interactive_mode(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Run interactive mode to get user input.

    Args:
        config: Current configuration.

    Returns:
        Updated configuration.
    """
    console.print("[bold green]Video Processor[/bold green]")
    console.print("Công cụ tải và xử lý video YouTube chuyên nghiệp\n")

    # Get URL
    url = Prompt.ask("[bold]Nhập URL video YouTube[/bold]")

    # Get output directory
    output_dir = Prompt.ask(
        "[bold]Thư mục đầu ra[/bold]",
        default=config.get("paths", {}).get("output_dir", "data/output")
    )

    # Ask about processing
    process_video = Confirm.ask("[bold]Xử lý video sau khi tải?[/bold]", default=True)

    if process_video:
        # Get processing options
        add_title = Confirm.ask("[bold]Thêm tiêu đề vào video?[/bold]", default=config.get("processing", {}).get("add_title", True))
        add_channel = Confirm.ask("[bold]Thêm tên kênh vào video?[/bold]", default=config.get("processing", {}).get("add_channel_name", True))

        max_segments = int(Prompt.ask(
            "[bold]Số đoạn video tối đa[/bold]",
            default=str(config.get("processing", {}).get("max_segments", 3))
        ))

        min_duration = int(Prompt.ask(
            "[bold]Thời lượng tối thiểu của mỗi đoạn (giây)[/bold]",
            default=str(config.get("processing", {}).get("min_segment_duration", 90))
        ))

        max_duration = int(Prompt.ask(
            "[bold]Thời lượng tối đa của mỗi đoạn (giây)[/bold]",
            default=str(config.get("processing", {}).get("max_segment_duration", 150))
        ))

        font = Prompt.ask(
            "[bold]Font chữ cho tiêu đề[/bold]",
            default=config.get("processing", {}).get("title_font", "Impact")
        )

        font_size = int(Prompt.ask(
            "[bold]Kích thước font chữ cho tiêu đề[/bold]",
            default=str(config.get("processing", {}).get("title_font_size", 70))
        ))

    # Update config
    updated_config = config.copy()

    # Update paths
    if "paths" not in updated_config:
        updated_config["paths"] = {}
    updated_config["paths"]["output_dir"] = output_dir

    # Update processing options
    if process_video:
        if "processing" not in updated_config:
            updated_config["processing"] = {}
        updated_config["processing"]["add_title"] = add_title
        updated_config["processing"]["add_channel_name"] = add_channel
        updated_config["processing"]["max_segments"] = max_segments
        updated_config["processing"]["min_segment_duration"] = min_duration
        updated_config["processing"]["max_segment_duration"] = max_duration
        updated_config["processing"]["title_font"] = font
        updated_config["processing"]["title_font_size"] = font_size

    return updated_config, url, process_video


def display_error(error: Exception) -> None:
    """
    Display an error message.

    Args:
        error: Exception to display.
    """
    if isinstance(error, DownloadError):
        console.print(f"[bold red]Lỗi khi tải video:[/bold red] {str(error)}")
    elif isinstance(error, ProcessingError):
        console.print(f"[bold red]Lỗi khi xử lý video:[/bold red] {str(error)}")
    elif isinstance(error, VideoProcessorError):
        console.print(f"[bold red]Lỗi:[/bold red] {str(error)}")
    else:
        console.print(f"[bold red]Lỗi không xác định:[/bold red] {str(error)}")


def display_success(output_path: str) -> None:
    """
    Display a success message.

    Args:
        output_path: Path to the output file.
    """
    console.print(f"[bold green]✅ Hoàn thành! Video đã được lưu tại:[/bold green] {output_path}")


def display_version() -> None:
    """Display version information."""
    from src.core import __version__

    console.print(f"[bold green]Video Processor[/bold green] phiên bản [bold]{__version__}[/bold]")
    console.print("Phát triển bởi: [bold blue]Augment Code[/bold blue]")
    console.print("Trang web: [link]https://augment.dev[/link]")
