"""
Module tải video từ YouTube using yt-dlp with advanced configuration
"""
import os
import json
import logging
import tempfile
import subprocess
from typing import Optional, Dict, List
import yt_dlp
import re
import glob
import time
import traceback
import random

# Import hàm kiểm tra cookies
from src.processors.youtube_downloader_cookies_check import check_cookies_freshness

# Cấu hình tối ưu cho yt-dlp
YTDLP_OPTIONS = {
    # Định dạng video - Tải video và audio riêng biệt để có chất lượng tốt nhất
    'format': 'bestvideo[height<=1080][fps<=30]+bestaudio/best[height<=1080][fps<=30]/best',
    'format_sort': ['res:1080', 'fps:30', 'size', 'br'],
    'video_format_sort': ['res:1080', 'fps:30', 'br', 'size'],
    'audio_format_sort': ['br', 'asr'],
    'postprocessor_args': {
        'ffmpeg': ['-c:v', 'copy', '-c:a', 'aac', '-b:a', '192k']
    },
    'outtmpl': '%(id)s_temp.%(ext)s',
    'writeinfojson': True,  # Lưu thông tin video dưới dạng JSON
    'merge_output_format': 'mp4',
    'hls_prefer_native': True,  # Hỗ trợ xử lý HLS (m3u8)

    # Sử dụng client web chuẩn để tránh giới hạn của client Android
    'extractor_args': {
        'youtube': {
            'player_client': ['web', 'tv_embedded', 'ios'],
            'player_skip': ['webpage', 'configs'],
            'player_client_name': 'WEB',
            'po_token': 'web.gvs+QmVhckBCYWNrQEJlYXJAQmFja0BAZWFAQB',  # PO token đặc biệt để truy cập các định dạng cao hơn
            'formats': 'missing_pot'  # Hiển thị cả các định dạng thiếu PO token
        }
    },

    # Tùy chọn tải xuống
    'noplaylist': True,  # Chỉ tải video đơn, không tải playlist
    'playlistrandom': False,  # Không tải ngẫu nhiên các video trong playlist
    'playlistend': 1,  # Chỉ tải video đầu tiên trong playlist
    'playlist_items': '1',  # Chỉ tải video đầu tiên trong playlist
    'skip_download': False,  # Không bỏ qua tải xuống
    'overwrites': True,  # Ghi đè các file đã tồn tại
    'continue': True,  # Tiếp tục tải các file đã tải dở
    'part': True,  # Sử dụng file .part
    'keep_fragments': False,  # Không giữ lại các fragment
    'updatetime': True,  # Cập nhật thời gian của file

    # Tối ưu hóa tải xuống
    'concurrent_fragment_downloads': 32,  # Tăng số lượng fragment tải song song lên 32
    'buffersize': 1024*1024*32,  # Tăng buffer size lên 32MB
    'throttledratelimit': 200000,  # Tăng giới hạn tốc độ lên 200MB/s
    'external_downloader': 'aria2c',  # Sử dụng aria2c cho tải xuống nhanh hơn
    'external_downloader_args': {
        'aria2c': [
            '--min-split-size=1M',  # Chia nhỏ file để tải song song
            '--max-connection-per-server=16',  # 16 kết nối mỗi server
            '--max-concurrent-downloads=16',  # 16 tải xuống đồng thời
            '--split=16',  # Chia file thành 16 phần
            '--optimize-concurrent-downloads=true',  # Tự động tối ưu hóa
            '--file-allocation=none'  # Không cấp phát trước file
        ]
    },

    # Thử lại khi gặp lỗi
    'retries': 10,
    'fragment_retries': 10,
    'file_access_retries': 10,
    'extractor_retries': 10,
    'retry_sleep_functions': {'fragment': lambda n: 5 * (2 ** (n-1))},  # Exponential backoff

    # Tùy chọn kết nối
    'socket_timeout': 60,  # Timeout 60s
    'source_address': '0.0.0.0',  # Địa chỉ nguồn

    # Tùy chọn HTTP
    'http_headers': {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    },

    # Tùy chọn gỡ lỗi - Ẩn các log debug
    'verbose': False,
    'quiet': True,
    'no_warnings': True,
    'no_color': True,
    'progress': False,  # Không hiển thị thanh tiến trình
    'logger': None,  # Không sử dụng logger mặc định
    'debug_printtraffic': False,  # Không in thông tin traffic

    # Tùy chọn nâng cao
    'geo_bypass': True,  # Bypass geo-restrictions
    'geo_bypass_country': 'US',
    'age_limit': 21,  # Bypass age restrictions
    'postprocessors': [{
        'key': 'FFmpegVideoConvertor',
        'preferedformat': 'mp4',
    }],
    'ignoreerrors': True,
    'extract_flat': False,
    'allow_unplayable_formats': True,  # Cho phép tải các format không phát được
    'check_formats': False,  # Không kiểm tra format trước khi tải
}

class YouTubeDownloader:
    """Lớp tải video từ YouTube với các tùy chọn nâng cao"""

    def __init__(self, url: str, output_dir: str, cookies_file: Optional[str] = None):
        self.url = url
        self.output_dir = output_dir
        self.cookies_file = cookies_file
        self.temp_dir = tempfile.mkdtemp()
        self.completed_urls_file = os.path.join(output_dir, "completed_urls.txt")
        self.failed_urls_file = os.path.join(output_dir, "failed_urls.txt")
        
        # Tạo thư mục output nếu chưa tồn tại
        os.makedirs(output_dir, exist_ok=True)
        
        # Tạo file completed_urls.txt nếu chưa tồn tại
        if not os.path.exists(self.completed_urls_file):
            with open(self.completed_urls_file, 'w', encoding='utf-8'):
                pass
            logging.debug(f"Đã tạo file mới: {self.completed_urls_file}")
        
        # Tạo file failed_urls.txt nếu chưa tồn tại
        if not os.path.exists(self.failed_urls_file):
            with open(self.failed_urls_file, 'w', encoding='utf-8'):
                pass
            logging.debug(f"Đã tạo file mới: {self.failed_urls_file}")

    def __del__(self):
        """Dọn dẹp thư mục tạm khi đối tượng bị hủy"""
        try:
            # Xóa các file tạm
            for file in glob.glob(os.path.join(self.temp_dir, "*")):
                try:
                    os.remove(file)
                except Exception as e:
                    logging.debug(f"Không thể xóa file tạm {file}: {str(e)}")
            
            # Xóa thư mục tạm
            if os.path.exists(self.temp_dir):
                try:
                    os.rmdir(self.temp_dir)
                except Exception as e:
                    logging.debug(f"Không thể xóa thư mục tạm {self.temp_dir}: {str(e)}")
        except Exception as e:
            logging.debug(f"Lỗi khi dọn dẹp thư mục tạm: {str(e)}")

    def _is_url_completed(self, url: str) -> bool:
        """Kiểm tra xem URL đã được tải xuống thành công chưa"""
        try:
            with open(self.completed_urls_file, 'r', encoding='utf-8') as f:
                completed_urls = f.read().splitlines()
            return url in completed_urls
        except Exception as e:
            logging.error(f"Lỗi khi kiểm tra URL đã hoàn thành: {str(e)}")
            return False

    def _mark_url_completed(self, url: str) -> None:
        """Đánh dấu URL đã được tải xuống thành công"""
        try:
            with open(self.completed_urls_file, 'a', encoding='utf-8') as f:
                f.write(f"{url}\n")
            logging.debug(f"Đã đánh dấu URL đã hoàn thành: {url}")
        except Exception as e:
            logging.error(f"Lỗi khi đánh dấu URL đã hoàn thành: {str(e)}")

    def _mark_url_failed(self, url: str) -> None:
        """Đánh dấu URL tải xuống thất bại"""
        try:
            with open(self.failed_urls_file, 'a', encoding='utf-8') as f:
                f.write(f"{url}\n")
            logging.debug(f"Đã đánh dấu URL thất bại: {url}")
        except Exception as e:
            logging.error(f"Lỗi khi đánh dấu URL thất bại: {str(e)}")

    def _get_video_info(self, url: str) -> Dict:
        """Lấy thông tin video từ URL"""
        try:
            # Tạo bản sao của tùy chọn mặc định
            ydl_opts = YTDLP_OPTIONS.copy()
            
            # Thêm cookies nếu có
            if self.cookies_file and os.path.exists(self.cookies_file):
                # Kiểm tra cookies có còn hiệu lực không
                if check_cookies_freshness(self.cookies_file):
                    ydl_opts['cookiefile'] = self.cookies_file
                    print(f"✅ Sử dụng cookies từ file: {self.cookies_file}")
                else:
                    print(f"⚠️ File cookies đã hết hạn hoặc không hợp lệ: {self.cookies_file}")
            
            # Chỉ lấy thông tin, không tải xuống
            ydl_opts['skip_download'] = True
            
            # Sử dụng yt-dlp để lấy thông tin video
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                
                # Kiểm tra xem có thông tin video không
                if not info:
                    logging.error(f"Không thể lấy thông tin video từ URL: {url}")
                    return {}
                
                # Trả về thông tin video
                return info
        except Exception as e:
            logging.error(f"Lỗi khi lấy thông tin video: {str(e)}")
            return {}

    def download(self, output_filename: Optional[str] = None) -> Dict:
        """Tải video từ URL và trả về thông tin video"""
        # Kiểm tra xem URL đã được tải xuống thành công chưa
        if self._is_url_completed(self.url):
            print(f"⏭️ Bỏ qua URL đã tải xuống thành công: {self.url}")
            return {}
        
        # Lấy thông tin video
        info = self._get_video_info(self.url)
        if not info:
            self._mark_url_failed(self.url)
            return {}
        
        # Lấy tiêu đề video
        title = info.get('title', 'Unknown Title')
        print(f"📝 Tiêu đề video: {title}")
        
        # Lấy tên kênh
        channel = info.get('channel', 'Unknown Channel')
        print(f"🎬 Kênh: {channel}")
        
        # Kiểm tra độ phân giải
        formats = info.get('formats', [])
        has_1080p = any(f.get('height', 0) >= 1080 for f in formats)
        print(f"🔍 Độ phân giải hiện có: {'1080p' if has_1080p else 'Thấp hơn 1080p'}")
        
        # Hiển thị các độ phân giải có sẵn
        available_resolutions = sorted(set(f.get('height', 0) for f in formats if f.get('height')), reverse=True)
        print(f"🔍 Các độ phân giải có sẵn: {', '.join([f'{r}p' for r in available_resolutions if r > 0])}")
        
        # Hiển thị định dạng tốt nhất có sẵn
        best_format = max((f for f in formats if f.get('height')), key=lambda x: (x.get('height', 0), x.get('width', 0)), default=None)
        if best_format:
            print(f"🔍 Định dạng tốt nhất có sẵn: {best_format.get('width', 0)}x{best_format.get('height', 0)} ({best_format.get('height', 0)}p, {best_format.get('format_note', 'Unknown')})")
        
        # Kiểm tra xem có độ phân giải 1080p không
        if has_1080p:
            print(f"✅ Video có sẵn ở độ phân giải 1080p. Sẽ tải ở độ phân giải cao nhất.")
        else:
            print(f"⚠️ Video không có sẵn ở độ phân giải 1080p. Sẽ tải ở độ phân giải cao nhất có sẵn.")
        
        # Hiển thị các định dạng video tốt nhất có sẵn
        best_formats = sorted(
            [f for f in formats if f.get('height', 0) >= 720 and f.get('vcodec', 'none') != 'none'],
            key=lambda x: (x.get('height', 0), x.get('width', 0), x.get('filesize', 0) or float('inf')),
            reverse=True
        )[:3]
        
        if best_formats:
            print(f"\n🔍 Các định dạng video tốt nhất có sẵn:")
            for i, fmt in enumerate(best_formats, 1):
                filesize_mb = (fmt.get('filesize', 0) or 0) / (1024 * 1024)
                print(f"  {i}. {fmt.get('width', 0)}x{fmt.get('height', 0)} - {fmt.get('height', 0)}p, {fmt.get('format_note', 'Unknown')} ({fmt.get('ext', 'unknown')}) - {filesize_mb:.1f}MB")
        
        # Tạo bản sao của tùy chọn mặc định
        ydl_opts = YTDLP_OPTIONS.copy()
        
        # Thêm cookies nếu có
        if self.cookies_file and os.path.exists(self.cookies_file):
            # Kiểm tra cookies có còn hiệu lực không
            if check_cookies_freshness(self.cookies_file):
                ydl_opts['cookiefile'] = self.cookies_file
                print(f"✅ Sử dụng cookies từ file: {self.cookies_file}")
            else:
                print(f"⚠️ File cookies đã hết hạn hoặc không hợp lệ: {self.cookies_file}")
        
        # Thử tải với tham số formats=missing_pot
        print(f"🔍 Thử tải video với tham số formats=missing_pot...")
        ydl_opts['extractor_args'] = {
            'youtube': {
                'player_client': ['web', 'tv_embedded', 'ios'],
                'player_skip': ['webpage', 'configs'],
                'player_client_name': 'WEB',
                'po_token': 'web.gvs+QmVhckBCYWNrQEJlYXJAQmFja0BAZWFAQB',
                'formats': 'missing_pot'
            }
        }
        
        # Kiểm tra xem có định dạng 1080p với tham số formats=missing_pot không
        try:
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(self.url, download=False)
                formats = info.get('formats', [])
                has_1080p_missing_pot = any(f.get('height', 0) >= 1080 for f in formats)
                
                if has_1080p_missing_pot:
                    print(f"✅ Đã tìm thấy định dạng 1080p với tham số formats=missing_pot")
                else:
                    print(f"⚠️ Không tìm thấy định dạng 1080p với tham số formats=missing_pot")
        except Exception as e:
            logging.error(f"Lỗi khi kiểm tra định dạng 1080p với tham số formats=missing_pot: {str(e)}")
            has_1080p_missing_pot = False
        
        # Tải video với tham số formats=missing_pot
        if has_1080p_missing_pot:
            print(f"🔍 Đang tải video với tham số formats=missing_pot...")
            try:
                # Đặt thư mục đầu ra
                ydl_opts['outtmpl'] = os.path.join(self.temp_dir, '%(id)s.%(ext)s')
                
                # Tải video
                with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                    info = ydl.extract_info(self.url, download=True)
                    
                    # Lấy đường dẫn đến file đã tải
                    if info:
                        downloaded_file = os.path.join(self.temp_dir, f"{info['id']}.mp4")
                        
                        # Kiểm tra xem file đã tải có tồn tại không
                        if os.path.exists(downloaded_file):
                            # Đặt tên file đầu ra
                            if output_filename:
                                output_file = os.path.join(self.output_dir, output_filename)
                            else:
                                # Sử dụng tiêu đề video làm tên file
                                safe_title = re.sub(r'[\\/*?:"<>|]', '_', info.get('title', 'video'))
                                output_file = os.path.join(self.output_dir, f"{safe_title}.mp4")
                            
                            # Di chuyển file đã tải đến thư mục đầu ra
                            os.rename(downloaded_file, output_file)
                            
                            # Đánh dấu URL đã hoàn thành
                            self._mark_url_completed(self.url)
                            
                            print(f"✅ Đã tải thành công video với tham số formats=missing_pot")
                            
                            # Trả về thông tin video
                            return {
                                'title': info.get('title', 'Unknown Title'),
                                'channel': info.get('channel', 'Unknown Channel'),
                                'duration': info.get('duration', 0),
                                'width': info.get('width', 0),
                                'height': info.get('height', 0),
                                'fps': info.get('fps', 0),
                                'file_path': output_file,
                                'tags': info.get('tags', []),
                                'categories': info.get('categories', []),
                                'description': info.get('description', ''),
                                'upload_date': info.get('upload_date', ''),
                                'view_count': info.get('view_count', 0),
                                'like_count': info.get('like_count', 0),
                                'dislike_count': info.get('dislike_count', 0),
                                'comment_count': info.get('comment_count', 0),
                                'is_live': info.get('is_live', False),
                                'was_live': info.get('was_live', False),
                                'live_status': info.get('live_status', 'not_live'),
                                'thumbnail': info.get('thumbnail', ''),
                                'thumbnails': info.get('thumbnails', []),
                                'id': info.get('id', ''),
                                'extractor': info.get('extractor', ''),
                                'extractor_key': info.get('extractor_key', ''),
                                'webpage_url': info.get('webpage_url', ''),
                                'webpage_url_basename': info.get('webpage_url_basename', ''),
                                'webpage_url_domain': info.get('webpage_url_domain', ''),
                            }
                        else:
                            logging.error(f"File đã tải không tồn tại: {downloaded_file}")
                            self._mark_url_failed(self.url)
                            return {}
                    else:
                        logging.error(f"Không thể tải video từ URL: {self.url}")
                        self._mark_url_failed(self.url)
                        return {}
            except Exception as e:
                logging.error(f"Lỗi khi tải video với tham số formats=missing_pot: {str(e)}")
                traceback.print_exc()
                self._mark_url_failed(self.url)
                return {}
        
        # Nếu không thể tải với tham số formats=missing_pot, thử tải với tham số mặc định
        print(f"🔍 Đang tải video với tham số mặc định...")
        try:
            # Đặt thư mục đầu ra
            ydl_opts['outtmpl'] = os.path.join(self.temp_dir, '%(id)s.%(ext)s')
            
            # Xóa tham số formats=missing_pot
            if 'extractor_args' in ydl_opts and 'youtube' in ydl_opts['extractor_args'] and 'formats' in ydl_opts['extractor_args']['youtube']:
                del ydl_opts['extractor_args']['youtube']['formats']
            
            # Tải video
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(self.url, download=True)
                
                # Lấy đường dẫn đến file đã tải
                if info:
                    downloaded_file = os.path.join(self.temp_dir, f"{info['id']}.mp4")
                    
                    # Kiểm tra xem file đã tải có tồn tại không
                    if os.path.exists(downloaded_file):
                        # Đặt tên file đầu ra
                        if output_filename:
                            output_file = os.path.join(self.output_dir, output_filename)
                        else:
                            # Sử dụng tiêu đề video làm tên file
                            safe_title = re.sub(r'[\\/*?:"<>|]', '_', info.get('title', 'video'))
                            output_file = os.path.join(self.output_dir, f"{safe_title}.mp4")
                        
                        # Di chuyển file đã tải đến thư mục đầu ra
                        os.rename(downloaded_file, output_file)
                        
                        # Đánh dấu URL đã hoàn thành
                        self._mark_url_completed(self.url)
                        
                        print(f"✅ Đã tải thành công video với tham số mặc định")
                        
                        # Trả về thông tin video
                        return {
                            'title': info.get('title', 'Unknown Title'),
                            'channel': info.get('channel', 'Unknown Channel'),
                            'duration': info.get('duration', 0),
                            'width': info.get('width', 0),
                            'height': info.get('height', 0),
                            'fps': info.get('fps', 0),
                            'file_path': output_file,
                            'tags': info.get('tags', []),
                            'categories': info.get('categories', []),
                            'description': info.get('description', ''),
                            'upload_date': info.get('upload_date', ''),
                            'view_count': info.get('view_count', 0),
                            'like_count': info.get('like_count', 0),
                            'dislike_count': info.get('dislike_count', 0),
                            'comment_count': info.get('comment_count', 0),
                            'is_live': info.get('is_live', False),
                            'was_live': info.get('was_live', False),
                            'live_status': info.get('live_status', 'not_live'),
                            'thumbnail': info.get('thumbnail', ''),
                            'thumbnails': info.get('thumbnails', []),
                            'id': info.get('id', ''),
                            'extractor': info.get('extractor', ''),
                            'extractor_key': info.get('extractor_key', ''),
                            'webpage_url': info.get('webpage_url', ''),
                            'webpage_url_basename': info.get('webpage_url_basename', ''),
                            'webpage_url_domain': info.get('webpage_url_domain', ''),
                        }
                    else:
                        logging.error(f"File đã tải không tồn tại: {downloaded_file}")
                        self._mark_url_failed(self.url)
                        return {}
                else:
                    logging.error(f"Không thể tải video từ URL: {self.url}")
                    self._mark_url_failed(self.url)
                    return {}
        except Exception as e:
            logging.error(f"Lỗi khi tải video với tham số mặc định: {str(e)}")
            traceback.print_exc()
            self._mark_url_failed(self.url)
            return {}
