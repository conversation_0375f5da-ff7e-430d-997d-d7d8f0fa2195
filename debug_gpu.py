#!/usr/bin/env python3
"""
GPU Debug Script - Kiểm tra GPU NVIDIA RTX 3070Ti detection và utilization
"""

import os
import sys
import subprocess
import json
import logging
from typing import Dict, Any

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def check_nvidia_smi():
    """Kiểm tra nvidia-smi"""
    print("🔍 Checking nvidia-smi...")
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ nvidia-smi available")
            print(f"Output:\n{result.stdout}")
            return True
        else:
            print(f"❌ nvidia-smi failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ nvidia-smi error: {e}")
        return False

def check_ffmpeg_nvenc():
    """Kiểm tra FFmpeg NVENC support"""
    print("\n🔍 Checking FFmpeg NVENC support...")
    try:
        from src.core.utils import get_ffmpeg_path
        ffmpeg_path = get_ffmpeg_path()
        
        result = subprocess.run([
            ffmpeg_path, '-hide_banner', '-encoders'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            if 'h264_nvenc' in result.stdout:
                print("✅ h264_nvenc encoder available")
                return True
            else:
                print("❌ h264_nvenc encoder NOT available")
                print("Available encoders:")
                for line in result.stdout.split('\n'):
                    if 'h264' in line.lower():
                        print(f"  {line}")
                return False
        else:
            print(f"❌ FFmpeg encoders check failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ FFmpeg check error: {e}")
        return False

def check_gpu_detection():
    """Kiểm tra GPU detection trong script"""
    print("\n🔍 Checking GPU detection in script...")
    try:
        from src.utils.gpu_optimizer import get_gpu_info
        
        gpu_info = get_gpu_info()
        print(f"GPU Info: {json.dumps(gpu_info, indent=2)}")
        
        if gpu_info.get('type') == 'nvidia' and gpu_info.get('nvenc_available'):
            print("✅ GPU detection successful")
            return True
        else:
            print("❌ GPU detection failed or NVENC not available")
            return False
    except Exception as e:
        print(f"❌ GPU detection error: {e}")
        return False

def check_config_settings():
    """Kiểm tra config settings"""
    print("\n🔍 Checking config settings...")
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        use_gpu = config.get('processing', {}).get('use_gpu', False)
        print(f"use_gpu setting: {use_gpu}")
        
        if use_gpu:
            print("✅ GPU enabled in config")
            return True
        else:
            print("❌ GPU disabled in config")
            return False
    except Exception as e:
        print(f"❌ Config check error: {e}")
        return False

def test_gpu_encoding():
    """Test GPU encoding với một video nhỏ"""
    print("\n🔍 Testing GPU encoding...")
    try:
        from src.core.utils import get_ffmpeg_path
        ffmpeg_path = get_ffmpeg_path()
        
        # Tạo test video 5 giây
        test_input = "test_input.mp4"
        test_output = "test_gpu_output.mp4"
        
        # Tạo test video
        create_cmd = [
            ffmpeg_path, '-f', 'lavfi', '-i', 'testsrc=duration=5:size=1920x1080:rate=30',
            '-c:v', 'libx264', '-t', '5', test_input
        ]
        
        print("Creating test video...")
        result = subprocess.run(create_cmd, capture_output=True, text=True, timeout=30)
        if result.returncode != 0:
            print(f"❌ Failed to create test video: {result.stderr}")
            return False
        
        # Test GPU encoding
        gpu_cmd = [
            ffmpeg_path, '-y', '-i', test_input,
            '-c:v', 'h264_nvenc', '-preset', 'fast', '-cq', '23',
            test_output
        ]
        
        print("Testing GPU encoding...")
        result = subprocess.run(gpu_cmd, capture_output=True, text=True, timeout=30)
        
        # Cleanup
        for f in [test_input, test_output]:
            if os.path.exists(f):
                os.remove(f)
        
        if result.returncode == 0:
            print("✅ GPU encoding test successful")
            return True
        else:
            print(f"❌ GPU encoding test failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ GPU encoding test error: {e}")
        return False

def main():
    """Main debug function"""
    print("🚀 GPU NVIDIA RTX 3070Ti Debug Script")
    print("=" * 50)
    
    checks = [
        ("NVIDIA SMI", check_nvidia_smi),
        ("FFmpeg NVENC", check_ffmpeg_nvenc),
        ("GPU Detection", check_gpu_detection),
        ("Config Settings", check_config_settings),
        ("GPU Encoding Test", test_gpu_encoding)
    ]
    
    results = {}
    for name, check_func in checks:
        try:
            results[name] = check_func()
        except Exception as e:
            print(f"❌ {name} check failed with exception: {e}")
            results[name] = False
    
    print("\n" + "=" * 50)
    print("📊 SUMMARY:")
    for name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {name}: {status}")
    
    all_passed = all(results.values())
    if all_passed:
        print("\n🎉 All checks passed! GPU should be working.")
    else:
        print("\n⚠️ Some checks failed. GPU may not be utilized properly.")
        
        # Recommendations
        print("\n💡 RECOMMENDATIONS:")
        if not results.get("NVIDIA SMI"):
            print("  - Install NVIDIA drivers")
            print("  - Check if GPU is properly connected")
        if not results.get("FFmpeg NVENC"):
            print("  - Install FFmpeg with NVENC support")
            print("  - Check FFmpeg build configuration")
        if not results.get("GPU Detection"):
            print("  - Check GPU detection logic in script")
        if not results.get("Config Settings"):
            print("  - Enable GPU in config.json")
        if not results.get("GPU Encoding Test"):
            print("  - Check GPU drivers and FFmpeg compatibility")

if __name__ == "__main__":
    main()
