#!/usr/bin/env python3
"""
Monitor GPU usage during video processing
"""
import subprocess
import time
import threading
import sys
from datetime import datetime

def get_gpu_stats():
    """Get current GPU statistics"""
    try:
        result = subprocess.run([
            'nvidia-smi', 
            '--query-gpu=utilization.gpu,utilization.memory,memory.used,memory.total,temperature.gpu,power.draw',
            '--format=csv,noheader,nounits'
        ], capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0:
            values = result.stdout.strip().split(', ')
            return {
                'gpu_util': int(values[0]),
                'mem_util': int(values[1]),
                'mem_used': int(values[2]),
                'mem_total': int(values[3]),
                'temperature': int(values[4]),
                'power_draw': float(values[5])
            }
    except Exception as e:
        print(f"Error getting GPU stats: {e}")
    
    return None

def get_cpu_stats():
    """Get current CPU statistics"""
    try:
        import psutil
        return {
            'cpu_percent': psutil.cpu_percent(interval=0.1),
            'memory_percent': psutil.virtual_memory().percent
        }
    except Exception as e:
        return {'cpu_percent': 0, 'memory_percent': 0}

def monitor_gpu(duration=120, interval=1):
    """Monitor GPU for specified duration"""
    print(f"🔍 Monitoring GPU for {duration} seconds (interval: {interval}s)")
    print("=" * 90)
    print(f"{'Time':<12} {'GPU%':<6} {'MEM%':<6} {'VRAM':<12} {'TEMP':<6} {'POWER':<8} {'CPU%':<6} {'Status':<15}")
    print("=" * 90)
    
    start_time = time.time()
    max_gpu_util = 0
    max_mem_util = 0
    avg_gpu_util = 0
    avg_cpu_util = 0
    samples = 0
    gpu_active_time = 0
    
    try:
        while time.time() - start_time < duration:
            gpu_stats = get_gpu_stats()
            cpu_stats = get_cpu_stats()
            
            if gpu_stats and cpu_stats:
                current_time = datetime.now().strftime("%H:%M:%S")
                vram_usage = f"{gpu_stats['mem_used']}/{gpu_stats['mem_total']}MB"
                
                # Determine status
                status = ""
                if gpu_stats['gpu_util'] > 50:
                    status = "🚀 GPU ACTIVE"
                    gpu_active_time += interval
                elif gpu_stats['gpu_util'] > 10:
                    status = "⚡ GPU WORKING"
                    gpu_active_time += interval
                elif cpu_stats['cpu_percent'] > 80:
                    status = "🔥 CPU HEAVY"
                else:
                    status = "💤 IDLE"
                
                print(f"{current_time:<12} "
                      f"{gpu_stats['gpu_util']:<6}% "
                      f"{gpu_stats['mem_util']:<6}% "
                      f"{vram_usage:<12} "
                      f"{gpu_stats['temperature']:<6}°C "
                      f"{gpu_stats['power_draw']:<8.1f}W "
                      f"{cpu_stats['cpu_percent']:<6.1f}% "
                      f"{status:<15}")
                
                # Track statistics
                max_gpu_util = max(max_gpu_util, gpu_stats['gpu_util'])
                max_mem_util = max(max_mem_util, gpu_stats['mem_util'])
                avg_gpu_util += gpu_stats['gpu_util']
                avg_cpu_util += cpu_stats['cpu_percent']
                samples += 1
            
            time.sleep(interval)
            
    except KeyboardInterrupt:
        print("\n⏹️ Monitoring stopped by user")
    
    # Print summary
    if samples > 0:
        print("=" * 90)
        print("📊 MONITORING SUMMARY:")
        print(f"  Duration: {time.time() - start_time:.1f} seconds")
        print(f"  Max GPU Utilization: {max_gpu_util}%")
        print(f"  Max Memory Utilization: {max_mem_util}%")
        print(f"  Average GPU Utilization: {avg_gpu_util/samples:.1f}%")
        print(f"  Average CPU Utilization: {avg_cpu_util/samples:.1f}%")
        print(f"  GPU Active Time: {gpu_active_time:.1f}s ({gpu_active_time/(time.time()-start_time)*100:.1f}%)")
        
        if max_gpu_util < 10:
            print("❌ GPU was barely used - likely using CPU encoding")
        elif max_gpu_util < 50:
            print("⚠️ GPU was lightly used - may have some GPU acceleration")
        else:
            print("✅ GPU was actively used - good GPU acceleration")

def run_video_processing():
    """Run video processing in background"""
    print("🎬 Starting video processing...")
    try:
        result = subprocess.run([
            'python', 'run.py', 
            '--url', 'https://www.youtube.com/watch?v=9bZkp7q19f0',
            '--title', 'GPU Monitor Test'
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ Video processing completed successfully")
        else:
            print(f"❌ Video processing failed: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("⏰ Video processing timed out")
    except Exception as e:
        print(f"❌ Error running video processing: {e}")

if __name__ == "__main__":
    print("🚀 GPU Usage Monitor During Video Processing")
    print("=" * 60)
    
    # Start video processing in background
    processing_thread = threading.Thread(target=run_video_processing)
    processing_thread.daemon = True
    processing_thread.start()
    
    # Wait a moment for processing to start
    time.sleep(2)
    
    # Monitor GPU usage
    monitor_gpu(duration=180, interval=2)
    
    # Wait for processing to complete
    processing_thread.join(timeout=10)
    
    print("\n🏁 Monitoring complete!")
