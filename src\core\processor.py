"""
Video processor module for the video processor application.
"""

import os
import re
import subprocess
import logging
import random
import shutil
from typing import Optional, Dict, Any, List, Tuple, Callable
from concurrent.futures import ProcessPoolExecutor

from src.core.exceptions import ProcessingError, FFmpegError
from src.core.models import VideoInfo, ProcessingOptions
from src.core.utils import get_ffmpeg_path, create_temp_directory, cleanup_temp_directory, check_video_resolution, sanitize_filename, extract_high_value_keywords

logger = logging.getLogger("video_processor.processor")


class VideoProcessor:
    """Video processor for processing downloaded videos."""

    def __init__(self, video_path: str, video_info: Optional[VideoInfo] = None, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the video processor.

        Args:
            video_path: Path to the video file.
            video_info: VideoInfo object with video information.
            config: Configuration dictionary.
        """
        self.video_path = video_path
        self.video_info = video_info
        self.config = config or {}
        self.temp_dir = None
        self.output_path = None

        # Get processing options from config
        processing_config = self.config.get("processing", {})
        self.options = ProcessingOptions(
            add_title=False,  # Vô hiệu hóa thêm tiêu đề
            add_channel_name=False,  # Vô hiệu hóa thêm tên kênh
            title_font_size=processing_config.get("title_font_size", 70),
            title_font=processing_config.get("title_font", "Impact"),
            channel_font=processing_config.get("channel_font", "Impact"),
            max_title_width=processing_config.get("max_title_width", 864),
            title_position=processing_config.get("title_position", "center"),
            channel_position=processing_config.get("channel_position", "bottom"),
            title_color=processing_config.get("title_color", "white"),
            title_background=processing_config.get("title_background", False),
            title_background_color=processing_config.get("title_background_color", "black"),
            max_segments=processing_config.get("max_segments", 3),
            min_segment_duration=processing_config.get("min_segment_duration", 90),
            max_segment_duration=processing_config.get("max_segment_duration", 150),
            output_format=processing_config.get("output_format", "mp4"),
            max_bitrate=processing_config.get("max_bitrate", 6000),
            max_fps=processing_config.get("max_fps", 30)
        )

        # Get max parallel processes
        self.max_parallel_processes = processing_config.get("max_parallel_processes", 2)

        # Validate video path
        if not os.path.exists(video_path):
            logger.error(f"Video file not found: {video_path}")
            raise ValueError(f"Video file not found: {video_path}")

    def process(self, output_dir: Optional[str] = None, progress_callback: Optional[Callable[[float], None]] = None) -> str:
        """
        Process the video.

        Args:
            output_dir: Directory to save the processed video. If None, uses current directory.
            progress_callback: Callback function for progress updates.

        Returns:
            Path to the processed video file.

        Raises:
            ProcessingError: If video processing fails.
        """
        try:
            # Create output directory if it doesn't exist
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)

            # Create temporary directory
            self.temp_dir = create_temp_directory()

            # Get video info including vertical detection
            duration, resolution, is_vertical = self._get_video_info()
            logger.info(f"Video duration: {duration}s, resolution: {resolution}, vertical: {is_vertical}")

            # Store vertical status for use in other methods
            self.is_vertical = is_vertical

            if is_vertical:
                # For vertical videos (9:16), process the whole video without segmenting
                logger.info("Processing vertical video without segmenting")

                # For vertical videos, we want to use a different naming scheme
                # Use video title if available, otherwise use filename
                if self.video_info and self.video_info.title:
                    # Use the actual video title from video_info
                    from src.core.utils import sanitize_filename
                    base_filename = sanitize_filename(self.video_info.title)
                    # Không giới hạn độ dài tiêu đề
                else:
                    # Extract original filename without extension
                    original_filename = os.path.splitext(os.path.basename(self.video_path))[0]

                    # Remove any temporary ID or part numbers from the filename
                    # First, remove "_temp" suffix that might be in the original filename
                    original_filename = original_filename.replace("_temp", "")

                    # Remove any existing "Part X" text that might be in the original filename
                    base_filename = re.sub(r' - Part \d+', '', original_filename)

                # Extract keywords from video info if available
                keywords = []
                if self.video_info:
                    # Extract keywords from title and tags
                    title = self.video_info.title if self.video_info else os.path.basename(self.video_path)
                    tags = self.video_info.tags if self.video_info and hasattr(self.video_info, 'tags') else []
                    keywords = extract_high_value_keywords(title, tags)
                # Không thêm các hashtag mặc định cho video dọc

                # For vertical videos, we want to use a different naming scheme
                # Instead of using generate_segment_filename, we'll create our own format
                # to avoid any "Part X" text in the filename

                # Sử dụng tiêu đề gốc của video nếu có
                if self.video_info and self.video_info.title:
                    # Sử dụng tiêu đề gốc của video
                    original_title = self.video_info.title

                    # Bỏ các ký tự đặc biệt ra khỏi tiêu đề video
                    # Danh sách các ký tự đặc biệt cần bỏ
                    special_chars = ["<", ">", ":", "\"", "/", "\\", "|", "*", "?", "!", "@", "#",
                                    "$", "%", "^", "&", "(", ")", "+", "=", "[", "]", "{", "}",
                                    ";", "'", ",", "."]

                    # Bỏ các ký tự đặc biệt ra khỏi tiêu đề video
                    clean_title = original_title
                    for char in special_chars:
                        clean_title = clean_title.replace(char, "")

                    # Loại bỏ khoảng trắng thừa
                    clean_title = " ".join(clean_title.split())

                    # Create hashtag string
                    hashtags = " ".join([f"#{keyword}" for keyword in keywords[:5]])  # Limit to 5 hashtags

                    # Nếu có hashtags thì thêm khoảng trắng, nếu không thì không thêm
                    if hashtags:
                        desired_filename = f"{clean_title} {hashtags}"
                    else:
                        desired_filename = clean_title

                    # Lưu tên file an toàn không chứa ký tự đặc biệt
                    safe_desired_filename = desired_filename

                    # Sử dụng tên file an toàn
                    temp_filename = safe_desired_filename
                else:
                    # Nếu không có tiêu đề, sử dụng tên file gốc
                    # Create hashtag string
                    hashtags = " ".join([f"#{keyword}" for keyword in keywords[:5]])  # Limit to 5 hashtags

                    # Create filename with format: filename #keyword1 #keyword2 #keyword3
                    clean_filename = base_filename.replace(" - Part 1", "").replace(" - Part 2", "").replace(" - Part 3", "")

                    # Bỏ các ký tự đặc biệt ra khỏi tên file
                    # Danh sách các ký tự đặc biệt cần bỏ
                    special_chars = ["<", ">", ":", "\"", "/", "\\", "|", "*", "?", "!", "@", "#",
                                    "$", "%", "^", "&", "(", ")", "+", "=", "[", "]", "{", "}",
                                    ";", "'", ",", "."]

                    # Bỏ các ký tự đặc biệt ra khỏi tên file
                    for char in special_chars:
                        clean_filename = clean_filename.replace(char, "")

                    # Loại bỏ khoảng trắng thừa
                    clean_filename = " ".join(clean_filename.split())

                    # Nếu có hashtags thì thêm khoảng trắng, nếu không thì không thêm
                    if hashtags:
                        desired_filename = f"{clean_filename} {hashtags}"
                    else:
                        desired_filename = clean_filename

                    # Lưu tên file an toàn không chứa ký tự đặc biệt
                    safe_desired_filename = desired_filename

                    # Sử dụng tên file an toàn
                    temp_filename = safe_desired_filename

                # Create the final output path
                if output_dir:
                    # Tạo đường dẫn cho file tạm thời
                    temp_output_path = os.path.join(output_dir, f"{temp_filename}.{self.options.output_format}")
                    # Tạo đường dẫn cho file cuối cùng
                    final_output_path = os.path.join(output_dir, f"{safe_desired_filename}.{self.options.output_format}")
                else:
                    # Tạo đường dẫn cho file tạm thời
                    temp_output_path = os.path.join(os.path.dirname(self.video_path), f"{temp_filename}.{self.options.output_format}")
                    # Tạo đường dẫn cho file cuối cùng
                    final_output_path = os.path.join(os.path.dirname(self.video_path), f"{safe_desired_filename}.{self.options.output_format}")

                # Sử dụng đường dẫn tạm thời cho FFmpeg
                output_path = temp_output_path

                # Log that we're processing a vertical video
                logger.info(f"Processing vertical video without segmenting: {os.path.basename(self.video_path)} -> {os.path.basename(final_output_path)}")

                # Process the vertical video with only copyright effects and watermark
                processed_video = self._process_vertical_video(
                    self.video_path,
                    output_path,
                    self.options,
                    self.video_info.uploader if self.video_info else None
                )

                # Return the directory containing the processed video
                self.output_path = os.path.dirname(processed_video)
                logger.info(f"Processed vertical video: {processed_video}")
            else:
                # For horizontal videos, use the original segmenting approach
                # Determine segments
                segments = self._determine_segments(duration)
                logger.info(f"Determined {len(segments)} segments")

                # Process segments in parallel
                processed_segments = self._process_segments(segments, progress_callback)

                # Combine segments
                output_filename = self._generate_output_filename()
                if output_dir:
                    output_filename = os.path.join(output_dir, output_filename)

                self.output_path = self._combine_segments(processed_segments, output_filename)
                logger.info(f"Combined segments into: {self.output_path}")

            return self.output_path

        except Exception as e:
            logger.error(f"Error processing video: {e}")
            raise ProcessingError(f"Error processing video: {e}")

        finally:
            # Clean up temporary directory
            if self.temp_dir:
                cleanup_temp_directory(self.temp_dir)

    def _get_video_info(self) -> Tuple[float, Tuple[int, int], bool]:
        """
        Get video duration, resolution, and determine if it's a vertical video.

        Returns:
            Tuple of (duration, (width, height), is_vertical).

        Raises:
            ProcessingError: If video information cannot be retrieved.
        """
        try:
            ffprobe_path = os.path.join(os.path.dirname(get_ffmpeg_path()), "ffprobe.exe")
            if not os.path.exists(ffprobe_path):
                ffprobe_path = "ffprobe"

            # Get duration
            cmd_duration = [
                ffprobe_path,
                "-v", "error",
                "-show_entries", "format=duration",
                "-of", "default=noprint_wrappers=1:nokey=1",
                self.video_path
            ]

            result = subprocess.run(
                cmd_duration,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=False,
                encoding='utf-8',
                errors='replace'
            )

            if result.returncode != 0:
                logger.error(f"Failed to get video duration: {result.stderr}")
                raise ProcessingError(f"Failed to get video duration: {result.stderr}")

            duration = float(result.stdout.strip())

            # Get resolution
            resolution = check_video_resolution(self.video_path)
            if not resolution:
                logger.warning("Failed to get video resolution, using default (1920x1080)")
                resolution = (1920, 1080)

            # Determine if it's a vertical video (aspect ratio close to 9:16)
            width, height = resolution
            is_vertical = False

            if width > 0 and height > 0:
                aspect_ratio = width / height
                # Consider it vertical if aspect ratio is close to 9:16 (0.5625) or narrower
                # Allow some tolerance (0.65) to catch videos that are slightly wider than 9:16
                if aspect_ratio <= 0.65:
                    is_vertical = True
                    logger.info(f"Detected vertical video with aspect ratio {aspect_ratio:.4f} ({width}x{height})")

            return duration, resolution, is_vertical

        except Exception as e:
            logger.error(f"Error getting video info: {e}")
            raise ProcessingError(f"Error getting video info: {e}")

    def _determine_segments(self, duration: float) -> List[Tuple[float, float]]:
        """
        Determine video segments to extract with random durations between min_segment_duration and max_segment_duration.
        If the last segment is less than 60 seconds, it will be merged with the previous segment.

        Args:
            duration: Video duration in seconds.

        Returns:
            List of (start_time, end_time) tuples.
        """
        # If video is shorter than min_segment_duration, use the whole video
        if duration <= self.options.min_segment_duration:
            return [(0, duration)]

        # Get segment duration limits in seconds
        min_duration = self.options.min_segment_duration  # 90 seconds (1'30")
        max_duration = self.options.max_segment_duration  # 150 seconds (2'30")
        min_last_segment = 60  # Minimum duration for last segment (1')

        # Calculate segment times with random durations
        segment_times = []
        current_time = 0.0
        remaining_duration = duration

        # Ensure we don't create too many segments
        max_segments = self.options.max_segments

        while remaining_duration > 0 and len(segment_times) < max_segments:
            # If this is the last possible segment or remaining duration is less than minimum,
            # use all remaining duration
            if len(segment_times) == max_segments - 1 or remaining_duration <= min_duration:
                # If we already have segments and the remaining duration is less than min_last_segment,
                # merge with the previous segment
                if segment_times and remaining_duration < min_last_segment:
                    # Remove the last segment
                    last_start, _ = segment_times.pop()
                    # Add a new segment that combines the last segment with the remaining duration
                    segment_times.append((last_start, duration))
                else:
                    # Add the final segment
                    segment_times.append((current_time, duration))
                break

            # Calculate a random segment duration within bounds
            # Ensure it's strictly between min_duration and max_duration
            max_possible = min(max_duration, remaining_duration)
            segment_duration = random.uniform(min_duration, max_possible)

            # Add segment
            segment_times.append((current_time, current_time + segment_duration))

            # Update remaining duration and current time
            remaining_duration -= segment_duration
            current_time += segment_duration

            # Check if the remaining duration is less than min_last_segment
            # If so, and we have at least one segment, extend the current segment
            if remaining_duration < min_last_segment and remaining_duration > 0:
                # Remove the last segment
                last_start, _ = segment_times.pop()
                # Add a new segment that extends to the end
                segment_times.append((last_start, duration))
                break

        # Verify all segments are within the required duration range
        final_segments = []
        for start, end in segment_times:
            segment_duration = end - start

            # Skip segments that are too short
            if segment_duration < min_duration and len(segment_times) > 1:
                continue

            # Ensure segments aren't too long
            if segment_duration > max_duration and start + max_duration <= duration:
                end = start + random.uniform(min_duration, max_duration)

            final_segments.append((start, end))

        # Log the segment information (simplified)
        logger.info(f"Video duration: {duration:.2f}s, Split into {len(final_segments)} segments")

        return final_segments

    def _process_segments(self, segments: List[Tuple[float, float]], progress_callback: Optional[Callable[[float], None]] = None) -> List[str]:
        """
        Process video segments in parallel.

        Args:
            segments: List of (start_time, end_time) tuples.
            progress_callback: Callback function for progress updates.

        Returns:
            List of paths to processed segment files.

        Raises:
            ProcessingError: If segment processing fails.
        """
        from src.core.utils import extract_high_value_keywords, generate_segment_filename, sanitize_filename

        processed_segments = []

        # Get base filename without extension
        if self.video_info and self.video_info.title:
            # Sanitize title for filename
            base_filename = sanitize_filename(self.video_info.title)
            # Không giới hạn độ dài tiêu đề
        else:
            # Extract original filename without extension
            original_filename = os.path.splitext(os.path.basename(self.video_path))[0]

            # Remove any existing "Part X" text that might be in the original filename
            clean_filename = re.sub(r' - Part \d+', '', original_filename)

            base_filename = clean_filename

        # Extract high-value keywords from title and tags
        keywords = []
        if self.video_info:
            keywords = extract_high_value_keywords(
                self.video_info.title,
                self.video_info.tags,
                max_keywords=7  # Increased for better TikTok SEO
            )

        # Process segments in parallel with optimized settings for GPU
        # Use fewer parallel processes when using GPU to avoid overloading it
        max_workers = 2 if self.options.use_gpu and VideoProcessor._check_nvidia_gpu() else self.max_parallel_processes

        # Log the number of parallel processes
        logger.info(f"Processing segments with {max_workers} parallel processes")

        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            futures = []

            for i, (start_time, end_time) in enumerate(segments):
                # Generate segment filename with format: filename - Part X #keyword1 #keyword2 #keyword3
                # Pass is_vertical=False since this method is only called for horizontal videos
                segment_filename = generate_segment_filename(base_filename, i+1, keywords, is_vertical=False)
                segment_path = os.path.join(self.temp_dir, f"{segment_filename}.mp4")

                future = executor.submit(
                    self._process_segment,
                    self.video_path,
                    segment_path,
                    start_time,
                    end_time,
                    self.options,
                    self.video_info.uploader if self.video_info else None
                )
                futures.append((future, i))

            # Wait for all futures to complete
            total_segments = len(segments)
            completed_segments = 0

            for future, i in futures:
                try:
                    segment_path = future.result()
                    processed_segments.append((i, segment_path))
                    completed_segments += 1

                    # Update progress
                    if progress_callback:
                        progress_callback(completed_segments / total_segments * 100)

                except Exception as e:
                    logger.error(f"Error processing segment {i}: {e}")
                    raise ProcessingError(f"Error processing segment {i}: {e}")

        # Sort segments by index
        processed_segments.sort()
        return [path for _, path in processed_segments]

    def _process_vertical_video(self, video_path: str, output_path: str,
                          options: ProcessingOptions, _: Optional[str] = None) -> str:
        """
        Process a vertical video (9:16) without segmenting, applying copyright effects.
        For vertical videos, we don't crop to 1:1 aspect ratio, don't add title, and don't split into parts.
        We apply copyright circumvention effects only.

        Args:
            video_path: Path to the input video.
            output_path: Path to save the processed video.
            options: Processing options.
            _: Không sử dụng (đã loại bỏ watermark).

        Returns:
            Path to the processed video file.

        Raises:
            ProcessingError: If video processing fails.
        """
        try:
            # Sử dụng FFmpeg để xử lý video dọc với hiệu ứng chống bản quyền và watermark
            logger.info(f"Processing vertical video: {video_path} -> {output_path}")
            ffmpeg_path = get_ffmpeg_path()

            # Create a list of possible copyright circumvention effects
            copyright_effects = []

            # Only apply effects if enabled in options
            if options.apply_copyright_effects:
                # 1. Use a simpler approach with random flip intervals
                if options.use_hflip and random.random() < 0.9:  # Increased probability
                    # Generate 2-3 different random intervals
                    num_intervals = random.randint(2, 3)
                    intervals = []
                    for _ in range(num_intervals):
                        # Random interval between 6-15 seconds (for minimum 3 seconds flip time)
                        interval = random.uniform(6, 15)
                        # Random offset to make the pattern less predictable
                        offset = random.uniform(0, interval)
                        intervals.append((interval, offset))

                    # Create hflip filters with different patterns
                    for i, (interval, offset) in enumerate(intervals):
                        # Use a more complex expression to create randomness
                        # This creates a pattern where video flips based on a sine wave with random interval and offset
                        expr = f"gt(sin((t+{offset})/{interval}*PI),0)"
                        copyright_effects.append(f"hflip=enable='{expr}'")

                    # Add a simple constant zoom effect
                    if random.random() < 0.7:  # 70% chance to add zoom
                        zoom_factor = random.uniform(1.01, 1.05)  # 1-5% zoom
                        copyright_effects.append(f"scale=iw*{zoom_factor}:ih*{zoom_factor},crop=iw/({zoom_factor}):ih/({zoom_factor})")

                # 2. Slight color adjustment (without brightness)
                if options.use_color_adjustment and random.random() < 0.8:
                    # Only adjust contrast, saturation and gamma (no brightness)
                    contrast = random.uniform(0.97, 1.02)    # Narrower range
                    saturation = random.uniform(0.97, 1.02)  # Narrower range
                    gamma = random.uniform(0.98, 1.02)       # Add gamma adjustment
                    copyright_effects.append(f"eq=contrast={contrast}:saturation={saturation}:gamma={gamma}")

                # 3. Speed adjustment (0.9x - 1.2x)
                if options.use_speed_adjustment and random.random() < 0.3:
                    # Use weighted random to favor slight adjustments but allow more extreme values
                    if random.random() < 0.7:
                        # 70% chance for slight adjustment (0.95-1.05)
                        speed_factor = random.uniform(0.95, 1.05)
                    else:
                        # 30% chance for more extreme adjustment (0.9-0.95 or 1.05-1.2)
                        if random.random() < 0.5:
                            speed_factor = random.uniform(0.9, 0.95)  # Slower
                        else:
                            speed_factor = random.uniform(1.05, 1.2)  # Faster

                    if abs(speed_factor - 1.0) > 0.01:  # Only apply if difference is noticeable
                        copyright_effects.append(f"setpts={1/speed_factor}*PTS")

                        # Handle audio speed adjustment
                        # FFmpeg's atempo filter only supports 0.5 to 2.0 range
                        if 0.5 <= speed_factor <= 2.0:
                            audio_speed = f"atempo={speed_factor}"
                        else:
                            # For values outside the range, we need to chain multiple atempo filters
                            # But for our range (0.9-1.2) this shouldn't be necessary
                            audio_speed = f"atempo={speed_factor}"
                    else:
                        audio_speed = None
                else:
                    audio_speed = None

            # Get original video fps and bitrate
            try:
                # Use ffprobe to get original video fps and bitrate
                ffprobe_path = os.path.join(os.path.dirname(ffmpeg_path), "ffprobe.exe")
                if not os.path.exists(ffprobe_path):
                    ffprobe_path = "ffprobe"

                # Get fps
                cmd_fps = [
                    ffprobe_path,
                    "-v", "error",
                    "-select_streams", "v:0",
                    "-show_entries", "stream=r_frame_rate",
                    "-of", "default=noprint_wrappers=1:nokey=1",
                    video_path
                ]

                # Import required modules
                from src.utils.subprocess_wrapper import run_ffmpeg_safely

                # Run command with enhanced error handling
                result = run_ffmpeg_safely(cmd_fps, timeout=10)

                original_fps = options.max_fps  # Default to max_fps
                if result.returncode == 0:
                    # Parse fps (format is usually "num/den")
                    fps_str = result.stdout.strip()
                    if "/" in fps_str:
                        num, den = map(int, fps_str.split("/"))
                        if den != 0:
                            original_fps = min(num / den, options.max_fps)
                    else:
                        try:
                            original_fps = min(float(fps_str), options.max_fps)
                        except ValueError:
                            pass

                # Get bitrate
                cmd_bitrate = [
                    ffprobe_path,
                    "-v", "error",
                    "-select_streams", "v:0",
                    "-show_entries", "stream=bit_rate",
                    "-of", "default=noprint_wrappers=1:nokey=1",
                    video_path
                ]

                # Run command with enhanced error handling
                result = run_ffmpeg_safely(cmd_bitrate, timeout=10)

                original_bitrate = options.max_bitrate * 1000  # Default to max_bitrate in bits/s
                if result.returncode == 0 and result.stdout.strip():
                    try:
                        original_bitrate = min(int(result.stdout.strip()), options.max_bitrate * 1000)
                    except ValueError:
                        pass

                # Convert back to kbps for FFmpeg
                original_bitrate_kbps = original_bitrate // 1000

            except Exception as e:
                logger.warning(f"Could not get original video fps and bitrate: {e}")
                original_fps = options.max_fps
                original_bitrate_kbps = options.max_bitrate

            # Create a list of video filters
            video_filters = []

            # Start with fps filter
            video_filters.append(f"fps={original_fps}")

            # Add copyright effects if enabled
            if options.apply_copyright_effects:
                for effect in copyright_effects:
                    video_filters.append(effect)

            # Fix color issues - prevent washed out/overexposed look
            # Use color correction to ensure proper brightness and contrast
            video_filters.append("eq=gamma=1.0:contrast=1.0:brightness=0.0:saturation=1.0")

            # Không thêm watermark tên kênh

            # Build FFmpeg command
            cmd = [
                ffmpeg_path,
                "-y"  # Overwrite output file if it exists
            ]

            # Import GPU optimizer
            from src.utils.gpu_optimizer import get_gpu_info, get_optimal_ffmpeg_params

            # Get GPU info
            gpu_info = get_gpu_info()

            # Get optimal FFmpeg parameters
            if options.use_gpu and gpu_info["type"] == "nvidia" and gpu_info["nvenc_available"]:
                # Use GPU acceleration with optimized parameters
                ffmpeg_params = get_optimal_ffmpeg_params(
                    gpu_info,
                    preset="balanced",
                    target_bitrate=options.max_bitrate
                )

                # Add input parameters
                cmd.extend(ffmpeg_params["input"])
                cmd.extend(["-i", video_path])

                # Store output parameters for later
                gpu_output_params = ffmpeg_params["output"]
            else:
                # Standard CPU input without hardware acceleration
                cmd.extend(["-i", video_path])
                gpu_output_params = None

            # Check if input is AV1 codec (which NVENC doesn't support directly)
            is_av1 = False
            try:
                # Use ffprobe to check video codec
                ffprobe_path = os.path.join(os.path.dirname(ffmpeg_path), "ffprobe.exe")
                if not os.path.exists(ffprobe_path):
                    ffprobe_path = "ffprobe"

                cmd_codec = [
                    ffprobe_path,
                    "-v", "error",
                    "-select_streams", "v:0",
                    "-show_entries", "stream=codec_name",
                    "-of", "default=noprint_wrappers=1:nokey=1",
                    video_path
                ]

                # Run command with enhanced error handling
                result = run_ffmpeg_safely(cmd_codec, timeout=10)

                if result.returncode == 0 and "av1" in result.stdout.lower():
                    is_av1 = True
                    logger.info("Detected AV1 codec, using CPU for decoding")
            except Exception as e:
                logger.warning(f"Could not check video codec: {e}")

            # Set output codec based on GPU availability
            if options.use_gpu and gpu_info["type"] == "nvidia" and gpu_info["nvenc_available"] and not is_av1:
                # Use optimized GPU parameters from gpu_optimizer
                if gpu_output_params:
                    cmd.extend(gpu_output_params)
                else:
                    # Fallback to default NVIDIA settings if gpu_output_params is None
                    cmd.extend([
                        "-c:v", "h264_nvenc",
                        "-preset", "p2",          # Balanced preset (p2 instead of p1) for better speed
                        "-tune", "hq",            # Keep high quality tuning
                        "-rc", "vbr",             # Standard variable bitrate (instead of vbr_hq) for better speed
                        "-cq", "23",              # Slightly higher quality factor (23 instead of 20) for better speed
                        "-b:v", f"{original_bitrate_kbps}k",
                        "-maxrate", f"{original_bitrate_kbps * 1.2}k",  # Keep 20% higher bitrate for quality
                        "-bufsize", f"{original_bitrate_kbps * 2}k",
                        # Add additional NVENC parameters for better GPU utilization
                        "-spatial-aq", "1",       # Enable spatial adaptive quantization
                        "-temporal-aq", "1",      # Enable temporal adaptive quantization
                        "-zerolatency", "1",      # Enable zero latency mode for faster processing
                        # Color settings to prevent washed out look
                        "-color_primaries", "bt709",
                        "-color_trc", "bt709",
                        "-colorspace", "bt709"
                    ])
            else:
                # Get optimal CPU parameters from gpu_optimizer
                cpu_params = get_optimal_ffmpeg_params(
                    {"type": "cpu"},
                    preset="balanced",
                    target_bitrate=original_bitrate_kbps
                )

                # Use optimized CPU parameters
                cmd.extend(cpu_params["output"])

            # Check if we need to use filter_complex (for audio speed adjustment)
            if audio_speed:
                # Use filter_complex for both video and audio
                cmd.extend([
                    "-filter_complex",
                    f"[0:v]{','.join(video_filters)}[v];[0:a]{audio_speed}[a]",
                    "-map", "[v]",
                    "-map", "[a]"
                ])
            else:
                # Use simple -vf for video only
                cmd.extend([
                    "-vf", ",".join(video_filters)
                ])

            # Add optimized audio settings
            cmd.extend([
                "-c:a", "aac",
                "-b:a", "128k",
                "-ar", "44100",
                "-ac", "2",
                "-movflags", "+faststart",  # Optimize for web streaming
                "-max_muxing_queue_size", "1024"  # Increase muxing queue size for better performance
            ])

            # Add output path
            cmd.append(output_path)

            # Run FFmpeg with enhanced error handling
            try:
                result = run_ffmpeg_safely(cmd, timeout=300)  # 5 minute timeout
                logger.info(f"FFmpeg completed with return code {result.returncode}")
            except Exception as e:
                logger.error(f"FFmpeg error: {e}")
                raise ProcessingError(f"FFmpeg error: {e}")

            if result.returncode != 0:
                raise FFmpegError(f"FFmpeg error: {result.stderr}")

            if not os.path.exists(output_path):
                raise ProcessingError(f"Output file not created: {output_path}")

            return output_path

        except Exception as e:
            logger.error(f"Error processing vertical video: {e}")
            raise ProcessingError(f"Error processing vertical video: {e}")

    @staticmethod
    def _process_segment(video_path: str, output_path: str, start_time: float, end_time: float,
                         options: ProcessingOptions, _: Optional[str] = None) -> str:
        """
        Process a video segment.

        Args:
            video_path: Path to the input video.
            output_path: Path to save the processed segment.
            start_time: Start time in seconds.
            end_time: End time in seconds.
            options: Processing options.
            _: Không sử dụng (đã loại bỏ watermark).

        Returns:
            Path to the processed segment file.

        Raises:
            ProcessingError: If segment processing fails.
        """
        try:
            # Import required modules
            from src.utils.subprocess_wrapper import run_ffmpeg_safely
            from src.utils.gpu_optimizer import get_gpu_info, get_optimal_ffmpeg_params

            ffmpeg_path = get_ffmpeg_path()

            # Get GPU info
            try:
                gpu_info = get_gpu_info()
                logger.info(f"GPU info: {gpu_info['name']}, NVENC available: {gpu_info['nvenc_available']}")
            except Exception as e:
                logger.warning(f"Error getting GPU info: {e}")
                # Fallback to basic GPU info
                gpu_info = {
                    "type": "unknown",
                    "name": "Unknown GPU",
                    "nvenc_available": False
                }

            # Build FFmpeg command with copyright circumvention effects
            # Create a list of possible copyright circumvention effects
            copyright_effects = []
            audio_speed = None

            # Only apply effects if enabled in options
            if options.apply_copyright_effects:
                # 1. Use a simpler approach with random flip intervals
                if options.use_hflip and random.random() < 0.9:  # Increased probability
                    # Create a more random pattern by using multiple hflip filters with different intervals
                    # This creates a less predictable pattern than a single interval

                    # Generate 2-3 different random intervals
                    num_intervals = random.randint(2, 3)
                    intervals = []
                    for _ in range(num_intervals):
                        # Random interval between 6-15 seconds (for minimum 3 seconds flip time)
                        interval = random.uniform(6, 15)
                        # Random offset to make the pattern less predictable
                        offset = random.uniform(0, interval)
                        intervals.append((interval, offset))

                    # Create hflip filters with different patterns
                    for i, (interval, offset) in enumerate(intervals):
                        # Use a more complex expression to create randomness
                        # This creates a pattern where video flips based on a sine wave with random interval and offset
                        expr = f"gt(sin((t+{offset})/{interval}*PI),0)"
                        copyright_effects.append(f"hflip=enable='{expr}'")

                    # Add a simple constant zoom effect
                    if random.random() < 0.7:  # 70% chance to add zoom
                        zoom_factor = random.uniform(1.01, 1.05)  # 1-5% zoom
                        copyright_effects.append(f"scale=iw*{zoom_factor}:ih*{zoom_factor},crop=iw/({zoom_factor}):ih/({zoom_factor})")

                    # Add a slight rotation for additional copyright protection
                    if random.random() < 0.3:
                        rotation = random.uniform(0.5, 1.5)
                        copyright_effects.append(f"rotate={rotation}*PI/180")

                # 2. Slight color adjustment (without brightness)
                if options.use_color_adjustment and random.random() < 0.8:
                    # Only adjust contrast, saturation and gamma (no brightness)
                    contrast = random.uniform(0.97, 1.02)    # Narrower range
                    saturation = random.uniform(0.97, 1.02)  # Narrower range
                    gamma = random.uniform(0.98, 1.02)       # Add gamma adjustment
                    copyright_effects.append(f"eq=contrast={contrast}:saturation={saturation}:gamma={gamma}")

                # 3. Slight crop (1-2% from edges) - ensure dimensions are even
                if options.use_zoom and random.random() < 0.6:
                    zoom_factor = random.uniform(0.01, 0.02)
                    copyright_effects.append(f"crop=trunc((in_w*(1-{zoom_factor}*2))/2)*2:trunc((in_h*(1-{zoom_factor}*2))/2)*2:trunc(in_w*{zoom_factor}/2)*2:trunc(in_h*{zoom_factor}/2)*2")

                # 4. Add slight border (ensure even dimensions)
                if options.use_border and random.random() < 0.4:
                    border_size = random.randint(1, 3) * 2  # Ensure even border size
                    copyright_effects.append(f"pad=width=in_w+{border_size}:height=in_h+{border_size}:x={border_size}/2:y={border_size}/2:color=black")

                # 6. Speed adjustment (0.9x - 1.2x)
                if options.use_speed_adjustment and random.random() < 0.3:
                    # Increased speed range for more noticeable effect
                    # Use weighted random to favor slight adjustments but allow more extreme values
                    if random.random() < 0.7:
                        # 70% chance for slight adjustment (0.95-1.05)
                        speed_factor = random.uniform(0.95, 1.05)
                    else:
                        # 30% chance for more extreme adjustment (0.9-0.95 or 1.05-1.2)
                        if random.random() < 0.5:
                            speed_factor = random.uniform(0.9, 0.95)  # Slower
                        else:
                            speed_factor = random.uniform(1.05, 1.2)  # Faster

                    if abs(speed_factor - 1.0) > 0.01:  # Only apply if difference is noticeable
                        copyright_effects.append(f"setpts={1/speed_factor}*PTS")

                        # Handle audio speed adjustment
                        # FFmpeg's atempo filter only supports 0.5 to 2.0 range
                        if 0.5 <= speed_factor <= 2.0:
                            audio_speed = f"atempo={speed_factor}"
                        else:
                            # For values outside the range, we need to chain multiple atempo filters
                            # But for our range (0.9-1.2) this shouldn't be necessary
                            audio_speed = f"atempo={speed_factor}"
                    else:
                        audio_speed = None
                else:
                    audio_speed = None

            # Get original video fps and bitrate first
            original_fps = options.max_fps  # Default to max_fps
            original_bitrate_kbps = options.max_bitrate  # Default to max_bitrate

            try:
                # Use ffprobe to get original video fps and bitrate
                ffprobe_path = os.path.join(os.path.dirname(ffmpeg_path), "ffprobe.exe")
                if not os.path.exists(ffprobe_path):
                    ffprobe_path = "ffprobe"

                # Get fps
                cmd_fps = [
                    ffprobe_path,
                    "-v", "error",
                    "-select_streams", "v:0",
                    "-show_entries", "stream=r_frame_rate",
                    "-of", "default=noprint_wrappers=1:nokey=1",
                    video_path
                ]

                result = run_ffmpeg_safely(cmd_fps, timeout=10)

                if result.returncode == 0:
                    # Parse fps (format is usually "num/den")
                    fps_str = result.stdout.strip()
                    if "/" in fps_str:
                        num, den = map(int, fps_str.split("/"))
                        if den != 0:
                            original_fps = min(num / den, options.max_fps)
                    else:
                        try:
                            original_fps = min(float(fps_str), options.max_fps)
                        except ValueError:
                            pass

                # Get bitrate
                cmd_bitrate = [
                    ffprobe_path,
                    "-v", "error",
                    "-select_streams", "v:0",
                    "-show_entries", "stream=bit_rate",
                    "-of", "default=noprint_wrappers=1:nokey=1",
                    video_path
                ]

                result = run_ffmpeg_safely(cmd_bitrate, timeout=10)

                if result.returncode == 0 and result.stdout.strip():
                    try:
                        original_bitrate = min(int(result.stdout.strip()), options.max_bitrate * 1000)
                        # Convert to kbps for FFmpeg
                        original_bitrate_kbps = original_bitrate // 1000
                    except ValueError:
                        pass
            except Exception as e:
                logger.warning(f"Could not get original video fps and bitrate: {e}")
                # Use defaults already set above

            # Build a simpler command with GPU acceleration
            cmd = [
                ffmpeg_path,
                "-y",  # Overwrite output file if it exists
                "-hide_banner"  # Hide banner for cleaner output
            ]

            # Get optimal FFmpeg parameters
            try:
                ffmpeg_params = get_optimal_ffmpeg_params(
                    gpu_info,
                    preset="balanced",
                    target_bitrate=original_bitrate_kbps
                )
            except Exception as e:
                logger.warning(f"Error getting optimal FFmpeg parameters: {e}")
                # Fallback to basic parameters
                ffmpeg_params = {
                    "input": [],
                    "output": []
                }

            # Use GPU acceleration for input if available
            if options.use_gpu and gpu_info["type"] == "nvidia" and gpu_info["nvenc_available"]:
                # Add hardware acceleration for input decoding
                cmd.extend(ffmpeg_params["input"])
                cmd.extend([
                    "-ss", str(start_time),
                    "-to", str(end_time),
                    "-i", video_path,
                    # Use passthrough mode for better frame accuracy
                    "-fps_mode", "passthrough"
                ])

                # Store output parameters for later
                gpu_output_params = ffmpeg_params["output"]
            else:
                # Standard CPU input without hardware acceleration
                cmd.extend([
                    "-ss", str(start_time),
                    "-to", str(end_time),
                    "-i", video_path,
                    # Use passthrough mode for better frame accuracy
                    "-fps_mode", "passthrough"
                ])

                # Store output parameters for later
                gpu_output_params = None

            # Check if input is AV1 codec (which NVENC doesn't support directly)
            is_av1 = False
            try:
                # Use ffprobe to check video codec
                ffprobe_path = os.path.join(os.path.dirname(ffmpeg_path), "ffprobe.exe")
                if not os.path.exists(ffprobe_path):
                    ffprobe_path = "ffprobe"

                cmd_codec = [
                    ffprobe_path,
                    "-v", "error",
                    "-select_streams", "v:0",
                    "-show_entries", "stream=codec_name",
                    "-of", "default=noprint_wrappers=1:nokey=1",
                    video_path
                ]

                # Run command with enhanced error handling
                result = run_ffmpeg_safely(cmd_codec, timeout=10)

                if result.returncode == 0 and "av1" in result.stdout.lower():
                    is_av1 = True
                    logger.info("Detected AV1 codec, using CPU for decoding")
            except Exception as e:
                logger.warning(f"Could not check video codec: {e}")

            # Set output codec based on GPU availability
            if options.use_gpu and gpu_info["type"] == "nvidia" and gpu_info["nvenc_available"] and not is_av1:
                # Use optimized GPU parameters from gpu_optimizer
                if gpu_output_params:
                    cmd.extend(gpu_output_params)
                else:
                    # Fallback to default NVIDIA settings if gpu_output_params is None
                    cmd.extend([
                        "-c:v", "h264_nvenc",
                        "-preset", "p2",          # Balanced preset (p2 instead of p1) for better speed
                        "-tune", "hq",            # Keep high quality tuning
                        "-rc", "vbr",             # Standard variable bitrate (instead of vbr_hq) for better speed
                        "-cq", "23",              # Slightly higher quality factor (23 instead of 20) for better speed
                        "-b:v", f"{original_bitrate_kbps}k",
                        "-maxrate", f"{original_bitrate_kbps * 1.2}k",  # Keep 20% higher bitrate for quality
                        "-bufsize", f"{original_bitrate_kbps * 2}k",
                        # Add additional NVENC parameters for better GPU utilization
                        "-spatial-aq", "1",       # Enable spatial adaptive quantization
                        "-temporal-aq", "1",      # Enable temporal adaptive quantization
                        "-zerolatency", "1",      # Enable zero latency mode for faster processing
                        # Color settings to prevent washed out look
                        "-color_primaries", "bt709",
                        "-color_trc", "bt709",
                        "-colorspace", "bt709"
                    ])
            else:
                # Get optimal CPU parameters from gpu_optimizer
                cpu_params = get_optimal_ffmpeg_params(
                    {"type": "cpu"},
                    preset="balanced",
                    target_bitrate=original_bitrate_kbps
                )

                # Use optimized CPU parameters
                cmd.extend(cpu_params["output"])


            # Simplify the filter approach

            # Create a list of video filters
            video_filters = []

            # Use original fps but cap at max_fps
            video_filters.append(f"fps={original_fps}")

            # Add copyright effects if enabled
            if options.apply_copyright_effects:
                # Add the copyright effects directly
                for effect in copyright_effects:
                    video_filters.append(effect)

            # Fix color issues - prevent washed out/overexposed look
            # Use color correction to ensure proper brightness and contrast
            video_filters.append("eq=gamma=1.0:contrast=1.0:brightness=0.0:saturation=1.0")

            # Scale to 1080p height while maintaining aspect ratio
            video_filters.append("scale=-1:1080:flags=lanczos")

            # Crop to 1:1 aspect ratio (1080x1080)
            video_filters.append("crop=1080:1080:iw/2-540:0")

            # Đã vô hiệu hóa thêm tên kênh vào video

            # Check if we need to use filter_complex (for audio speed adjustment)
            if audio_speed:
                # Use filter_complex for both video and audio
                # Tối ưu hóa cho GPU NVIDIA RTX 3070 Ti
                if options.use_gpu and gpu_info["type"] == "nvidia" and gpu_info["nvenc_available"]:
                    # Sử dụng bộ lọc đơn giản không sử dụng GPU để tránh lỗi
                    sw_filters = video_filters.copy()

                    # Sử dụng bộ lọc đơn giản không sử dụng GPU để tránh lỗi
                    filter_str = f"[0:v]{','.join(sw_filters)}[v];[0:a]{audio_speed}[a]"

                    cmd.extend([
                        "-filter_complex",
                        filter_str,
                        "-map", "[v]",
                        "-map", "[a]"
                    ])
                else:
                    # Standard filter complex for CPU processing
                    cmd.extend([
                        "-filter_complex",
                        f"[0:v]{','.join(video_filters)}[v];[0:a]{audio_speed}[a]",
                        "-map", "[v]",
                        "-map", "[a]"
                    ])
            else:
                # Use simple -vf for video only
                # Tối ưu hóa cho GPU NVIDIA RTX 3070 Ti
                if options.use_gpu and gpu_info["type"] == "nvidia" and gpu_info["nvenc_available"]:
                    # Sử dụng bộ lọc đơn giản không sử dụng GPU để tránh lỗi
                    sw_filters = video_filters.copy()

                    # Sử dụng bộ lọc đơn giản không sử dụng GPU để tránh lỗi
                    filter_str = f"{','.join(sw_filters)}"

                    cmd.extend([
                        "-vf", filter_str
                    ])
                else:
                    # Standard filter for CPU processing
                    cmd.extend([
                        "-vf", ",".join(video_filters)
                    ])

            # Add optimized audio settings
            cmd.extend([
                "-c:a", "aac",
                "-b:a", "128k",
                "-ar", "44100",
                "-ac", "2",
                "-movflags", "+faststart",  # Optimize for web streaming
                "-max_muxing_queue_size", "1024"  # Increase muxing queue size for better performance
            ])

            # Tối ưu hóa luồng xử lý
            if options.use_gpu and gpu_info["type"] == "nvidia" and gpu_info["nvenc_available"]:
                # Tối ưu hóa cho GPU encoding - đơn giản hóa tham số
                cmd.extend([
                    "-threads", "4",  # Giới hạn số luồng CPU khi sử dụng GPU
                    "-gpu", "0"       # Chỉ định GPU
                ])
            else:
                # Tối ưu hóa cho CPU encoding
                cmd.extend([
                    "-threads", "0"  # Sử dụng tất cả luồng CPU có sẵn
                ])

            # Add output path
            cmd.append(output_path)

            # Run FFmpeg with enhanced error handling
            try:
                result = run_ffmpeg_safely(cmd, timeout=300)  # 5 minute timeout
                logger.info(f"FFmpeg completed with return code {result.returncode}")
            except Exception as e:
                logger.error(f"FFmpeg error: {e}")
                raise ProcessingError(f"FFmpeg error: {e}")

            if result.returncode != 0:
                raise FFmpegError(f"FFmpeg error: {result.stderr}")

            if not os.path.exists(output_path):
                raise ProcessingError(f"Output file not created: {output_path}")

            return output_path

        except Exception as e:
            raise ProcessingError(f"Error processing segment: {e}")

    def _combine_segments(self, segment_paths: List[str], output_path: str) -> str:
        """
        Copy processed segments to the output directory instead of combining them.

        Args:
            segment_paths: List of paths to segment files.
            output_path: Path to save the combined video (not used).

        Returns:
            Path to the output directory.

        Raises:
            ProcessingError: If copying segments fails.
        """
        try:
            # Get output directory
            output_dir = os.path.dirname(output_path)

            # Create output directory if it doesn't exist
            os.makedirs(output_dir, exist_ok=True)

            # Copy each segment to the output directory
            for segment_path in segment_paths:
                # Get segment filename
                segment_filename = os.path.basename(segment_path)

                # Create destination path
                dest_path = os.path.join(output_dir, segment_filename)

                # Copy file
                shutil.copy2(segment_path, dest_path)
                logger.info(f"Copied segment to: {dest_path}")

            return output_dir

        except Exception as e:
            logger.error(f"Error copying segments: {e}")
            raise ProcessingError(f"Error copying segments: {e}")

    def _generate_output_filename(self) -> str:
        """
        Generate output filename based on video info.

        Returns:
            Output filename.
        """
        if self.video_info and self.video_info.title:
            # Sanitize title for filename - đã bao gồm xử lý ký tự Cyrillic
            title = sanitize_filename(self.video_info.title)

            # Limit length but preserve file extension
            if len(title) > 50:
                title = title[:47] + "..."

            # For vertical videos, don't add any suffix
            return f"{title}.{self.options.output_format}"
        else:
            try:
                # Extract original filename without extension
                original_filename = os.path.splitext(os.path.basename(self.video_path))[0]

                # Remove any temporary ID from the filename
                original_filename = original_filename.replace("_temp", "")

                # Remove any existing "Part X" text that might be in the original filename
                clean_filename = re.sub(r' - Part \d+', '', original_filename)

                # Sanitize filename to ensure no special characters
                clean_filename = sanitize_filename(clean_filename)

                # Giữ nguyên tên file cho cả video dọc và ngang
                return f"{clean_filename}.{self.options.output_format}"
            except Exception as e:
                logger.error(f"Error generating output filename: {e}")
                # Fallback to timestamp if there's an error
                import time
                return f"video_{int(time.time())}.{self.options.output_format}"

    @staticmethod
    def _format_title(title: str, max_width: int) -> str:
        """
        Format title for display, adding line breaks if needed.

        Args:
            title: Original title.
            max_width: Maximum width in pixels.

        Returns:
            Formatted title.
        """
        # Estimate average character width (in pixels) for Impact font at size 70
        avg_char_width = 35

        # Calculate maximum characters per line
        max_chars_per_line = max_width // avg_char_width

        # Split title into words
        words = title.split()

        # Build lines
        lines = []
        current_line = []
        current_length = 0

        for word in words:
            word_length = len(word)

            if current_length + word_length + len(current_line) <= max_chars_per_line:
                # Word fits on current line
                current_line.append(word)
                current_length += word_length
            else:
                # Word doesn't fit, start a new line
                if current_line:
                    lines.append(" ".join(current_line))
                current_line = [word]
                current_length = word_length

        # Add the last line
        if current_line:
            lines.append(" ".join(current_line))

        # Limit to 3 lines
        if len(lines) > 3:
            # Keep first two lines and add ellipsis to the third
            third_line = lines[2]
            lines = lines[:2]
            lines.append(third_line + "...")

        # Join lines with newlines
        return "\\n".join(lines)

    @staticmethod
    def _create_title_filter(_: Any = None) -> str:
        """
        Create FFmpeg drawtext filter for title.

        Đã vô hiệu hóa - trả về chuỗi rỗng.

        Returns:
            Empty string (feature disabled).
        """
        # Đã vô hiệu hóa thêm tiêu đề vào video
        return ""

    @staticmethod
    def _create_channel_filter(_: Any = None) -> str:
        """
        Create FFmpeg drawtext filter for channel name.

        Đã vô hiệu hóa - trả về chuỗi rỗng.

        Returns:
            Empty string (feature disabled).
        """
        # Đã vô hiệu hóa thêm tên kênh vào video
        return ""

    def cleanup(self) -> None:
        """Clean up temporary files."""
        if self.temp_dir:
            cleanup_temp_directory(self.temp_dir)
            self.temp_dir = None

    def __del__(self):
        """Destructor to ensure cleanup."""
        try:
            self.cleanup()
        except Exception as e:
            logger.warning(f"Error during cleanup in destructor: {e}")

    @staticmethod
    def _check_nvidia_gpu() -> bool:
        """
        Check if NVIDIA GPU (specifically 3070Ti) is available for FFmpeg.
        Optimized for better detection and utilization of 3070Ti.

        Returns:
            True if NVIDIA GPU is available, False otherwise.
        """
        try:
            # Sử dụng hàm từ gpu_optimizer để kiểm tra GPU
            from src.utils.gpu_optimizer import get_gpu_info

            # Lấy thông tin GPU
            gpu_info = get_gpu_info()

            # Kiểm tra xem có phải NVIDIA GPU với NVENC không
            if gpu_info["type"] == "nvidia" and gpu_info["nvenc_available"]:
                # Kiểm tra cụ thể cho RTX 3070 Ti
                if "3070" in gpu_info["name"]:
                    logger.info(f"NVIDIA GeForce RTX 3070Ti detected - optimizing for this GPU")
                    # Ghi log thông tin chi tiết về GPU
                    logger.info(f"GPU Memory: {gpu_info['memory_total']}")
                    logger.info(f"Driver Version: {gpu_info['driver_version']}")
                    logger.info(f"CUDA Version: {gpu_info['cuda_version']}")
                    logger.info(f"NVENC Capabilities: {', '.join(gpu_info['capabilities'])}")

                # Ghi log thông tin về GPU
                logger.info(f"NVIDIA GPU detected: {gpu_info['name']}")
                logger.info("NVIDIA GPU acceleration (h264_nvenc) is available")

                return True

            return False
        except Exception as e:
            logger.debug(f"Error checking NVIDIA GPU: {e}")
            return False

    @staticmethod
    def _check_amd_gpu() -> bool:
        """
        Check if AMD GPU is available for FFmpeg.

        Returns:
            True if AMD GPU is available, False otherwise.
        """
        try:
            # Check if FFmpeg supports AMD acceleration
            ffmpeg_path = get_ffmpeg_path()
            result = subprocess.run(
                [ffmpeg_path, "-hide_banner", "-encoders"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=False,
                encoding='utf-8',
                errors='replace'
            )

            if "h264_amf" in result.stdout:
                # Try to check for AMD GPU
                if os.name == 'nt':  # Windows
                    # Check using Windows Management Instrumentation
                    wmi_result = subprocess.run(
                        ["wmic", "path", "win32_VideoController", "get", "name"],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        check=False,
                        encoding='utf-8',
                        errors='replace'
                    )

                    if "AMD" in wmi_result.stdout or "Radeon" in wmi_result.stdout:
                        logger.info("AMD GPU acceleration is available")
                        return True
                # Loại bỏ phần Linux vì không sử dụng và không thể truy cập

            return False
        except Exception as e:
            logger.debug(f"Error checking AMD GPU: {e}")
            return False

    @staticmethod
    def _check_intel_gpu() -> bool:
        """
        Check if Intel GPU is available for FFmpeg.

        Returns:
            True if Intel GPU is available, False otherwise.
        """
        try:
            # Check if FFmpeg supports Intel acceleration
            ffmpeg_path = get_ffmpeg_path()
            result = subprocess.run(
                [ffmpeg_path, "-hide_banner", "-encoders"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=False,
                encoding='utf-8',
                errors='replace'
            )

            if "h264_qsv" in result.stdout:
                # Try to check for Intel GPU
                if os.name == 'nt':  # Windows
                    # Check using Windows Management Instrumentation
                    wmi_result = subprocess.run(
                        ["wmic", "path", "win32_VideoController", "get", "name"],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        check=False,
                        encoding='utf-8',
                        errors='replace'
                    )

                    if "Intel" in wmi_result.stdout:
                        logger.info("Intel GPU acceleration is available")
                        return True
                # Loại bỏ phần Linux vì không sử dụng và không thể truy cập

            return False
        except Exception as e:
            logger.debug(f"Error checking Intel GPU: {e}")
            return False
