import os
import re
import sys
import shutil

def add_clean_text_function(content):
    """Thêm hàm clean_text_for_display nếu chưa có"""
    if "def clean_text_for_display(" not in content:
        clean_text_function = """
def clean_text_for_display(text):
    # Loại bỏ các ký tự đặc biệt có thể gây lỗi trong filter
    text = re.sub(r'[\\'\\\\]', '', text)
    # Thay thế các ký tự đặc biệt khác bằng khoảng trắng
    text = re.sub(r'[^\\w\\s]', ' ', text)
    # Loại bỏ khoảng trắng thừa
    text = re.sub(r'\\s+', ' ', text).strip()
    return text
"""
        # Thêm hàm clean_text_for_display sau các import
        import_pattern = r"import os\nimport re\nimport time"
        content = content.replace(import_pattern, import_pattern + clean_text_function)
    
    return content

def create_clean_file():
    """Tạo file advanced_video_processor.py mới với tên kênh ở phía dưới video"""
    
    # Đường dẫn đến file gốc và file mới
    original_file = "src/processors/advanced_video_processor.py"
    backup_file = "src/processors/advanced_video_processor.py.bak2"
    
    # Tạo bản sao lưu
    shutil.copy2(original_file, backup_file)
    print(f"Đã tạo bản sao lưu: {backup_file}")
    
    # Đọc nội dung file
    with open(original_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Thêm hàm clean_text_for_display nếu chưa có
    content = add_clean_text_function(content)
    
    # Tìm và thay thế tất cả các phần filter không có tên kênh
    pattern = r'# Không sử dụng title_filter và pad nữa, crop thành 1080x1080\s+return f"\[0:v\]scale=1080:1080:force_original_aspect_ratio=increase,crop=1080:1080:\(iw-1080\)/2:\(ih-1080\)/2,setpts=PTS/1.1\[v\];\[0:a\]atempo=1.1\[a\]"'
    
    replacement = """# Thêm tên kênh vào phía dưới video
                # Tìm font chữ có sẵn cho tên kênh
                font_path = find_available_font()
                
                # Thêm tên kênh vào phía dưới video
                channel_text = ""
                if font_path and hasattr(self, 'channel_name') and self.channel_name:
                    # Xử lý tên kênh để tránh lỗi với các ký tự đặc biệt
                    channel_name = self.channel_name
                    # Loại bỏ các ký tự đặc biệt có thể gây lỗi trong filter
                    safe_channel_name = re.sub(r'[^\\w\\s]', ' ', channel_name)
                    # Loại bỏ khoảng trắng thừa
                    safe_channel_name = re.sub(r'\\s+', ' ', safe_channel_name).strip()
                    
                    # Làm sạch tên kênh để hiển thị trong video
                    clean_channel_name = clean_text_for_display(safe_channel_name)
                    
                    # Escape đường dẫn font để tránh lỗi với FFmpeg
                    escaped_font_path = font_path.replace('\\\\', '\\\\\\\\') if font_path else ""
                    
                    # Thêm tên kênh vào phía dưới video
                    channel_text = f",drawtext=text='{clean_channel_name}':fontfile='{escaped_font_path}':fontsize=50:fontcolor=white:box=1:boxcolor=black@0.5:boxborderw=5:x=(w-text_w)/2:y=h-th-50"
                    
                    print(f"🔤 Đã thêm tên kênh '{clean_channel_name}' vào phía dưới video")
                
                # Thêm channel_text vào filter
                return f"[0:v]scale=1080:1080:force_original_aspect_ratio=increase,crop=1080:1080:(iw-1080)/2:(ih-1080)/2,setpts=PTS/1.1{channel_text}[v];[0:a]atempo=1.1[a]"
"""
    
    # Sử dụng phương pháp thay thế đơn giản hơn
    lines = content.split('\n')
    new_lines = []
    
    i = 0
    while i < len(lines):
        line = lines[i]
        
        # Kiểm tra xem đây có phải là dòng bắt đầu của phần filter không
        if "# Không sử dụng title_filter và pad nữa, crop thành 1080x1080" in line:
            # Kiểm tra xem dòng tiếp theo có phải là return filter không
            if i + 1 < len(lines) and "return f\"[0:v]scale=1080:1080:force_original_aspect_ratio=increase,crop=1080:1080:(iw-1080)/2:(ih-1080)/2,setpts=PTS/1.1[v];[0:a]atempo=1.1[a]\"" in lines[i + 1]:
                # Thay thế bằng filter có tên kênh
                new_lines.append("                # Thêm tên kênh vào phía dưới video")
                new_lines.append("                # Tìm font chữ có sẵn cho tên kênh")
                new_lines.append("                font_path = find_available_font()")
                new_lines.append("                ")
                new_lines.append("                # Thêm tên kênh vào phía dưới video")
                new_lines.append("                channel_text = \"\"")
                new_lines.append("                if font_path and hasattr(self, 'channel_name') and self.channel_name:")
                new_lines.append("                    # Xử lý tên kênh để tránh lỗi với các ký tự đặc biệt")
                new_lines.append("                    channel_name = self.channel_name")
                new_lines.append("                    # Loại bỏ các ký tự đặc biệt có thể gây lỗi trong filter")
                new_lines.append("                    safe_channel_name = re.sub(r'[^\\w\\s]', ' ', channel_name)")
                new_lines.append("                    # Loại bỏ khoảng trắng thừa")
                new_lines.append("                    safe_channel_name = re.sub(r'\\s+', ' ', safe_channel_name).strip()")
                new_lines.append("                    ")
                new_lines.append("                    # Làm sạch tên kênh để hiển thị trong video")
                new_lines.append("                    clean_channel_name = clean_text_for_display(safe_channel_name)")
                new_lines.append("                    ")
                new_lines.append("                    # Escape đường dẫn font để tránh lỗi với FFmpeg")
                new_lines.append("                    escaped_font_path = font_path.replace('\\\\', '\\\\\\\\') if font_path else \"\"")
                new_lines.append("                    ")
                new_lines.append("                    # Thêm tên kênh vào phía dưới video")
                new_lines.append("                    channel_text = f\",drawtext=text='{clean_channel_name}':fontfile='{escaped_font_path}':fontsize=50:fontcolor=white:box=1:boxcolor=black@0.5:boxborderw=5:x=(w-text_w)/2:y=h-th-50\"")
                new_lines.append("                    ")
                new_lines.append("                    print(f\"🔤 Đã thêm tên kênh '{clean_channel_name}' vào phía dưới video\")")
                new_lines.append("                ")
                new_lines.append("                # Thêm channel_text vào filter")
                new_lines.append("                return f\"[0:v]scale=1080:1080:force_original_aspect_ratio=increase,crop=1080:1080:(iw-1080)/2:(ih-1080)/2,setpts=PTS/1.1{channel_text}[v];[0:a]atempo=1.1[a]\"")
                
                # Bỏ qua dòng tiếp theo (dòng return)
                i += 1
            else:
                new_lines.append(line)
        # Kiểm tra xem đây có phải là dòng bắt đầu của phần filter bị lỗi không
        elif "# Không sử dụng tiêu đề nữa" in line and i + 2 < len(lines) and "# Thêm tên kênh vào phía dưới video" in lines[i + 2]:
            # Thêm dòng hiện tại
            new_lines.append(line)
            
            # Thêm các dòng mới
            new_lines.append("")
            new_lines.append("                # Thêm tên kênh vào phía dưới video")
            new_lines.append("                # Tìm font chữ có sẵn cho tên kênh")
            new_lines.append("                font_path = find_available_font()")
            new_lines.append("                ")
            new_lines.append("                # Thêm tên kênh vào phía dưới video")
            new_lines.append("                channel_text = \"\"")
            new_lines.append("                if font_path and hasattr(self, 'channel_name') and self.channel_name:")
            new_lines.append("                    # Xử lý tên kênh để tránh lỗi với các ký tự đặc biệt")
            new_lines.append("                    channel_name = self.channel_name")
            new_lines.append("                    # Loại bỏ các ký tự đặc biệt có thể gây lỗi trong filter")
            new_lines.append("                    safe_channel_name = re.sub(r'[^\\w\\s]', ' ', channel_name)")
            new_lines.append("                    # Loại bỏ khoảng trắng thừa")
            new_lines.append("                    safe_channel_name = re.sub(r'\\s+', ' ', safe_channel_name).strip()")
            new_lines.append("                    ")
            new_lines.append("                    # Làm sạch tên kênh để hiển thị trong video")
            new_lines.append("                    clean_channel_name = clean_text_for_display(safe_channel_name)")
            new_lines.append("                    ")
            new_lines.append("                    # Escape đường dẫn font để tránh lỗi với FFmpeg")
            new_lines.append("                    escaped_font_path = font_path.replace('\\\\', '\\\\\\\\') if font_path else \"\"")
            new_lines.append("                    ")
            new_lines.append("                    # Thêm tên kênh vào phía dưới video")
            new_lines.append("                    channel_text = f\",drawtext=text='{clean_channel_name}':fontfile='{escaped_font_path}':fontsize=50:fontcolor=white:box=1:boxcolor=black@0.5:boxborderw=5:x=(w-text_w)/2:y=h-th-50\"")
            new_lines.append("                    ")
            new_lines.append("                    print(f\"🔤 Đã thêm tên kênh '{clean_channel_name}' vào phía dưới video\")")
            new_lines.append("                ")
            new_lines.append("                # Thêm channel_text vào filter")
            new_lines.append("                return f\"[0:v]scale=1080:1080:force_original_aspect_ratio=increase,crop=1080:1080:(iw-1080)/2:(ih-1080)/2,setpts=PTS/1.1{channel_text}[v];[0:a]atempo=1.1[a]\"")
            
            # Bỏ qua các dòng bị lỗi
            i += 1
            while i < len(lines) and "def _process_segment_wrapper" not in lines[i]:
                i += 1
            
            # Thêm dòng tiếp theo (dòng def _process_segment_wrapper)
            if i < len(lines):
                new_lines.append("")
                new_lines.append(lines[i])
        else:
            new_lines.append(line)
        
        i += 1
    
    # Ghi nội dung đã sửa vào file gốc
    with open(original_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(new_lines))
    
    print(f"Đã tạo file mới với tên kênh ở phía dưới video: {original_file}")

if __name__ == "__main__":
    create_clean_file()
