"""
Smart progress reporting and user experience optimization.
"""

import time
import threading
from typing import Dict, Any, Optional, Callable
from rich.console import Console
from rich.progress import Progress, TaskID, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich.live import Live
from rich.panel import Panel
from rich.text import Text
from rich.table import Table

console = Console()


class SmartProgressManager:
    """Smart progress manager with adaptive reporting."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.progress = None
        self.live = None
        self.tasks = {}
        self.stats = {
            "videos_processed": 0,
            "videos_total": 0,
            "segments_processed": 0,
            "segments_total": 0,
            "errors": 0,
            "gpu_usage": 0,
            "cpu_usage": 0,
            "memory_usage": 0,
            "processing_speed": 0.0,
            "eta": 0,
            "current_operation": "",
            "current_video": "",
            "current_codec": "",
            "gpu_compatible": True
        }
        self.start_time = time.time()
        self.last_update = time.time()
        self.update_interval = 2.0  # Update every 2 seconds
        
    def __enter__(self):
        """Enter context manager."""
        self.start()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit context manager."""
        self.stop()
    
    def start(self):
        """Start the progress manager."""
        self.progress = Progress(
            SpinnerColumn(),
            TextColumn("[bold blue]{task.description}"),
            BarColumn(bar_width=40),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeElapsedColumn(),
            console=console,
            transient=False
        )
        
        # Create main progress display
        self.live = Live(self._create_display(), console=console, refresh_per_second=0.5)
        self.live.start()
        
    def stop(self):
        """Stop the progress manager."""
        if self.live:
            self.live.stop()
        if self.progress:
            self.progress.stop()
    
    def _create_display(self) -> Panel:
        """Create the main display panel."""
        # Create status table
        table = Table.grid(padding=1)
        table.add_column(style="cyan", no_wrap=True)
        table.add_column(style="white")
        
        # Add current operation
        if self.stats["current_operation"]:
            table.add_row("🔄 Operation:", self.stats["current_operation"])
        
        # Add current video
        if self.stats["current_video"]:
            video_name = self.stats["current_video"][:50] + "..." if len(self.stats["current_video"]) > 50 else self.stats["current_video"]
            table.add_row("📹 Video:", video_name)
        
        # Add codec info
        if self.stats["current_codec"]:
            codec_icon = "✅" if self.stats["gpu_compatible"] else "⚠️"
            table.add_row("🎬 Codec:", f"{codec_icon} {self.stats['current_codec']}")
        
        # Add progress info
        if self.stats["videos_total"] > 0:
            table.add_row("📊 Progress:", f"{self.stats['videos_processed']}/{self.stats['videos_total']} videos")
        
        if self.stats["segments_total"] > 0:
            table.add_row("✂️ Segments:", f"{self.stats['segments_processed']}/{self.stats['segments_total']}")
        
        # Add performance info
        if self.stats["processing_speed"] > 0:
            table.add_row("⚡ Speed:", f"{self.stats['processing_speed']:.1f} videos/hour")
        
        if self.stats["eta"] > 0:
            eta_minutes = int(self.stats["eta"] / 60)
            eta_seconds = int(self.stats["eta"] % 60)
            table.add_row("⏱️ ETA:", f"{eta_minutes}m {eta_seconds}s")
        
        # Add system info (if enabled)
        if self.config.get("show_system_info", False):
            if self.stats["gpu_usage"] > 0:
                table.add_row("🎮 GPU:", f"{self.stats['gpu_usage']:.1f}%")
            if self.stats["cpu_usage"] > 0:
                table.add_row("💻 CPU:", f"{self.stats['cpu_usage']:.1f}%")
            if self.stats["memory_usage"] > 0:
                table.add_row("🧠 RAM:", f"{self.stats['memory_usage']:.1f}%")
        
        # Add errors
        if self.stats["errors"] > 0:
            table.add_row("❌ Errors:", str(self.stats["errors"]))
        
        # Create title
        elapsed = time.time() - self.start_time
        elapsed_str = f"{int(elapsed//3600):02d}:{int((elapsed%3600)//60):02d}:{int(elapsed%60):02d}"
        title = f"Video Processor - {elapsed_str}"
        
        return Panel(table, title=title, border_style="green")
    
    def update_operation(self, operation: str):
        """Update current operation."""
        self.stats["current_operation"] = operation
        self._update_display()
    
    def update_video(self, video_name: str, codec: str = "", gpu_compatible: bool = True):
        """Update current video being processed."""
        self.stats["current_video"] = video_name
        self.stats["current_codec"] = codec
        self.stats["gpu_compatible"] = gpu_compatible
        self._update_display()
    
    def update_progress(self, videos_processed: int = None, videos_total: int = None,
                       segments_processed: int = None, segments_total: int = None):
        """Update progress counters."""
        if videos_processed is not None:
            self.stats["videos_processed"] = videos_processed
        if videos_total is not None:
            self.stats["videos_total"] = videos_total
        if segments_processed is not None:
            self.stats["segments_processed"] = segments_processed
        if segments_total is not None:
            self.stats["segments_total"] = segments_total
        
        # Calculate processing speed and ETA
        elapsed = time.time() - self.start_time
        if elapsed > 0 and self.stats["videos_processed"] > 0:
            self.stats["processing_speed"] = (self.stats["videos_processed"] / elapsed) * 3600  # videos per hour
            
            if self.stats["videos_total"] > self.stats["videos_processed"]:
                remaining = self.stats["videos_total"] - self.stats["videos_processed"]
                self.stats["eta"] = remaining / (self.stats["videos_processed"] / elapsed)
        
        self._update_display()
    
    def update_system_stats(self, gpu_usage: float = None, cpu_usage: float = None, memory_usage: float = None):
        """Update system resource usage."""
        if gpu_usage is not None:
            self.stats["gpu_usage"] = gpu_usage
        if cpu_usage is not None:
            self.stats["cpu_usage"] = cpu_usage
        if memory_usage is not None:
            self.stats["memory_usage"] = memory_usage
        
        self._update_display()
    
    def add_error(self):
        """Increment error counter."""
        self.stats["errors"] += 1
        self._update_display()
    
    def _update_display(self):
        """Update the live display."""
        current_time = time.time()
        if current_time - self.last_update >= self.update_interval:
            if self.live:
                self.live.update(self._create_display())
            self.last_update = current_time
    
    def show_completion_summary(self):
        """Show completion summary."""
        elapsed = time.time() - self.start_time
        
        # Create summary table
        table = Table(title="Processing Complete", show_header=False)
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("📹 Videos Processed", str(self.stats["videos_processed"]))
        table.add_row("✂️ Segments Created", str(self.stats["segments_processed"]))
        table.add_row("⏱️ Total Time", f"{int(elapsed//3600):02d}:{int((elapsed%3600)//60):02d}:{int(elapsed%60):02d}")
        
        if self.stats["videos_processed"] > 0:
            avg_time = elapsed / self.stats["videos_processed"]
            table.add_row("⚡ Avg Time/Video", f"{avg_time:.1f}s")
        
        if self.stats["errors"] > 0:
            table.add_row("❌ Errors", str(self.stats["errors"]))
        else:
            table.add_row("✅ Status", "All videos processed successfully")
        
        console.print(table)


class MinimalProgressReporter:
    """Minimal progress reporter for clean output."""
    
    def __init__(self):
        self.last_message = ""
        self.last_update = time.time()
        self.update_interval = 3.0  # Update every 3 seconds
    
    def update(self, message: str, force: bool = False):
        """Update progress with minimal output."""
        current_time = time.time()
        
        if force or (current_time - self.last_update >= self.update_interval and message != self.last_message):
            # Clear previous line and print new message
            console.print(f"\r{message}", end="")
            self.last_message = message
            self.last_update = current_time
    
    def complete(self, message: str):
        """Show completion message."""
        console.print(f"\r{message}")


def create_progress_manager(config: Dict[str, Any]) -> SmartProgressManager:
    """Create appropriate progress manager based on config."""
    if config.get("minimal_output", False):
        return MinimalProgressReporter()
    else:
        return SmartProgressManager(config)
