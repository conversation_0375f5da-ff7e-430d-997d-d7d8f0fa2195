import subprocess
import sys
import os
import time

# <PERSON><PERSON><PERSON> thư mục logs nếu chưa tồn tại
if not os.path.exists('logs'):
    os.makedirs('logs')

# Tạo tên file log với timestamp
log_file = f"logs/run_log_{int(time.time())}.txt"

# Mở file log để ghi
with open(log_file, 'w', encoding='utf-8') as f:
    f.write("=== Bắt đầu chạy script ===\n")
    f.write(f"Thời gian: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
    
    # Chạy script run.py và ghi output vào file log
    try:
        process = subprocess.Popen(
            ["python", "run.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            errors='replace'
        )
        
        # Đ<PERSON><PERSON> và ghi output theo thời gian thực
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                f.write(output)
                f.flush()
                print(output.strip())
                
        # Đọc và ghi stderr nếu có
        stderr = process.stderr.read()
        if stderr:
            f.write("\n=== Lỗi ===\n")
            f.write(stderr)
            print("\n=== Lỗi ===")
            print(stderr)
            
        # Ghi kết quả
        return_code = process.poll()
        f.write(f"\n=== Kết thúc với mã trạng thái: {return_code} ===\n")
        print(f"\n=== Kết thúc với mã trạng thái: {return_code} ===")
        
    except Exception as e:
        f.write(f"\n=== Lỗi khi chạy script: {str(e)} ===\n")
        print(f"\n=== Lỗi khi chạy script: {str(e)} ===")

print(f"Đã ghi log vào file: {log_file}")
