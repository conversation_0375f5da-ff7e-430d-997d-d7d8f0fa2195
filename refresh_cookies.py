#!/usr/bin/env python3
"""
Cookie Refresh Script - Tự động cập nhật cookies cho YouTube
"""

import os
import sys
import time
import subprocess
import tempfile
from datetime import datetime, timed<PERSON><PERSON>

def check_cookies_freshness(cookies_file="cookies.txt"):
    """Kiểm tra độ tươi của cookies"""
    try:
        if not os.path.exists(cookies_file):
            print(f"❌ File cookies không tồn tại: {cookies_file}")
            return False
        
        # Kiểm tra thời gian sửa đổi file
        file_time = os.path.getmtime(cookies_file)
        current_time = time.time()
        age_hours = (current_time - file_time) / 3600
        
        print(f"🕒 Cookies age: {age_hours:.1f} hours")
        
        # Nếu cookies cũ hơn 24 giờ, cần refresh
        if age_hours > 24:
            print(f"⚠️ Cookies cũ hơn 24 giờ, cần refresh")
            return False
        
        # Kiể<PERSON> tra nội dung cookies có <PERSON> không
        with open(cookies_file, 'r', encoding='utf-8') as f:
            content = f.read()
            if '.youtube.com' not in content:
                print(f"⚠️ Cookies không chứa YouTube domains")
                return False
        
        print(f"✅ Cookies còn tươi và hợp lệ")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi kiểm tra cookies: {e}")
        return False

def extract_cookies_with_browser():
    """Trích xuất cookies từ browser"""
    try:
        print("🔄 Đang trích xuất cookies từ browser...")
        
        # Sử dụng yt-dlp để extract cookies
        cmd = [
            "yt-dlp",
            "--cookies-from-browser", "chrome",
            "--cookies", "cookies_new.txt",
            "--no-download",
            "--quiet",
            "https://www.youtube.com/watch?v=dQw4w9WgXcQ"  # Test video
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0 and os.path.exists("cookies_new.txt"):
            # Backup old cookies
            if os.path.exists("cookies.txt"):
                backup_name = f"cookies_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
                os.rename("cookies.txt", backup_name)
                print(f"📦 Đã backup cookies cũ: {backup_name}")
            
            # Replace with new cookies
            os.rename("cookies_new.txt", "cookies.txt")
            print(f"✅ Đã cập nhật cookies từ Chrome")
            return True
        else:
            print(f"❌ Không thể trích xuất cookies: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ Timeout khi trích xuất cookies")
        return False
    except Exception as e:
        print(f"❌ Lỗi trích xuất cookies: {e}")
        return False

def test_cookies():
    """Test cookies với YouTube"""
    try:
        print("🧪 Đang test cookies với YouTube...")
        
        cmd = [
            "yt-dlp",
            "--cookies", "cookies.txt",
            "--no-download",
            "--quiet",
            "--get-title",
            "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0 and result.stdout.strip():
            print(f"✅ Cookies hoạt động tốt: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ Cookies không hoạt động: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ Timeout khi test cookies")
        return False
    except Exception as e:
        print(f"❌ Lỗi test cookies: {e}")
        return False

def update_downloader_config():
    """Cập nhật config downloader để bypass bot detection"""
    try:
        import json
        
        config_file = "config.json"
        if not os.path.exists(config_file):
            print(f"❌ Config file không tồn tại: {config_file}")
            return False
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # Cập nhật download settings để bypass bot detection
        download_config = config.get("download", {})
        
        # Thêm settings chống bot detection
        download_config.update({
            "sleep_interval": 2,
            "max_sleep_interval": 5,
            "sleep_interval_requests": 1,
            "sleep_interval_subtitles": 1,
            "extractor_retries": 5,
            "fragment_retries": 10,
            "retry_sleep_functions": {
                "http": "exp=1:2",
                "fragment": "linear=1:3:5",
                "extractor": "exp=1:2"
            },
            "http_headers": {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                "Accept-Language": "en-us,en;q=0.5",
                "Accept-Encoding": "gzip,deflate",
                "Accept-Charset": "ISO-8859-1,utf-8;q=0.7,*;q=0.7",
                "Keep-Alive": "300",
                "Connection": "keep-alive"
            }
        })
        
        config["download"] = download_config
        
        # Backup config
        backup_name = f"config_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(backup_name, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        # Save updated config
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Đã cập nhật config downloader")
        print(f"📦 Backup config: {backup_name}")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi cập nhật config: {e}")
        return False

def main():
    """Main function"""
    print("🍪 Cookie Refresh Tool")
    print("=" * 50)
    
    # 1. Kiểm tra cookies hiện tại
    if check_cookies_freshness():
        if test_cookies():
            print("✅ Cookies hiện tại vẫn hoạt động tốt!")
            return 0
    
    # 2. Trích xuất cookies mới từ browser
    print("\n🔄 Đang refresh cookies...")
    if extract_cookies_with_browser():
        if test_cookies():
            print("✅ Cookies mới hoạt động tốt!")
        else:
            print("⚠️ Cookies mới có vấn đề, có thể cần đăng nhập lại YouTube")
    else:
        print("❌ Không thể refresh cookies tự động")
        print("💡 Hướng dẫn thủ công:")
        print("   1. Mở Chrome và đăng nhập YouTube")
        print("   2. Chạy: yt-dlp --cookies-from-browser chrome --cookies cookies.txt --no-download https://youtube.com")
        return 1
    
    # 3. Cập nhật config downloader
    print("\n🔧 Đang cập nhật config downloader...")
    update_downloader_config()
    
    print("\n✅ Hoàn thành refresh cookies!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
