#!/usr/bin/env python
"""
Script để chạy chương trình bằng cách nhấp đúp
"""
import os
import sys
import time
import logging
import platform
import argparse
import traceback

# Import utils package first to apply Unicode-safe patch to subprocess
from src.utils import subprocess_wrapper

# <PERSON><PERSON><PERSON> tra phiên bản Python
if sys.version_info < (3, 7):
    print("❌ Lỗi: Chương trình yêu cầu Python 3.7 trở lên")
    print(f"Phiên bản hiện tại: {platform.python_version()}")
    input("Nhấn Enter để thoát...")
    sys.exit(1)

# Chuyển hướng stderr vào file log.txt
log_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "log.txt")
try:
    sys.stderr = open(log_file, 'w', encoding='utf-8')
except Exception as e:
    print(f"⚠️ Không thể mở file log: {str(e)}")
    print("<PERSON><PERSON><PERSON><PERSON> tục mà không ghi log...")

# <PERSON>h<PERSON><PERSON> thư mục hiện tại vào sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import các module cần thiết
try:
    from rich.console import Console
    from rich.panel import Panel
    from rich.text import Text

    from src.core.logging_config import setup_logging
    from src.core.config import Config
    from src.core.cache import Cache
    from src.core.downloader import VideoDownloader
    from src.core.processor import VideoProcessor
    from src.core.batch_processor import process_channel_list
    from src.core.cli import (
        parse_args, check_dependencies, display_video_info, create_progress_bar,
        interactive_mode, display_error, display_success, display_version, console
    )
    from src.core.exceptions import VideoProcessorError, DownloadError, ProcessingError
except ImportError as e:
    print(f"❌ Lỗi khi import module: {str(e)}")
    print("Vui lòng đảm bảo đã cài đặt đầy đủ các thư viện cần thiết")
    input("Nhấn Enter để thoát...")
    sys.exit(1)

def check_required_files(cookies_file, channels_file):
    """Kiểm tra các file cần thiết cho chương trình"""
    # Lấy thư mục hiện tại
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # Đường dẫn đầy đủ đến các file
    cookies_path = os.path.join(current_dir, cookies_file)
    channels_path = os.path.join(current_dir, channels_file)

    # Kiểm tra file channel_list.txt
    if not os.path.exists(channels_path):
        console.print(f"❌ Không tìm thấy file {channels_file}")
        console.print(f"Vui lòng tạo file {channels_file} và thêm đường dẫn đến các file videos.txt (mỗi đường dẫn một dòng)")
        input("Nhấn Enter để thoát...")
        sys.exit(1)

    # Kiểm tra file cookies.txt nhưng không hiển thị thông báo
    if not os.path.exists(cookies_path):
        pass

    return channels_path, cookies_path

def process_file(file_path, config, cache, title=None, tags=None):
    """Xử lý một file video có sẵn"""
    try:
        # Check dependencies
        if not check_dependencies():
            return 1

        if not file_path or not os.path.exists(file_path):
            console.print(f"[bold red]Lỗi:[/bold red] File không tồn tại: {file_path}")
            return 1

        # Process video
        with create_progress_bar() as progress:
            # Create processing task
            process_task = progress.add_task("[cyan]Đang xử lý video...", total=100)

            # Create video_info object if title is provided
            video_info = None
            if title:
                from src.core.models import VideoInfo

                # Process tags if provided
                tag_list = []
                if tags:
                    tag_list = [tag.strip() for tag in tags.split(',') if tag.strip()]
                    console.print(f"[green]Sử dụng tags: {', '.join(tag_list)}[/green]")

                video_info = VideoInfo(
                    id=os.path.splitext(os.path.basename(file_path))[0],
                    title=title,
                    url="file://" + file_path,  # Fake URL for local file
                    uploader="Local Video",
                    duration=0,  # Will be determined by processor
                    tags=tag_list
                )
                console.print(f"[green]Sử dụng tiêu đề: {title}[/green]")

            # Create processor with video_info if available
            processor = VideoProcessor(file_path, video_info, config)

            # Process video
            def update_process_progress(percent):
                if percent % 20 < 1:  # Chỉ hiển thị mỗi 20%
                    progress.update(process_task, completed=percent, description=f"[cyan]Đang xử lý video... {int(percent)}%[/cyan]")

            output_dir = config["paths"].get("output_dir", os.path.join(os.path.dirname(os.path.abspath(__file__)), "data", "output"))

            try:
                console.print("[cyan]Bắt đầu xử lý video...[/cyan]")
                output_path = processor.process(output_dir, update_process_progress)
            except Exception as e:
                # If processing fails, use original video
                console.print(f"[yellow]Lỗi khi xử lý video: {str(e)}[/yellow]")
                console.print("[green]Sử dụng video gốc làm đầu ra.[/green]")
                output_path = file_path

        # Display success message
        display_success(output_path)
        return 0

    except VideoProcessorError as e:
        display_error(e)
        logging.error(f"Error: {e}")
        return 1

    except Exception as e:
        display_error(e)
        logging.error(f"Unexpected error: {e}")
        logging.debug(traceback.format_exc())
        return 1

def process_single_video(url, config, cache):
    """Xử lý một video đơn lẻ"""
    try:
        # Check dependencies
        if not check_dependencies():
            return 1

        # Run in interactive mode if requested
        process_video = True

        if not url:
            console.print("[bold red]Lỗi:[/bold red] URL không được cung cấp.")
            return 1

        # Download video
        with create_progress_bar() as progress:
            # Create download task
            download_task = progress.add_task("[green]Đang tải video...", total=100)

            # Create downloader
            downloader = VideoDownloader(url, config, cache)

            # Get video info
            video_info = downloader.get_info()
            # Hiển thị thông tin video đơn giản
            console.print(f"[bold green]Tiêu đề:[/bold green] {video_info.title}")
            if video_info.uploader:
                console.print(f"[bold yellow]Kênh:[/bold yellow] {video_info.uploader}")
            console.print(f"[bold blue]Thời lượng:[/bold blue] {video_info.duration_formatted}")

            # Download video
            def update_download_progress(percent):
                if percent % 20 < 1:  # Chỉ hiển thị mỗi 20%
                    progress.update(download_task, completed=percent, description=f"[green]Đang tải video... {int(percent)}%[/green]")

            output_dir = config["paths"].get("output_dir", os.path.join(os.path.dirname(os.path.abspath(__file__)), "data", "output"))
            video_path = downloader.download(output_dir, update_download_progress)

            # Process video if requested
            if process_video:
                # Create processor
                processor = VideoProcessor(video_path, video_info, config)

                # Create processing task
                process_task = progress.add_task("[cyan]Đang xử lý video...", total=100)

                # Process video
                def update_process_progress(percent):
                    if percent % 20 < 1:  # Chỉ hiển thị mỗi 20%
                        progress.update(process_task, completed=percent, description=f"[cyan]Đang xử lý video... {int(percent)}%[/cyan]")

                try:
                    console.print("[cyan]Bắt đầu xử lý video...[/cyan]")
                    output_path = processor.process(output_dir, update_process_progress)
                except Exception as e:
                    # If processing fails, use original video
                    console.print(f"[yellow]Lỗi khi xử lý video: {str(e)}[/yellow]")
                    console.print("[green]Sử dụng video gốc làm đầu ra.[/green]")
                    output_path = video_path

                # Clean up original video if processing was successful and output path is different
                if output_path != video_path:
                    downloader.cleanup()
            else:
                output_path = video_path

        # Display success message
        display_success(output_path)

        # Mark URL as completed
        try:
            # Save in the data directory
            completed_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data", "completed_urls.txt")

            # Append URL to completed_urls.txt
            with open(completed_file, 'a', encoding='utf-8') as f:
                f.write(f"{url}\n")
            console.print(f"✅ URL đã được xử lý và đánh dấu hoàn thành")

            # Check if URL is in any videos.txt file and remove it
            try:
                # Look for videos.txt files in the channel_list.txt
                channel_list_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "channel_list.txt")
                if os.path.exists(channel_list_path):
                    with open(channel_list_path, 'r', encoding='utf-8') as f:
                        videos_txt_paths = [line.strip().strip('"\'') for line in f if line.strip()]

                    # Check each videos.txt file
                    for videos_txt_path in videos_txt_paths:
                        if os.path.exists(videos_txt_path):
                            with open(videos_txt_path, 'r', encoding='utf-8') as f:
                                all_urls = [line.strip() for line in f if line.strip()]

                            # If URL is in this file, remove it
                            if url in all_urls:
                                all_urls.remove(url)
                                with open(videos_txt_path, 'w', encoding='utf-8') as f:
                                    for remaining_url in all_urls:
                                        f.write(f"{remaining_url}\n")
                                # Không hiển thị thông báo xóa URL
                                break  # URL should only be in one file
            except Exception as e:
                logging.warning(f"⚠️ Could not remove URL from videos.txt: {str(e)}")
        except Exception as e:
            logging.warning(f"⚠️ Could not mark URL as completed: {str(e)}")

        return 0

    except VideoProcessorError as e:
        display_error(e)
        logging.error(f"Error: {e}")
        return 1

    except Exception as e:
        display_error(e)
        logging.error(f"Unexpected error: {e}")
        logging.debug(traceback.format_exc())
        return 1

def process_batch_mode(channel_list_path, config, cache):
    """Xử lý theo batch từ channel_list.txt"""
    try:
        # Process channel list
        total_channels, success_channels = process_channel_list(channel_list_path, config, cache)

        # Hiển thị tóm tắt đơn giản
        console.print(f"[bold green]Đã xử lý {success_channels}/{total_channels} kênh thành công[/bold green]")

        return 0 if success_channels > 0 else 1

    except Exception as e:
        console.print(f"[bold red]Error processing channel list:[/bold red] {str(e)}")
        logging.error(f"Error processing channel list: {e}")
        logging.debug(traceback.format_exc())
        return 1

def main():
    """
    Main entry point for the application.

    Returns:
        Exit code.
    """
    # Parse command-line arguments
    parser = argparse.ArgumentParser(
        description="Video Processor - Công cụ tải và xử lý video YouTube chuyên nghiệp",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    # Add mode group
    mode_group = parser.add_mutually_exclusive_group()

    mode_group.add_argument(
        "--url",
        help="URL của video YouTube cần tải (chế độ đơn lẻ)"
    )

    mode_group.add_argument(
        "--file",
        help="Đường dẫn đến file video cần xử lý (chế độ xử lý file)"
    )

    mode_group.add_argument(
        "--channel-list", "-c",
        help="Đường dẫn đến file channel_list.txt (chế độ batch)"
    )

    mode_group.add_argument(
        "--interactive", "-i",
        action="store_true",
        help="Chế độ tương tác"
    )

    # Common options
    parser.add_argument(
        "-o", "--output",
        help="Thư mục đầu ra"
    )

    parser.add_argument(
        "-r", "--resolution",
        default="1080",
        help="Độ phân giải video (720, 1080, ...)"
    )

    parser.add_argument(
        "--no-title",
        action="store_true",
        help="Không thêm tiêu đề vào video"
    )

    parser.add_argument(
        "--no-channel",
        action="store_true",
        help="Không thêm tên kênh vào video"
    )

    parser.add_argument(
        "--segments",
        type=int,
        default=40,
        help="Số đoạn video tối đa"
    )

    parser.add_argument(
        "--min-duration",
        type=int,
        default=90,
        help="Thời lượng tối thiểu của mỗi đoạn (giây)"
    )

    parser.add_argument(
        "--max-duration",
        type=int,
        default=150,
        help="Thời lượng tối đa của mỗi đoạn (giây)"
    )

    parser.add_argument(
        "--font",
        default="Impact",
        help="Font chữ cho tiêu đề"
    )

    parser.add_argument(
        "--font-size",
        type=int,
        default=70,
        help="Kích thước font chữ cho tiêu đề"
    )

    parser.add_argument(
        "--title",
        help="Tiêu đề của video (chỉ dùng khi xử lý file)"
    )

    parser.add_argument(
        "--tags",
        help="Các tag của video, phân cách bằng dấu phẩy (chỉ dùng khi xử lý file)"
    )

    parser.add_argument(
        "--cookies",
        default="cookies.txt",
        help="Đường dẫn đến file cookies (mặc định: cookies.txt)"
    )

    # GPU acceleration
    parser.add_argument(
        "--use-gpu",
        action="store_true",
        help="Sử dụng GPU để tăng tốc xử lý video"
    )

    parser.add_argument(
        "--no-gpu",
        action="store_true",
        help="Không sử dụng GPU để xử lý video"
    )

    # Copyright circumvention effects
    parser.add_argument(
        "--copyright-effects",
        action="store_true",
        help="Áp dụng các hiệu ứng lách bản quyền"
    )

    parser.add_argument(
        "--no-copyright-effects",
        action="store_true",
        help="Không áp dụng các hiệu ứng lách bản quyền"
    )

    parser.add_argument(
        "--config",
        help="Đường dẫn đến file cấu hình"
    )

    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Hiển thị thông tin chi tiết"
    )

    parser.add_argument(
        "--version",
        action="store_true",
        help="Hiển thị phiên bản"
    )

    args = parser.parse_args()

    # Display version and exit if requested
    if args.version:
        display_version()
        return 0

    # Set up logging - luôn tắt chế độ verbose để giảm thông báo
    log_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data", "video_processor.log")
    logger = setup_logging(log_file, False)  # Luôn đặt verbose=False

    # Tắt các logger của các module khác
    logging.getLogger("video_processor.downloader").setLevel(logging.ERROR)
    logging.getLogger("video_processor.processor").setLevel(logging.ERROR)
    logging.getLogger("video_processor.config").setLevel(logging.ERROR)
    logging.getLogger("video_processor.batch").setLevel(logging.ERROR)

    try:
        # Check dependencies
        if not check_dependencies():
            return 1

        # Load configuration
        config_file = args.config
        if not config_file and os.path.exists(os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.json")):
            config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.json")

        config_obj = Config(config_file)
        config = config_obj.config

        # Update config from command-line arguments
        if args.output:
            config["paths"]["output_dir"] = args.output

        if args.resolution:
            if args.resolution == "1080":
                config["download"]["format"] = "bestvideo[height=1080][vcodec!*=av01]+bestaudio/299+140/137+140/303+140/bestvideo[height=1080][ext=mp4]+bestaudio[ext=m4a]/bestvideo[height=1080]+bestaudio/best[height=1080]/best"
            elif args.resolution == "720":
                config["download"]["format"] = "bestvideo[height=720][vcodec!*=av01]+bestaudio/298+140/136+140/302+140/bestvideo[height=720][ext=mp4]+bestaudio[ext=m4a]/bestvideo[height=720]+bestaudio/best[height=720]/best"
            else:
                config["download"]["format"] = f"bestvideo[height<={args.resolution}][vcodec!*=av01]+bestaudio/best[height<={args.resolution}]/best"

        if args.no_title:
            config["processing"]["add_title"] = False

        if args.no_channel:
            config["processing"]["add_channel_name"] = False

        if args.segments:
            config["processing"]["max_segments"] = args.segments

        if args.min_duration:
            config["processing"]["min_segment_duration"] = args.min_duration

        if args.max_duration:
            config["processing"]["max_segment_duration"] = args.max_duration

        if args.font:
            config["processing"]["title_font"] = args.font

        if args.font_size:
            config["processing"]["title_font_size"] = args.font_size

        if args.cookies:
            config["download"]["cookies_file"] = args.cookies

        # GPU acceleration
        if args.use_gpu:
            config["processing"]["use_gpu"] = True
        elif args.no_gpu:
            config["processing"]["use_gpu"] = False

        # Copyright circumvention effects
        if args.copyright_effects:
            config["processing"]["apply_copyright_effects"] = True
        elif args.no_copyright_effects:
            config["processing"]["apply_copyright_effects"] = False

        # Initialize cache
        cache_dir = config["paths"].get("cache_dir", os.path.join(os.path.dirname(os.path.abspath(__file__)), "data", "cache"))
        cache = Cache(cache_dir)

        # Determine mode of operation
        if args.interactive:
            # Interactive mode
            config, url, _ = interactive_mode(config)
            return process_single_video(url, config, cache)
        elif args.url:
            # Single video mode
            return process_single_video(args.url, config, cache)
        elif args.file:
            # File processing mode
            return process_file(args.file, config, cache, args.title, args.tags)
        elif args.channel_list:
            # Batch mode with specified channel list
            return process_batch_mode(args.channel_list, config, cache)
        else:
            # Default to batch mode with default channel list
            channels_file = "channel_list.txt"
            cookies_file = args.cookies

            # Check required files
            channels_path, _ = check_required_files(cookies_file, channels_file)

            # Process in batch mode
            return process_batch_mode(channels_path, config, cache)

    except VideoProcessorError as e:
        display_error(e)
        logger.error(f"Error: {e}")
        return 1

    except Exception as e:
        display_error(e)
        logger.error(f"Unexpected error: {e}")
        logger.debug(traceback.format_exc())
        return 1

if __name__ == "__main__":
    try:
        # Display simplified banner
        console.print("[bold green]Video Processor[/bold green] - Công cụ tải và xử lý video YouTube")

        # Run main function
        start_time = time.time()
        exit_code = main()
        elapsed_time = time.time() - start_time
        console.print(f"\n✅ Hoàn thành trong {elapsed_time:.2f} giây")
        sys.exit(exit_code)
    except KeyboardInterrupt:
        console.print("\n⚠️ Đã hủy bởi người dùng")
        sys.exit(1)
    except Exception as e:
        logging.error(f"❌ Lỗi không mong muốn: {str(e)}")
        console.print(f"\n❌ Lỗi không mong muốn: {str(e)}")
        # In thông tin chi tiết về lỗi
        traceback.print_exc()
        input("Nhấn Enter để thoát...")
        sys.exit(1)
