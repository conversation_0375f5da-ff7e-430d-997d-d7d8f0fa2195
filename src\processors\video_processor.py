"""
Module x<PERSON> lý video - phiên bản sửa lỗi cú pháp

File này là bản sao của video_processor.py với các lỗi cú pháp đã được sửa chữa
"""
import os
import json
import random
import logging
import subprocess
import unicodedata
import re
import time
import shutil
import tempfile
from typing import Dict, List, Optional, Tuple, Union

from ..config import settings
from ..utils.helpers import (
    generate_random_intervals,
    find_available_font,
    print_compact_ffmpeg_cmd
)
from ..utils.gpu_utils import check_gpu_capabilities, optimize_gpu_settings
from ..utils.seo_utils import extract_keywords, clean_text

class VideoProcessor:
    def __init__(
        self,
        input_path: str,
        output_path: str,
        video_settings: Dict[str, any],
        gpu_settings: Dict[str, any],
        video_title: Optional[str] = None,
        video_info: Optional[Dict] = None,
        skip_existing: bool = True,
        optimize_bitrate: bool = True,
        target_bitrate: Optional[int] = None
    ):
        """Initialize the VideoProcessor with the given parameters."""
        self.input_path = input_path
        self.output_path = output_path
        self.video_settings = video_settings
        self.gpu_settings = gpu_settings
        self.video_title = video_title
        self.video_info = video_info
        self.skip_existing = skip_existing
        self.optimize_bitrate = optimize_bitrate
        self.target_bitrate = target_bitrate
        self._video_info_cache = None

        # Initialize attributes to store original FPS and bitrate information
        self.original_fps = None
        self.original_bit_rate = None
        self.original_duration = None

        # Extract keywords if we have a title
        self.keywords = []
        if video_title:
            clean_title = clean_text(video_title)
            self.keywords = extract_keywords(clean_title)
            logging.info(f"Keywords extracted from title: {self.keywords}")

        # Check GPU capabilities
        try:
            self.gpu_info = check_gpu_capabilities()
            if self.gpu_info and self.gpu_info.get('can_use_gpu', False):
                logging.info("✅ GPU acceleration is available")
                self.gpu_settings = optimize_gpu_settings(self.gpu_info)
            else:
                logging.warning("⚠️ GPU acceleration is not available, falling back to CPU")
                self.gpu_info = {'can_use_gpu': False}
        except Exception as e:
            logging.error(f"❌ Error checking GPU capabilities: {str(e)}")
            self.gpu_info = {'can_use_gpu': False}

    def _get_video_info(self, path: str) -> Tuple[int, int, float, float]:
        """Get video information from input file"""
        try:
            if not os.path.exists(path):
                logging.error(f"❌ Input file does not exist: {path}")
                return (0, 0, 0, 0)

            cmd = [
                "ffprobe",
                "-v", "error",
                "-select_streams", "v:0",
                "-show_entries", "stream=width,height,r_frame_rate,duration,bit_rate",
                "-of", "json",
                path
            ]

            try:
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    data = json.loads(result.stdout)
                    if 'streams' in data and data['streams']:
                        stream = data['streams'][0]
                        width = int(stream.get('width', 0))
                        height = int(stream.get('height', 0))

                        fps = 0
                        r_frame_rate = stream.get('r_frame_rate', '')
                        if r_frame_rate and '/' in r_frame_rate:
                            num, denom = map(int, r_frame_rate.split('/'))
                            if denom > 0:
                                fps = num / denom

                        bit_rate = stream.get('bit_rate', '0')
                        original_bit_rate = None
                        try:
                            bit_rate = int(bit_rate)
                            if bit_rate > 0:
                                original_bit_rate = bit_rate // 1000
                                logging.info(f"Bitrate gốc của video: {original_bit_rate}k")
                        except (ValueError, TypeError):
                            pass

                        original_fps = None
                        if fps > 0:
                            original_fps = fps
                            logging.info(f"FPS gốc của video: {original_fps}")

                        duration = float(stream.get('duration', 0))

                        if width > 0 and height > 0:
                            return (width, height, fps, duration)
            except Exception as e:
                logging.error(f"Error using ffprobe: {str(e)}")

            return (1920, 1080, 30, 0)

        except Exception as e:
            logging.error(f"Error getting video info: {str(e)}")
            return (1920, 1080, 30, 0)

    def process(self) -> bool:
        """Process the input video - main entry point."""
        import psutil
        import os

        # Giới hạn tài nguyên CPU để tránh quá tải hệ thống
        def limit_cpu_usage():
            try:
                p = psutil.Process()
                # Giảm độ ưu tiên của process
                if hasattr(p, 'nice') and callable(p.nice):
                    p.nice(10)  # Giảm độ ưu tiên (giá trị cao hơn = độ ưu tiên thấp hơn)
                logging.info("✅ Đã giới hạn sử dụng CPU")
            except Exception as e:
                logging.warning(f"⚠️ Không thể giới hạn sử dụng CPU: {str(e)}")

        # Giới hạn CPU ngay từ đầu
        limit_cpu_usage()

        # Đo thời gian xử lý
        start_time = time.time()

        try:
            # Kiểm tra file đầu vào
            if not os.path.exists(self.input_path):
                logging.error(f"❌ Input file does not exist: {self.input_path}")
                return False

            logging.info(f"✅ Processing: Input file is valid and readable: {self.input_path}")

            # Kiểm tra file đầu ra đã tồn tại chưa
            if self.skip_existing and os.path.exists(self.output_path):
                file_size = os.path.getsize(self.output_path)
                if file_size > 1024 * 1024:  # Lớn hơn 1MB, có thể là file video hợp lệ
                    logging.info(f"✅ Output file already exists and is valid: {self.output_path}")
                    return True

            # Lấy thông tin video
            width, height, fps, duration = self._get_video_info(self.input_path)
            self.original_fps = fps
            self.original_duration = duration

            if width == 0 or height == 0:
                logging.error(f"❌ Could not determine video dimensions: {self.input_path}")
                return False

            # Xác định nếu đây là video dọc
            is_vertical = height > width
            logging.info(f"ℹ️ Video format: {'Vertical' if is_vertical else 'Horizontal'} ({width}x{height})")

            # Tính toán bitrate tối ưu
            if self.target_bitrate:
                bit_rate = self.target_bitrate
                logging.info(f"🎯 Using specified target bitrate: {bit_rate}k")
            else:
                minimum_acceptable_bitrate = 8000

                if self.original_bit_rate and self.original_bit_rate > minimum_acceptable_bitrate:
                    bit_rate = min(self.original_bit_rate, 10000)  # Giới hạn tối đa 10000 kbps
                    logging.info(f"✅ Using original bitrate: {bit_rate}k (above minimum quality threshold)")
                else:
                    if self.original_bit_rate:
                        logging.info(f"⚠️ Original bitrate {self.original_bit_rate}k is too low for high quality. Increasing bitrate.")

                    bit_rate = self._calculate_optimal_bitrate(width, height, duration)
                    logging.info(f"🔄 Calculated optimal bitrate for high quality: {bit_rate}k")

            # Tạo thư mục tạm trên ổ đĩa nhanh nhất
            # Ưu tiên sử dụng RAM disk nếu có, nếu không thì sử dụng thư mục tạm mặc định
            temp_dirs = [
                "R:\\temp",  # RAM disk nếu có
                "C:\\temp",  # Ổ C thường nhanh hơn
                None         # Mặc định
            ]

            temp_dir = None
            for dir_path in temp_dirs:
                if dir_path and os.path.exists(dir_path) and os.access(dir_path, os.W_OK):
                    temp_dir = tempfile.mkdtemp(dir=dir_path)
                    logging.info(f"✅ Using fast disk for temporary files: {dir_path}")
                    break

            if not temp_dir:
                temp_dir = tempfile.mkdtemp()
                logging.info(f"ℹ️ Using default temporary directory: {temp_dir}")

            try:
                # Hiển thị thông tin hệ thống
                cpu_count = psutil.cpu_count(logical=False)
                total_memory = psutil.virtual_memory().total / (1024 * 1024 * 1024)  # GB
                logging.info(f"ℹ️ System resources: {cpu_count} CPU cores, {total_memory:.1f}GB RAM")

                # Hiển thị thông tin GPU nếu có
                if self.gpu_info.get('can_use_gpu', False):
                    gpu_name = self.gpu_info.get('name', 'Unknown GPU')
                    logging.info(f"ℹ️ Using GPU acceleration: {gpu_name}")

                # Phân đoạn video
                logging.info(f"⏳ Segmenting video into parts...")
                segment_paths = self._segment_video(self.input_path, temp_dir, width, height, bit_rate, is_vertical)
                if not segment_paths:
                    logging.error("❌ Failed to segment video")
                    return False

                segment_count = len(segment_paths)
                logging.info(f"✅ Created {segment_count} video segments")

                # Xử lý từng phân đoạn
                logging.info(f"⏳ Processing {segment_count} video segments...")
                processed_segments = self._process_segments(segment_paths, temp_dir, width, height, bit_rate, is_vertical)
                if not processed_segments:
                    logging.error("❌ Failed to process video segments")
                    return False

                processed_count = len(processed_segments)
                logging.info(f"✅ Successfully processed {processed_count}/{segment_count} segments")

                # Ghép các phân đoạn lại
                logging.info(f"⏳ Merging {processed_count} video segments...")
                if not self._merge_segments(processed_segments, self.output_path):
                    logging.error("❌ Failed to merge video segments")
                    return False

                # Kiểm tra file đầu ra
                if os.path.exists(self.output_path):
                    output_size = os.path.getsize(self.output_path) / (1024 * 1024)  # MB
                    logging.info(f"✅ Successfully processed video: {self.output_path} ({output_size:.1f}MB)")

                    # Hiển thị thời gian xử lý
                    elapsed_time = time.time() - start_time
                    processing_speed = duration / elapsed_time if elapsed_time > 0 else 0
                    logging.info(f"✅ Processing completed in {elapsed_time:.1f} seconds ({processing_speed:.2f}x real-time)")

                    return True
                else:
                    logging.error(f"❌ Output file not created: {self.output_path}")
                    return False

            finally:
                # Dọn dẹp các file tạm
                try:
                    logging.info(f"🧹 Cleaning up temporary files...")
                    shutil.rmtree(temp_dir)
                except Exception as e:
                    logging.warning(f"⚠️ Failed to clean up temporary directory: {str(e)}")

        except Exception as e:
            logging.error(f"❌ Error processing video: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
            return False

    def _calculate_optimal_bitrate(self, width: int, height: int, duration: float) -> int:
        """Calculate optimal bitrate based on video properties"""
        base_bitrate = 9000  # kbps for 1080p (tăng từ 6000 lên 9000)

        pixel_count = width * height
        reference_pixel_count = 1920 * 1080
        resolution_factor = pixel_count / reference_pixel_count

        adjusted_bitrate = max(8000, int(base_bitrate * resolution_factor))

        if duration < 60:
            adjusted_bitrate = int(adjusted_bitrate * 1.1)
        elif duration > 600:
            adjusted_bitrate = int(adjusted_bitrate * 0.9)

        # Giới hạn bitrate trong khoảng 8000-10000 kbps
        adjusted_bitrate = max(8000, min(10000, adjusted_bitrate))

        return adjusted_bitrate

    def _segment_video(self, input_path: str, temp_dir: str, width: int, height: int, bit_rate: int, is_vertical: bool) -> List[str]:
        """Segment the video into smaller chunks for processing"""
        # Calculate segment durations
        min_duration = settings.VIDEO_SETTINGS["SEGMENT"]["MIN_DURATION"]
        max_duration = settings.VIDEO_SETTINGS["SEGMENT"]["MAX_DURATION"]

        # Calculate number of segments based on video duration
        total_segments = max(1, int(self.original_duration / ((min_duration + max_duration) / 2)))

        # Generate random segment durations
        segment_durations = []
        remaining_duration = self.original_duration

        for i in range(total_segments - 1):
            duration = random.uniform(min_duration, max_duration)
            if duration > remaining_duration:
                duration = remaining_duration
            segment_durations.append(duration)
            remaining_duration -= duration

        # Add remaining duration to last segment
        if remaining_duration > 0:
            segment_durations.append(remaining_duration)

        try:
            segment_files = []
            start_time = 0

            for i, duration in enumerate(segment_durations):
                segment_path = os.path.join(temp_dir, f"segment_{i:03d}.mp4")

                # Build FFmpeg command for segmenting
                cmd = [
                    "ffmpeg",
                    "-y",
                    "-ss", str(start_time),
                    "-t", str(duration),
                    "-i", input_path,
                ]

                # Combine crop and speed adjustment in a single filter_complex
                filter_complex = ""

                if not is_vertical:
                    crop_width = min(width, height)
                    crop_height = crop_width
                    x = (width - crop_width) // 2
                    y = (height - crop_height) // 2
                    filter_complex = f"[0:v]crop={crop_width}:{crop_height}:{x}:{y},setpts={1/settings.VIDEO_SETTINGS['VIDEO_SPEED']}*PTS[v];[0:a]atempo={settings.VIDEO_SETTINGS['VIDEO_SPEED']}[a]"
                else:
                    filter_complex = f"[0:v]setpts={1/settings.VIDEO_SETTINGS['VIDEO_SPEED']}*PTS[v];[0:a]atempo={settings.VIDEO_SETTINGS['VIDEO_SPEED']}[a]"

                cmd.extend(["-filter_complex", filter_complex])
                cmd.extend(["-map", "[v]", "-map", "[a]"])

                # Configure encoding settings
                if self.gpu_info['can_use_gpu']:
                    cmd.extend([
                        "-c:v", "h264_nvenc",
                        "-preset", "fast",  # Sử dụng preset tương thích
                        "-profile:v", "high",
                        "-b:v", f"{bit_rate}k",
                        "-maxrate", f"{bit_rate}k",
                        "-bufsize", f"{bit_rate * 2}k",
                        "-cq", "18",  # Giảm cq để tăng chất lượng
                    ])
                else:
                    cmd.extend([
                        "-c:v", "libx264",
                        "-preset", "slow",  # Sử dụng preset chất lượng cao hơn
                        "-profile:v", "high",
                        "-crf", "18",  # Giảm crf để tăng chất lượng (18 thay vì 23)
                        "-b:v", f"{bit_rate}k",
                        "-maxrate", f"{bit_rate}k",  # Giữ maxrate bằng bitrate để đảm bảo chất lượng ổn định
                        "-bufsize", f"{bit_rate * 2}k",
                    ])

                cmd.extend([
                    "-c:a", "aac",
                    "-b:a", "128k",
                    "-r", "30",
                    segment_path
                ])

                logging.info(f"⏳ Segmenting video part {i+1}/{len(segment_durations)}...")
                result = subprocess.run(cmd, capture_output=True, text=True)

                if result.returncode != 0:
                    logging.error(f"❌ Failed to segment video part {i+1}: {result.stderr}")
                    continue

                if os.path.exists(segment_path):
                    segment_files.append(segment_path)
                else:
                    logging.warning(f"⚠️ Segment file not created: {segment_path}")

                start_time += duration

            logging.info(f"✅ Created {len(segment_files)} video segments")
            return segment_files

        except Exception as e:
            logging.error(f"❌ Error segmenting video: {str(e)}")
            return []

    def _process_segments(self, segment_paths: List[str], temp_dir: str, width: int, height: int, bit_rate: int, is_vertical: bool) -> List[str]:
        """Process each video segment with effects and watermark"""
        processed_files = []
        total_segments = len(segment_paths)

        # Find a good font for watermark
        font_path = find_available_font()

        # Generate random flip intervals
        flip_intervals = []
        if settings.VIDEO_SETTINGS.get('COPYRIGHT_BYPASS_ENABLED', True):
            flip_count = random.randint(3, 5)  # 3-5 flips per video
            min_flip_duration = settings.VIDEO_SETTINGS.get('MIN_FLIP_DURATION', 3.0)
            max_flip_duration = settings.VIDEO_SETTINGS.get('MAX_FLIP_DURATION', 5.0)
            for _ in range(flip_count):
                start = random.uniform(0, self.original_duration - min_flip_duration)
                duration = random.uniform(min_flip_duration, max_flip_duration)
                flip_intervals.append((start, duration))

        for i, segment_path in enumerate(segment_paths):
            try:
                logging.info(f"⏳ Processing segment {i+1}/{total_segments}...")

                output_segment = os.path.join(temp_dir, f"processed_segment_{i:03d}.mp4")

                # Build complex processing command with all effects
                self._process_segment(
                    segment_path,
                    output_segment,
                    width,
                    height,
                    bit_rate,
                    font_path,
                    flip_intervals,
                    is_vertical
                )

                if os.path.exists(output_segment):
                    processed_files.append(output_segment)
                else:
                    logging.warning(f"⚠️ Processed segment not created: {output_segment}")

            except Exception as e:
                logging.error(f"❌ Error processing segment {i+1}: {str(e)}")

        logging.info(f"✅ Processed {len(processed_files)} of {total_segments} segments")
        return processed_files

    def _process_segment(self, input_path: str, output_path: str, width: int, height: int,
                         bit_rate: int, font_path: Optional[str], flip_intervals: List[Tuple[float, float]],
                         is_vertical: bool) -> bool:
        """Process a single video segment with effects and watermark"""
        try:
            # Determine if GPU acceleration is available
            use_gpu = self.gpu_info['can_use_gpu']

            # Set video codec based on GPU capabilities
            video_codec = "h264_nvenc" if use_gpu else "libx264"

            # Build filter complex string
            filters = []

            # Scale filter to ensure proper resolution
            if not is_vertical:
                # For horizontal videos, ensure 1:1 aspect ratio
                target_size = min(width, height)
                filters.append(f"scale={target_size}:{target_size}:force_original_aspect_ratio=decrease")
                filters.append(f"pad={target_size}:{target_size}:(ow-iw)/2:(oh-ih)/2")
            else:
                # For vertical videos, keep original aspect ratio
                filters.append(f"scale=1080:1920:force_original_aspect_ratio=decrease")
                filters.append(f"pad=1080:1920:(ow-iw)/2:(oh-ih)/2")

            # Apply hue variation if enabled
            if settings.VIDEO_SETTINGS.get('COPYRIGHT_BYPASS_ENABLED', True):
                hue_variation = settings.VIDEO_SETTINGS.get('HUE_VARIATION', 0.02)
                if hue_variation > 0:
                    hue_value = random.uniform(-hue_variation, hue_variation)
                    filters.append(f"hue=h={hue_value}")

            # Apply flip effects if enabled
            if flip_intervals:
                flip_filter = []
                for start, duration in flip_intervals:
                    flip_filter.append(f"between(t,{start},{start+duration})")
                if flip_filter:
                    filters.append(f"hflip=enable='{'+'.join(flip_filter)}'")

            # Set speed adjustment
            filters.append(f"setpts={1/settings.VIDEO_SETTINGS['VIDEO_SPEED']}*PTS")
            audio_filter = f"atempo={settings.VIDEO_SETTINGS['VIDEO_SPEED']}"

            # Add watermark if configured
            if self.video_settings.get('watermark_enabled', False):
                watermark_text = self.video_settings.get('watermark_text', '')

                if '%keywords%' in watermark_text and self.keywords:
                    random_keywords = random.sample(self.keywords, min(3, len(self.keywords)))
                    keyword_text = ' '.join(random_keywords)
                    watermark_text = watermark_text.replace('%keywords%', keyword_text)

                if watermark_text and font_path:
                    fontsize = 24
                    fontcolor = "white"
                    boxcolor = "black@0.5"

                    x_pos = random.randint(50, width - 100)
                    y_pos = random.randint(50, height - 50)

                    filters.append(f"drawtext=text='{watermark_text}':fontfile='{font_path}':x={x_pos}:y={y_pos}:fontsize={fontsize}:fontcolor={fontcolor}:box=1:boxcolor={boxcolor}")

            # Join all filters
            filter_complex = ",".join(filters)

            # Build base command
            cmd = [
                "ffmpeg",
                "-y",
                "-i", input_path,
            ]

            # Add filter complex if we have filters
            if filter_complex:
                cmd.extend(["-vf", filter_complex])

            # Add audio filter
            cmd.extend(["-af", audio_filter])

            # Configure video settings
            if use_gpu:
                cmd.extend([
                    "-c:v", video_codec,
                    "-preset", "fast",  # Sử dụng preset tương thích
                    "-profile:v", "high",
                    "-b:v", f"{bit_rate}k",
                    "-maxrate", f"{bit_rate}k",
                    "-bufsize", f"{bit_rate * 2}k",
                    "-cq", "18",  # Giảm cq để tăng chất lượng
                ])
            else:
                cmd.extend([
                    "-c:v", "libx264",
                    "-preset", "slow",  # Sử dụng preset chất lượng cao hơn
                    "-profile:v", "high",
                    "-crf", "18",  # Giảm crf để tăng chất lượng (18 thay vì 23)
                    "-b:v", f"{bit_rate}k",
                    "-maxrate", f"{bit_rate}k",  # Giữ maxrate bằng bitrate để đảm bảo chất lượng ổn định
                    "-bufsize", f"{bit_rate * 2}k",
                ])

            # Force 30 FPS output
            cmd.extend(["-r", "30"])

            # Configure audio settings
            cmd.extend([
                "-c:a", "aac",
                "-b:a", "128k",
                output_path
            ])

            # Run the command
            logging.info(f"⏳ Processing segment with {'GPU' if use_gpu else 'CPU'}, bitrate: {bit_rate}k")
            from src.utils.subprocess_wrapper import safe_run
            result = safe_run(cmd, capture_output=True, text=True)

            if result.returncode != 0:
                logging.error(f"❌ Failed to process segment: {result.stderr}")
                return False

            return os.path.exists(output_path)

        except Exception as e:
            logging.error(f"❌ Error in _process_segment: {str(e)}")
            return False

    def _merge_segments(self, segment_paths: List[str], output_path: str) -> bool:
        """Merge the processed segments back into a single video"""
        try:
            # Create a temporary file with list of segments to concatenate
            concat_file = tempfile.NamedTemporaryFile(delete=False, mode='w', suffix='.txt')

            try:
                # Write each segment file path to the concat file
                for segment in segment_paths:
                    concat_file.write(f"file '{segment}'\n")

                concat_file.close()

                # Use FFmpeg to concatenate segments
                cmd = [
                    "ffmpeg",
                    "-y",
                    "-f", "concat",
                    "-safe", "0",
                    "-i", concat_file.name,
                    "-c", "copy",
                    output_path
                ]

                logging.info(f"⏳ Merging {len(segment_paths)} segments...")
                result = subprocess.run(cmd, capture_output=True, text=True)

                if result.returncode != 0:
                    logging.error(f"❌ Failed to merge segments: {result.stderr}")
                    return False

                if os.path.exists(output_path):
                    logging.info(f"✅ Successfully merged segments to: {output_path}")
                    return True
                else:
                    logging.error(f"❌ Output file not created: {output_path}")
                    return False

            finally:
                # Remove the temporary concat file
                try:
                    os.unlink(concat_file.name)
                except Exception as e:
                    logging.warning(f"⚠️ Failed to remove temporary concat file: {str(e)}")

        except Exception as e:
            logging.error(f"❌ Error merging segments: {str(e)}")
            return False
