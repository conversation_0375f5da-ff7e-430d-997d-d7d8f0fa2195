"""
<PERSON><PERSON><PERSON> hình mặc định cho ứng dụng
"""
import os
from pathlib import Path

# Th<PERSON> mục gốc của project
ROOT_DIR = Path(__file__).parent.parent.parent

# Thư mục chứa font
FONTS_DIR = os.path.join(ROOT_DIR, "assets", "fonts")

# Font mặc định cho watermark
DEFAULT_FONT = os.path.join(FONTS_DIR, "Roboto-Regular.ttf")

# Thứ tự ưu tiên format video - Tối ưu cho 1080p chất lượng cao nhất
PREFERRED_FORMATS = [
    # 1080p chất lượng cao nhất - <PERSON>u tiên cao nhất, loại bỏ AV1 để tương thích GPU
    "bestvideo[height=1080][vcodec!*=av01]+bestaudio/bestvideo[height=1080][ext=mp4]+bestaudio[ext=m4a]",
    # 1080p 30fps - <PERSON><PERSON> ti<PERSON><PERSON> thứ hai
    "bestvideo[height=1080][fps<=30][ext=mp4]+bestaudio[ext=m4a]/best[height=1080][fps<=30][ext=mp4]",
    # 1080p bất kỳ fps với ext=mp4
    "bestvideo[height=1080][ext=mp4]+bestaudio[ext=m4a]/best[height=1080][ext=mp4]",
    # 1080p bất kỳ fps
    "bestvideo[height=1080]+bestaudio/best[height=1080]",
    # 720p chất lượng cao nhất
    "bestvideo[height=720][vcodec!*=av01]+bestaudio/bestvideo[height=720][ext=mp4]+bestaudio[ext=m4a]",
    # Format tốt nhất có sẵn
    "best[ext=mp4]/best"
]

# Cấu hình xử lý video
VIDEO_SETTINGS = {
    # Kích thước crop mặc định
    "DEFAULT_CROP_SIZE": 1080,

    # Thời lượng tối thiểu của mỗi segment (giây)
    "MIN_SEGMENT_DURATION": 90,

    # Thời lượng tối đa của mỗi segment (giây)
    "MAX_SEGMENT_DURATION": 150,

    # Số video tối đa cho mỗi kênh
    "MAX_VIDEOS_PER_CHANNEL": 50,

    # Cấu hình watermark
    "WATERMARK": {
        "TEXT_COLOR": "white",
        "FONT_SIZE": 24,
        "OPACITY": 0.75,  # Đã thay đổi từ 0.8 thành 0.75
        "MARGIN": 20,
        "POSITION": "center"  # Đã thay đổi từ bottom-right thành center
    },

    # Tốc độ video
    "VIDEO_SPEED": 1.1,

    # Cấu hình phân đoạn
    "SEGMENT": {
        "MIN_DURATION": 90,  # 1'30s
        "MAX_DURATION": 150,  # 2'30s
        "MIN_FINAL_SEGMENT": 60  # Độ dài tối thiểu của phân đoạn cuối cùng (1 phút)
    },

    # Cấu hình lật ngang video
    "FLIP": {
        "MIN_COUNT": 5,  # Số lần lật tối thiểu
        "MAX_COUNT": 7,  # Số lần lật tối đa
        "MIN_DURATION": 3,  # Thời gian lật tối thiểu (giây)
        "MAX_DURATION": 5,  # Thời gian lật tối đa (giây)
        "ZOOM_FACTOR": 1.25  # Hệ số zoom (25%)
    },

    # Cấu hình lách bản quyền
    "COPYRIGHT_BYPASS": {
        "ENABLED": True,
        "HUE_SHIFT": 0.02,  # Thay đổi màu sắc nhẹ
        "SATURATION": 0.9,  # Giảm độ bão hòa
        "NOISE_LEVEL": 2  # Mức độ noise
    },

    # Cấu hình crop
    "CROP": {
        "SQUARE_FOR_LANDSCAPE": True,  # Crop video ngang thành 1:1
        "KEEP_VERTICAL": True,  # Giữ nguyên video dọc
        "VERTICAL_OUTPUT": False,  # Xuất video dạng vuông 1080x1080 thay vì dọc 1080x1920
        "SHOW_TITLE": False,  # Không hiển thị tiêu đề
        "SHOW_SUBTITLE": False,  # Không hiển thị phụ đề
        "TITLE_FONT_SIZE": 70  # Giữ nguyên font size cho tương thích ngược
    }
}

# Cấu hình GPU - Tối ưu hóa cho NVIDIA RTX 3070 Ti - Cân bằng tốc độ và chất lượng
GPU_SETTINGS = {
    "enabled": True,                # Bật/tắt sử dụng GPU
    "type": "nvidia",               # Loại GPU (nvidia, amd, intel)
    "HARDWARE_ACCELERATION": "cuda", # Phương pháp tăng tốc phần cứng (cuda, dxva2, qsv, d3d11va)
    "SCALING_ALGORITHM": "lanczos", # Thuật toán scaling chất lượng cao (lanczos, bicubic, bilinear)

    # Các tham số tăng tốc phần cứng
    "hwaccel": ["cuda"],            # Sử dụng CUDA cho tăng tốc
    "hwaccel_device": "0",          # Chỉ định GPU số 0
    "hwaccel_output_format": "cuda", # Định dạng đầu ra của hwaccel
    "encoder": "h264_nvenc",        # Sử dụng encoder NVENC cho H.264

    # Các tham số tối ưu hóa cho RTX 3070 Ti - Cân bằng tốc độ và chất lượng
    "preset": "p2",                 # Sử dụng preset p2 để cân bằng tốc độ và chất lượng (thay vì p1)
    "rc": "vbr",                    # Chế độ điều khiển bitrate biến đổi thông thường (thay vì vbr_hq)
    "b:v": "9M",                    # Giữ nguyên bitrate video mặc định (9 Mbps)
    "maxrate": "10M",               # Giữ nguyên bitrate tối đa (10 Mbps)
    "minrate": "8M",                # Giữ nguyên bitrate tối thiểu (8 Mbps)
    "bufsize": "10M",               # Giữ nguyên kích thước buffer (10 Mbps)

    # Các tham số nâng cao cho NVENC - Tối ưu cho tốc độ xử lý
    "spatial-aq": 1,                # Giữ nguyên tăng tốc không gian
    "temporal-aq": 1,               # Giữ nguyên tăng tốc thời gian
    "weighted_pred": 1,             # Giữ nguyên dự đoán có trọng số
    "b_ref_mode": 0,                # Tắt chế độ tham chiếu khung B để tăng tốc độ
    "gpu": 0,                       # Chỉ định GPU số 0
    "surfaces": 32,                 # Giảm số lượng bề mặt CUDA để giảm sử dụng bộ nhớ (thay vì 64)
    "multipass": "qres",            # Sử dụng chế độ multipass độ phân giải thấp hơn để tăng tốc (thay vì fullres)
    "g": 30,                        # Giữ nguyên GOP size
    "bf": 2,                        # Giảm số lượng B-frames để tăng tốc độ (thay vì 4)
    "refs": 2,                      # Giảm số lượng frame tham chiếu để tăng tốc độ (thay vì 4)
    "lookahead": 20,                # Giảm kích thước cửa sổ lookahead để tăng tốc độ (thay vì 32)
    "no-scenecut": 0,               # Giữ nguyên phát hiện chuyển cảnh
    "forced-idr": 1,                # Giữ nguyên đảm bảo các keyframe là IDR
    "zerolatency": 1,               # Bật chế độ zero-latency để tăng tốc độ xử lý
    "strict_gop": 0,                # Tắt GOP cố định để tăng tốc độ
    "aq-strength": 7,               # Giảm độ mạnh của adaptive quantization để tăng tốc độ (thay vì 10)
    "nonref_p": 1,                  # Sử dụng non-reference P-frames để tăng tốc độ
    "init_qpI": 20,                 # Tăng QP ban đầu cho I-frames để tăng tốc độ (thay vì 16)
    "init_qpP": 22,                 # Tăng QP ban đầu cho P-frames để tăng tốc độ (thay vì 18)
    "init_qpB": 24                  # Tăng QP ban đầu cho B-frames để tăng tốc độ (thay vì 20)
}

# Cấu hình logging
LOGGING = {
    "LEVEL": "DEBUG",  # Ghi log ở mức DEBUG vào file
    "FORMAT": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "FILE": os.path.join(ROOT_DIR, "log.txt"),  # Đổi tên file log
    "CONSOLE_LEVEL": "ERROR"  # Chỉ hiển thị lỗi trên console
}

# Các thông số video được định nghĩa trong VIDEO_SETTINGS

# Cấu hình GPU được định nghĩa trong GPU_SETTINGS

# Cấu hình font chữ - Đã cập nhật để sử dụng font từ thư mục dự án và hỗ trợ Cyrillic
FONT_PATHS = [
    # Ưu tiên sử dụng font từ thư mục dự án - đảm bảo hỗ trợ Cyrillic
    "E:/# GET-VIDEO/fonts/arial.ttf",    # Arial Regular từ thư mục dự án - hỗ trợ tốt cho Cyrillic
    "E:/# GET-VIDEO/fonts/arialbd.ttf",  # Arial Bold từ thư mục dự án - hỗ trợ tốt cho Cyrillic

    # Font từ thư mục người dùng
    "C:/Users/<USER>/Documents/impact/impact.ttf",  # Impact từ thư mục người dùng
    "C:/Users/<USER>/Documents/impact/Impact.ttf",  # Impact từ thư mục người dùng (viết hoa)
    "C:/Users/<USER>/Documents/impact/IMPACT.TTF",  # Impact từ thư mục người dùng (viết hoa)

    # Font hỗ trợ tốt cho Cyrillic (tiếng Nga) từ Windows
    "C:/Windows/Fonts/arial.ttf",       # Arial Regular - hỗ trợ tốt cho Cyrillic
    "C:/Windows/Fonts/arialbd.ttf",     # Arial Bold - hỗ trợ tốt cho Cyrillic
    "C:/Windows/Fonts/tahoma.ttf",      # Tahoma Regular - hỗ trợ tốt cho Cyrillic
    "C:/Windows/Fonts/tahomabd.ttf",    # Tahoma Bold - hỗ trợ tốt cho Cyrillic
    "C:/Windows/Fonts/segoeui.ttf",     # Segoe UI - hỗ trợ tốt cho Cyrillic
    "C:/Windows/Fonts/segoeuib.ttf",    # Segoe UI Bold - hỗ trợ tốt cho Cyrillic
    "C:/Windows/Fonts/times.ttf",       # Times New Roman - hỗ trợ tốt cho Cyrillic
    "C:/Windows/Fonts/timesbd.ttf",     # Times New Roman Bold - hỗ trợ tốt cho Cyrillic
    "C:/Windows/Fonts/calibri.ttf",     # Calibri Regular - hỗ trợ tốt cho Cyrillic
    "C:/Windows/Fonts/calibrib.ttf",    # Calibri Bold - hỗ trợ tốt cho Cyrillic
    "C:/Windows/Fonts/verdana.ttf",     # Verdana - hỗ trợ tốt cho Cyrillic
    "C:/Windows/Fonts/verdanab.ttf",    # Verdana Bold - hỗ trợ tốt cho Cyrillic

    # Font đặc biệt cho Cyrillic
    "C:/Windows/Fonts/cour.ttf",        # Courier New - hỗ trợ tốt cho Cyrillic
    "C:/Windows/Fonts/courbd.ttf",      # Courier New Bold - hỗ trợ tốt cho Cyrillic
    "C:/Windows/Fonts/consola.ttf",     # Consolas - hỗ trợ tốt cho Cyrillic
    "C:/Windows/Fonts/consolab.ttf",    # Consolas Bold - hỗ trợ tốt cho Cyrillic

    # Font dự phòng khác
    "C:/Windows/Fonts/impact.ttf",      # Impact - có thể không hỗ trợ đầy đủ Cyrillic
    "C:/Windows/Fonts/ariblk.ttf"       # Arial Black - có thể không hỗ trợ đầy đủ Cyrillic
]

# Font mặc định cho tên kênh (ưu tiên Arial cho Cyrillic)
CHANNEL_NAME_FONT = "E:/# GET-VIDEO/fonts/arial.ttf"

# Các thông số xử lý video được định nghĩa trong VIDEO_SETTINGS

# Cấu hình yt-dlp - Tối ưu cho 1080p chất lượng cao nhất
YTDLP_OPTIONS = {
    # Cấu hình cơ bản
    'format': 'bestvideo[height=1080][vcodec!*=av01]+bestaudio/bestvideo[height=1080][ext=mp4]+bestaudio[ext=m4a]/bestvideo[height=1080]+bestaudio/best[height=1080]/best',  # Tối ưu cho 1080p chất lượng cao nhất
    'outtmpl': '%(id)s_temp.%(ext)s',
    'merge_output_format': 'mp4',
    'quiet': True,
    'no_warnings': True,
    'ignoreerrors': True,
    'retries': 10,                   # Giảm số lần thử lại để tăng tốc độ
    'fragment_retries': 10,          # Giảm số lần thử lại cho từng fragment
    'file_access_retries': 10,       # Giảm số lần thử lại truy cập file
    'extractor_retries': 10,         # Giảm số lần thử lại trích xuất
    'skip_unavailable_fragments': True,
    'keep_fragments': False,         # Không giữ lại các fragment để tiết kiệm dung lượng
    'no_check_certificate': True,
    'prefer_insecure': True,         # Ưu tiên kết nối không an toàn nếu cần
    'geo_bypass': True,
    'geo_bypass_country': 'US',
    'age_limit': 21,                 # Tăng giới hạn tuổi để truy cập nội dung hạn chế
    'sleep_interval': 1,             # Giảm thời gian chờ giữa các yêu cầu
    'max_sleep_interval': 5,         # Giảm thời gian chờ tối đa
    'source_address': '0.0.0.0',     # Địa chỉ nguồn
    'force_ipv4': True,              # Ép buộc sử dụng IPv4
    'allow_unplayable_formats': False, # Không cho phép các định dạng không phát được để tăng tốc độ
    'check_formats': False,          # Không kiểm tra định dạng
    'youtube_include_dash_manifest': True,  # Bao gồm DASH manifest
    'youtube_include_hls_manifest': True,   # Bao gồm HLS manifest
    'prefer_free_formats': False,    # Không ưu tiên các định dạng miễn phí
    'prefer_ffmpeg': True,           # Ưu tiên sử dụng ffmpeg
    'fixup': 'detect_or_warn',       # Tự động sửa lỗi nếu có thể

    # Tối ưu hóa tải xuống với aria2c - Tối ưu cho tốc độ
    'external_downloader': 'aria2c',
    'external_downloader_args': {
        'aria2c': [
            '--min-split-size=1M',       # Chia nhỏ file để tải song song
            '--max-connection-per-server=8',  # Giảm xuống 8 kết nối mỗi server để tránh bị chặn
            '--max-concurrent-downloads=8',   # Giảm xuống 8 tải xuống đồng thời
            '--split=8',                 # Giảm xuống 8 phần để tránh bị chặn
            '--optimize-concurrent-downloads=true',  # Tự động tối ưu hóa
            '--file-allocation=none',    # Không cấp phát trước file
            '--continue=true',           # Tiếp tục tải nếu bị gián đoạn
            '--retry-wait=2',            # Giảm thời gian chờ giữa các lần thử lại
            '--max-tries=5',             # Giảm số lần thử lại tối đa
            '--allow-overwrite=true',    # Cho phép ghi đè file
            '--always-resume=true',      # Luôn tiếp tục tải nếu có thể
            '--http-accept-gzip=true',   # Chấp nhận nén gzip
            '--summary-interval=0',      # Tắt hiển thị tóm tắt để tăng tốc độ
            '--console-log-level=warn',  # Chỉ hiển thị cảnh báo để tăng tốc độ
            '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'  # Thêm User-Agent
        ]
    },

    # Cấu hình buffer và tốc độ tải
    'buffersize': 33554432,          # Giảm xuống 32MB buffer để tránh lỗi bộ nhớ
    'throttledratelimit': 0,         # Không giới hạn tốc độ
    'hls_prefer_native': True,       # Ưu tiên sử dụng HLS native
    'downloader_options': {
        'http': {
            'timeout': 30,           # Giảm timeout xuống 30s
            'retries': 10            # Số lần thử lại
        }
    },

    # Cấu hình trích xuất - Thử nhiều cấu hình khác nhau
    'extractor_args': {
        'youtube': {
            # Thử với nhiều client khác nhau
            'player_client': ['android', 'web', 'tv_embedded', 'ios'],  # Thay đổi thứ tự ưu tiên
            'player_skip': ['webpage', 'configs', 'js'],  # Bỏ qua nhiều hơn
            'player_client_name': 'ANDROID',  # Thay đổi sang ANDROID
            'innertube_client': 'android',  # Thay đổi sang android
            'innertube_key': 'AIzaSyA8eiZmM1FaDVjRy-df2KTyQ_vz_yYM39w',  # Key cho Android
            'innertube_context': '{"client":{"clientName":"ANDROID","clientVersion":"17.31.35","androidSdkVersion":30,"userAgent":"com.google.android.youtube/17.31.35 (Linux; U; Android 11) gzip","hl":"en","timeZone":"UTC","utcOffsetMinutes":0}}',  # Context cho Android
            'formats': 'none',  # Thử với 'none' thay vì 'missing_pot'
            'max_formats': 100  # Tăng số lượng định dạng tối đa
        }
    },

    # Cấu hình HTTP headers - Thử với User-Agent Android
    'http_headers': {
        'User-Agent': 'com.google.android.youtube/17.31.35 (Linux; U; Android 11) gzip',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Referer': 'https://www.youtube.com/',
        'Origin': 'https://www.youtube.com',
        'X-YouTube-Client-Name': '3',  # Android client
        'X-YouTube-Client-Version': '17.31.35'  # Phiên bản mới nhất
    },

    # Cấu hình xử lý video
    'postprocessor_args': {
        'ffmpeg': ['-c:v', 'copy', '-c:a', 'aac', '-b:a', '192k']
    },

    # Thêm các tùy chọn mới
    'compat_opts': ['no-youtube-unavailable-videos', 'no-youtube-prefer-utc-upload-date'],
    'add_headers': [
        'Referer:https://www.youtube.com/',
        'Origin:https://www.youtube.com',
        'X-YouTube-Client-Name:3',
        'X-YouTube-Client-Version:17.31.35'
    ]
}