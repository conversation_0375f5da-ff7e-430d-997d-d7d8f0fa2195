#!/usr/bin/env python3
"""
Simple GPU Test Script - Test basic GPU encoding without complex filters
"""

import os
import sys
import subprocess
import tempfile
import time

def test_simple_gpu_encoding():
    """Test simple GPU encoding with h264_nvenc"""
    print("🔍 Testing simple GPU encoding...")
    
    try:
        # FFmpeg path
        ffmpeg_path = r"E:\# GET-VIDEO\ffmpeg\ffmpeg.exe"
        
        # Create a simple test video
        test_input = "test_simple_input.mp4"
        test_output = "test_simple_gpu_output.mp4"
        
        # Create test video (5 seconds, 1920x1080)
        print("Creating test video...")
        create_cmd = [
            ffmpeg_path, '-f', 'lavfi', '-i', 'testsrc=duration=5:size=1920x1080:rate=30',
            '-c:v', 'libx264', '-t', '5', test_input
        ]
        
        result = subprocess.run(create_cmd, capture_output=True, text=True, timeout=30)
        if result.returncode != 0:
            print(f"❌ Failed to create test video: {result.stderr}")
            return False
        
        # Test 1: Simple GPU encoding (no filters)
        print("\n🎮 Test 1: Simple GPU encoding (no filters)")
        simple_cmd = [
            ffmpeg_path, '-y', '-i', test_input,
            '-c:v', 'h264_nvenc', '-preset', 'fast', '-cq', '23',
            '-c:a', 'copy',
            'test_simple_1.mp4'
        ]
        
        start_time = time.time()
        result = subprocess.run(simple_cmd, capture_output=True, text=True, timeout=60)
        end_time = time.time()
        
        if result.returncode == 0:
            print(f"✅ Simple GPU encoding successful ({end_time - start_time:.2f}s)")
        else:
            print(f"❌ Simple GPU encoding failed: {result.stderr}")
            return False
        
        # Test 2: GPU encoding with basic scale filter
        print("\n🎮 Test 2: GPU encoding with basic scale filter")
        scale_cmd = [
            ffmpeg_path, '-y', '-i', test_input,
            '-vf', 'scale=1080:1080',
            '-c:v', 'h264_nvenc', '-preset', 'fast', '-cq', '23',
            '-c:a', 'copy',
            'test_scale_2.mp4'
        ]
        
        start_time = time.time()
        result = subprocess.run(scale_cmd, capture_output=True, text=True, timeout=60)
        end_time = time.time()
        
        if result.returncode == 0:
            print(f"✅ GPU encoding with scale successful ({end_time - start_time:.2f}s)")
        else:
            print(f"❌ GPU encoding with scale failed: {result.stderr}")
            return False
        
        # Test 3: GPU encoding with crop filter
        print("\n🎮 Test 3: GPU encoding with crop filter")
        crop_cmd = [
            ffmpeg_path, '-y', '-i', test_input,
            '-vf', 'crop=1080:1080:420:0',
            '-c:v', 'h264_nvenc', '-preset', 'fast', '-cq', '23',
            '-c:a', 'copy',
            'test_crop_3.mp4'
        ]
        
        start_time = time.time()
        result = subprocess.run(crop_cmd, capture_output=True, text=True, timeout=60)
        end_time = time.time()
        
        if result.returncode == 0:
            print(f"✅ GPU encoding with crop successful ({end_time - start_time:.2f}s)")
        else:
            print(f"❌ GPU encoding with crop failed: {result.stderr}")
            return False
        
        # Test 4: GPU encoding with multiple filters
        print("\n🎮 Test 4: GPU encoding with multiple filters")
        multi_cmd = [
            ffmpeg_path, '-y', '-i', test_input,
            '-vf', 'scale=1080:1080,crop=1080:1080:0:0,eq=contrast=1.0',
            '-c:v', 'h264_nvenc', '-preset', 'fast', '-cq', '23',
            '-c:a', 'copy',
            'test_multi_4.mp4'
        ]
        
        start_time = time.time()
        result = subprocess.run(multi_cmd, capture_output=True, text=True, timeout=60)
        end_time = time.time()
        
        if result.returncode == 0:
            print(f"✅ GPU encoding with multiple filters successful ({end_time - start_time:.2f}s)")
        else:
            print(f"❌ GPU encoding with multiple filters failed: {result.stderr}")
            print(f"Error details: {result.stderr}")
            return False
        
        # Test 5: Compare with CPU encoding
        print("\n🖥️ Test 5: CPU encoding comparison")
        cpu_cmd = [
            ffmpeg_path, '-y', '-i', test_input,
            '-vf', 'scale=1080:1080,crop=1080:1080:0:0,eq=contrast=1.0',
            '-c:v', 'libx264', '-preset', 'fast', '-crf', '23',
            '-c:a', 'copy',
            'test_cpu_5.mp4'
        ]
        
        start_time = time.time()
        result = subprocess.run(cpu_cmd, capture_output=True, text=True, timeout=60)
        end_time = time.time()
        
        if result.returncode == 0:
            print(f"✅ CPU encoding successful ({end_time - start_time:.2f}s)")
        else:
            print(f"❌ CPU encoding failed: {result.stderr}")
        
        # Cleanup
        cleanup_files = [
            test_input, 'test_simple_1.mp4', 'test_scale_2.mp4', 
            'test_crop_3.mp4', 'test_multi_4.mp4', 'test_cpu_5.mp4'
        ]
        
        for f in cleanup_files:
            if os.path.exists(f):
                os.remove(f)
        
        print("\n✅ All GPU tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ GPU test error: {e}")
        return False

def test_gpu_with_hardware_acceleration():
    """Test GPU with hardware acceleration"""
    print("\n🔍 Testing GPU with hardware acceleration...")
    
    try:
        ffmpeg_path = r"E:\# GET-VIDEO\ffmpeg\ffmpeg.exe"
        
        # Create test video
        test_input = "test_hwaccel_input.mp4"
        create_cmd = [
            ffmpeg_path, '-f', 'lavfi', '-i', 'testsrc=duration=3:size=1920x1080:rate=30',
            '-c:v', 'libx264', '-t', '3', test_input
        ]
        
        result = subprocess.run(create_cmd, capture_output=True, text=True, timeout=30)
        if result.returncode != 0:
            print(f"❌ Failed to create test video: {result.stderr}")
            return False
        
        # Test hardware acceleration
        print("🎮 Testing hardware acceleration...")
        hwaccel_cmd = [
            ffmpeg_path, '-y',
            '-hwaccel', 'cuda', '-hwaccel_device', '0',
            '-i', test_input,
            '-vf', 'scale=1080:1080',
            '-c:v', 'h264_nvenc', '-preset', 'fast', '-cq', '23',
            'test_hwaccel_output.mp4'
        ]
        
        start_time = time.time()
        result = subprocess.run(hwaccel_cmd, capture_output=True, text=True, timeout=60)
        end_time = time.time()
        
        if result.returncode == 0:
            print(f"✅ Hardware acceleration successful ({end_time - start_time:.2f}s)")
        else:
            print(f"❌ Hardware acceleration failed: {result.stderr}")
            return False
        
        # Cleanup
        for f in [test_input, 'test_hwaccel_output.mp4']:
            if os.path.exists(f):
                os.remove(f)
        
        return True
        
    except Exception as e:
        print(f"❌ Hardware acceleration test error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Simple GPU Encoding Test")
    print("=" * 50)
    
    # Test 1: Simple GPU encoding
    success1 = test_simple_gpu_encoding()
    
    # Test 2: Hardware acceleration
    success2 = test_gpu_with_hardware_acceleration()
    
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS:")
    print(f"  Simple GPU Encoding: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"  Hardware Acceleration: {'✅ PASS' if success2 else '❌ FAIL'}")
    
    if success1 and success2:
        print("\n🎉 All tests passed! GPU is working correctly.")
        print("💡 The issue is likely with complex filters in the main script.")
    else:
        print("\n⚠️ Some tests failed. GPU may have compatibility issues.")

if __name__ == "__main__":
    main()
