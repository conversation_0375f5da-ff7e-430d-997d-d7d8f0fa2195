@echo off
chcp 65001 > nul
title Video Processor

echo ===================================
echo Video Processor
echo ===================================

:: Kiểm tra xem Python đã được cài đặt chưa
python --version > nul 2>&1
if errorlevel 1 (
    echo [31mLỗi: Python chưa được cài đặt![0m
    echo Vui lòng cài đặt Python từ python.org
    pause
    exit /b 1
)

:: Kiểm tra file cần thiết
if not exist "channel_list.txt" (
    echo [31mLỗi: Không tìm thấy file channel_list.txt![0m
    echo Vui lòng tạo file channel_list.txt chứa danh sách đường dẫn tới các file videos.txt
    pause
    exit /b 1
)

:: Chạy script với verbose mode và cookies file (nếu có)
if exist "cookies.txt" (
    echo [32mĐã tìm thấy file cookies.txt[0m
    python -m src.main -c channel_list.txt --cookies-file cookies.txt -v
) else (
    echo [33mKhông tìm thấy file cookies.txt, tiếp tục chạy mà không có cookies...[0m
    python -m src.main -c channel_list.txt -v
)

:: Dừng lại nếu có lỗi
if errorlevel 1 (
    echo.
    echo [31mScript đã kết thúc với lỗi![0m
    pause
    exit /b 1
)

echo.
echo [32mHoàn thành xử lý video![0m
pause 