"""
Module hỗ trợ hiển thị ký tự Cyrillic (tiếng Nga) trong video
"""
import os
import re
from typing import Optional

def get_font_param(font_path: Optional[str], fallback_index: int = 0) -> str:
    """
    Tạo tham số font cho filter drawtext của FFmpeg
    Sử dụng fontfile thay vì font để hỗ trợ tốt hơn cho các ký tự Cyrillic

    Args:
        font_path: Đường dẫn đến font
        fallback_index: Chỉ số font dự phòng nếu font chính gây lỗi

    Returns:
        Chuỗi tham số font cho filter drawtext
    """
    # Danh sách các font hỗ trợ tốt cho Cyrillic
    cyrillic_fonts = [
        "E:/# GET-VIDEO/fonts/arial.ttf",      # Arial từ thư mục dự án
        "E:/# GET-VIDEO/fonts/arialbd.ttf",    # Arial Bold từ thư mục dự án
        "C:/Windows/Fonts/arial.ttf",          # Arial từ Windows
        "C:/Windows/Fonts/arialbd.ttf",        # Arial Bold từ Windows
        "C:/Windows/Fonts/tahoma.ttf",         # Tahoma từ Windows
        "C:/Windows/Fonts/tahomabd.ttf",       # Tahoma Bold từ Windows
        "C:/Windows/Fonts/segoeui.ttf",        # Segoe UI từ Windows
        "C:/Windows/Fonts/segoeuib.ttf",       # Segoe UI Bold từ Windows
        "C:/Windows/Fonts/times.ttf",          # Times New Roman từ Windows
        "C:/Windows/Fonts/timesbd.ttf",        # Times New Roman Bold từ Windows
        "C:/Windows/Fonts/cour.ttf",           # Courier New từ Windows
        "C:/Windows/Fonts/courbd.ttf",         # Courier New Bold từ Windows
    ]

    # Nếu fallback_index vượt quá số lượng font, sử dụng font cuối cùng
    if fallback_index >= len(cyrillic_fonts):
        fallback_index = len(cyrillic_fonts) - 1

    # Ưu tiên sử dụng font từ danh sách dự phòng nếu có chỉ số fallback
    if fallback_index > 0 and fallback_index < len(cyrillic_fonts):
        selected_font = cyrillic_fonts[fallback_index]
        if os.path.exists(selected_font):
            return f":fontfile='{selected_font}'"

    # Ưu tiên sử dụng font Arial từ thư mục dự án cho tên kênh Cyrillic
    if os.path.exists(cyrillic_fonts[0]):
        return f":fontfile='{cyrillic_fonts[0]}'"

    # Nếu không có Arial từ dự án, sử dụng font được chỉ định
    if font_path and os.path.exists(font_path):
        # Sử dụng fontfile thay vì font để hỗ trợ tốt hơn cho các ký tự Cyrillic
        return f":fontfile='{font_path}'"

    # Nếu không có font được chỉ định, thử các font dự phòng
    for font in cyrillic_fonts[1:]:
        if os.path.exists(font):
            return f":fontfile='{font}'"

    # Nếu không tìm thấy font nào, trả về rỗng
    return ""

def clean_text_for_display(text: str) -> str:
    """
    Làm sạch text để hiển thị trong video, giữ lại các ký tự Cyrillic

    Args:
        text: Văn bản cần làm sạch

    Returns:
        Văn bản đã làm sạch
    """
    if not text:
        return ""

    # Loại bỏ các ký tự có thể gây lỗi trong filter FFmpeg
    # Giữ lại các ký tự Cyrillic (U+0400 đến U+04FF)
    # Giữ lại các ký tự Latin, số, khoảng trắng và một số ký tự đặc biệt phổ biến

    # Thay thế dấu nháy đơn và nháy kép để tránh lỗi với FFmpeg
    text = text.replace("'", "").replace('"', "").replace('\\', "")

    # Loại bỏ các ký tự đặc biệt nguy hiểm cho FFmpeg nhưng giữ lại Cyrillic
    # Chỉ loại bỏ các ký tự thực sự có thể gây lỗi
    dangerous_chars = r'[;|<>:&]'
    text = re.sub(dangerous_chars, '', text)

    # Loại bỏ khoảng trắng thừa nhưng giữ nguyên các ký tự Cyrillic
    text = re.sub(r'\s+', ' ', text).strip()

    return text
