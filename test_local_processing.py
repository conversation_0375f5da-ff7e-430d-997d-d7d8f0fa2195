#!/usr/bin/env python3
"""
Local Video Processing Test - Bypass download để test processing với video local
"""

import os
import sys
import time
import json
import shutil
import tempfile
import subprocess
from pathlib import Path

def create_test_videos():
    """Tạo test videos với FFmpeg"""
    try:
        ffmpeg_path = r"E:\# GET-VIDEO\ffmpeg\ffmpeg.exe"

        test_videos = []

        # 1. Video ngang (1920x1080) với audio
        print("🎬 Tạo test video ngang (1920x1080)...")
        horizontal_video = "test_horizontal_1080p.mp4"
        cmd_h = [
            ffmpeg_path, "-f", "lavfi", "-i", "testsrc2=duration=300:size=1920x1080:rate=30",
            "-f", "lavfi", "-i", "sine=frequency=440:duration=300",
            "-c:v", "libx264", "-c:a", "aac", "-t", "300", horizontal_video
        ]

        if not os.path.exists(horizontal_video):
            result = subprocess.run(cmd_h, capture_output=True, text=True, timeout=60)
            if result.returncode == 0:
                test_videos.append(horizontal_video)
                print(f"✅ Tạo thành công: {horizontal_video}")
            else:
                print(f"❌ Lỗi tạo video ngang: {result.stderr}")
        else:
            test_videos.append(horizontal_video)
            print(f"✅ Sử dụng video có sẵn: {horizontal_video}")

        # 2. Video dọc (1080x1920) với audio
        print("📱 Tạo test video dọc (1080x1920)...")
        vertical_video = "test_vertical_1080p.mp4"
        cmd_v = [
            ffmpeg_path, "-f", "lavfi", "-i", "testsrc2=duration=120:size=1080x1920:rate=30",
            "-f", "lavfi", "-i", "sine=frequency=880:duration=120",
            "-c:v", "libx264", "-c:a", "aac", "-t", "120", vertical_video
        ]

        if not os.path.exists(vertical_video):
            result = subprocess.run(cmd_v, capture_output=True, text=True, timeout=60)
            if result.returncode == 0:
                test_videos.append(vertical_video)
                print(f"✅ Tạo thành công: {vertical_video}")
            else:
                print(f"❌ Lỗi tạo video dọc: {result.stderr}")
        else:
            test_videos.append(vertical_video)
            print(f"✅ Sử dụng video có sẵn: {vertical_video}")

        # 3. Video ngắn (shorts) với audio
        print("⚡ Tạo test video shorts (1080x1920, 60s)...")
        shorts_video = "test_shorts.mp4"
        cmd_s = [
            ffmpeg_path, "-f", "lavfi", "-i", "testsrc2=duration=60:size=1080x1920:rate=30",
            "-f", "lavfi", "-i", "sine=frequency=660:duration=60",
            "-c:v", "libx264", "-c:a", "aac", "-t", "60", shorts_video
        ]

        if not os.path.exists(shorts_video):
            result = subprocess.run(cmd_s, capture_output=True, text=True, timeout=60)
            if result.returncode == 0:
                test_videos.append(shorts_video)
                print(f"✅ Tạo thành công: {shorts_video}")
            else:
                print(f"❌ Lỗi tạo video shorts: {result.stderr}")
        else:
            test_videos.append(shorts_video)
            print(f"✅ Sử dụng video có sẵn: {shorts_video}")

        return test_videos

    except Exception as e:
        print(f"❌ Lỗi tạo test videos: {e}")
        return []

def create_fake_video_info(video_path, title, uploader="Test Channel"):
    """Tạo fake VideoInfo object"""
    try:
        # Import VideoInfo
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from src.core.models import VideoInfo

        # Extract video ID from filename
        video_id = os.path.splitext(os.path.basename(video_path))[0]

        # Get video duration with ffprobe
        ffmpeg_path = r"E:\# GET-VIDEO\ffmpeg\ffmpeg.exe"
        ffprobe_path = ffmpeg_path.replace('ffmpeg.exe', 'ffprobe.exe')

        duration = 0
        try:
            import subprocess
            cmd = [
                ffprobe_path, "-v", "quiet", "-show_entries", "format=duration",
                "-of", "default=noprint_wrappers=1:nokey=1", video_path
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                duration = float(result.stdout.strip())
        except:
            duration = 300  # Default 5 minutes

        # Create VideoInfo object
        video_info = VideoInfo(
            id=video_id,
            title=title,
            url=f"file://{video_path}",
            uploader=uploader,
            duration=duration,
            tags=["test", "gpu", "processing", "local"]
        )

        return video_info

    except Exception as e:
        print(f"❌ Lỗi tạo VideoInfo: {e}")
        return None

def test_video_processing(video_path, title, use_gpu=True):
    """Test video processing với GPU"""
    try:
        print(f"\n🎬 Testing: {title}")
        print(f"📁 File: {video_path}")

        # Import required modules
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from src.core.config import Config
        from src.core.processor import VideoProcessor

        # Load config
        config_obj = Config("config.json")
        config = config_obj.config

        # Force GPU settings
        config["processing"]["use_gpu"] = use_gpu
        config["processing"]["prefer_gpu_filters"] = False  # Use CPU filters for compatibility

        # Create fake video info
        video_info = create_fake_video_info(video_path, title)
        if not video_info:
            print(f"❌ Không thể tạo video info")
            return False

        # Create processor
        processor = VideoProcessor(video_path, video_info, config)

        # Create output directory
        output_dir = os.path.join("data", "test_output")
        os.makedirs(output_dir, exist_ok=True)

        # Process video
        start_time = time.time()
        print(f"🚀 Bắt đầu xử lý với {'GPU' if use_gpu else 'CPU'}...")

        output_path = processor.process(output_dir)

        end_time = time.time()
        processing_time = end_time - start_time

        if output_path and os.path.exists(output_path):
            print(f"✅ Xử lý thành công trong {processing_time:.2f}s")
            print(f"📁 Output: {output_path}")

            # Check output files
            if os.path.isdir(output_path):
                files = [f for f in os.listdir(output_path) if f.endswith('.mp4')]
                print(f"📊 Tạo ra {len(files)} file(s): {files[:3]}{'...' if len(files) > 3 else ''}")

            return True
        else:
            print(f"❌ Xử lý thất bại")
            return False

    except Exception as e:
        print(f"❌ Lỗi xử lý video: {e}")
        import traceback
        traceback.print_exc()
        return False

def benchmark_gpu_vs_cpu():
    """Benchmark GPU vs CPU performance"""
    try:
        print("\n🏁 GPU vs CPU Benchmark")
        print("=" * 50)

        # Create test video if not exists
        test_video = "test_benchmark.mp4"
        if not os.path.exists(test_video):
            print("🎬 Tạo video benchmark...")
            ffmpeg_path = r"E:\# GET-VIDEO\ffmpeg\ffmpeg.exe"
            cmd = [
                ffmpeg_path, "-f", "lavfi", "-i", "testsrc2=duration=180:size=1920x1080:rate=30",
                "-f", "lavfi", "-i", "sine=frequency=440:duration=180",
                "-c:v", "libx264", "-c:a", "aac", "-t", "180", test_video
            ]

            import subprocess
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            if result.returncode != 0:
                print(f"❌ Không thể tạo video benchmark")
                return

        # Test CPU
        print("\n🖥️ Testing CPU processing...")
        cpu_start = time.time()
        cpu_success = test_video_processing(test_video, "Benchmark Video (CPU)", use_gpu=False)
        cpu_time = time.time() - cpu_start

        # Test GPU
        print("\n🎮 Testing GPU processing...")
        gpu_start = time.time()
        gpu_success = test_video_processing(test_video, "Benchmark Video (GPU)", use_gpu=True)
        gpu_time = time.time() - gpu_start

        # Results
        print("\n📊 BENCHMARK RESULTS")
        print("=" * 30)
        print(f"CPU Processing: {cpu_time:.2f}s ({'✅ Success' if cpu_success else '❌ Failed'})")
        print(f"GPU Processing: {gpu_time:.2f}s ({'✅ Success' if gpu_success else '❌ Failed'})")

        if cpu_success and gpu_success:
            speedup = cpu_time / gpu_time
            print(f"🚀 GPU Speedup: {speedup:.2f}x faster")

            if speedup > 1.5:
                print("🎉 GPU acceleration is working well!")
            elif speedup > 1.1:
                print("✅ GPU provides moderate improvement")
            else:
                print("⚠️ GPU improvement is minimal")

    except Exception as e:
        print(f"❌ Lỗi benchmark: {e}")

def main():
    """Main function"""
    print("🎬 Local Video Processing Test")
    print("=" * 50)



    # 1. Create test videos
    print("📹 Tạo test videos...")
    test_videos = create_test_videos()

    if not test_videos:
        print("❌ Không thể tạo test videos")
        return 1

    # 2. Test processing each video
    success_count = 0
    for video_path in test_videos:
        if os.path.exists(video_path):
            title = f"Test Video - {os.path.basename(video_path)}"
            if test_video_processing(video_path, title):
                success_count += 1

    print(f"\n📊 Kết quả: {success_count}/{len(test_videos)} videos xử lý thành công")

    # 3. Benchmark GPU vs CPU
    benchmark_gpu_vs_cpu()

    # 4. Cleanup
    print(f"\n🧹 Dọn dẹp test files...")
    for video in test_videos + ["test_benchmark.mp4"]:
        if os.path.exists(video):
            try:
                os.remove(video)
                print(f"🗑️ Đã xóa: {video}")
            except:
                print(f"⚠️ Không thể xóa: {video}")

    print(f"\n✅ Hoàn thành test local processing!")
    return 0 if success_count > 0 else 1

if __name__ == "__main__":
    sys.exit(main())
