"""
GPU optimization utilities for video processing
"""
import os
import re
import logging
import subprocess
import json
import shutil
from typing import Dict, Any, List, Optional, Tuple

from .subprocess_wrapper import safe_run, run_ffmpeg_safely

# NVIDIA GPU optimization presets
NVIDIA_PRESETS = {
    # Balanced preset (good quality, good speed)
    "balanced": {
        "preset": "p2",
        "tune": "hq",
        "rc": "vbr",
        "cq": "23",
        "qmin": "1",
        "qmax": "51",
        "b_ref_mode": "middle",
        "spatial_aq": "1",
        "temporal_aq": "1",
        "rc-lookahead": "20",
        "surfaces": "32",
        "gpu_copy": "1",
        "no-scenecut": "1",
        "b_adapt": "1",
        "zerolatency": "1",
        "weighted_pred": "1",
        "nonref_p": "1",
        "strict_gop": "0",
        "aq-strength": "8",
        "init_qpP": "23",
        "init_qpB": "25",
        "init_qpI": "21",
        "bitrate": "8000k",
        "maxrate": "10000k",
        "bufsize": "12000k",
        "multipass": "qres"
    },

    # Speed preset (faster encoding, lower quality)
    "speed": {
        "preset": "p4",
        "tune": "ll",
        "rc": "vbr",
        "cq": "26",
        "qmin": "1",
        "qmax": "51",
        "b_ref_mode": "disabled",
        "spatial_aq": "1",
        "temporal_aq": "0",
        "rc-lookahead": "10",
        "surfaces": "16",
        "gpu_copy": "1",
        "no-scenecut": "1",
        "b_adapt": "0",
        "zerolatency": "1",
        "weighted_pred": "0",
        "nonref_p": "1",
        "strict_gop": "0",
        "aq-strength": "5",
        "init_qpP": "26",
        "init_qpB": "28",
        "init_qpI": "24",
        "bitrate": "6000k",
        "maxrate": "8000k",
        "bufsize": "8000k",
        "multipass": "disabled"
    },

    # Quality preset (higher quality, slower encoding)
    "quality": {
        "preset": "p1",
        "tune": "hq",
        "rc": "vbr_hq",
        "cq": "20",
        "qmin": "1",
        "qmax": "51",
        "b_ref_mode": "each",
        "spatial_aq": "1",
        "temporal_aq": "1",
        "rc-lookahead": "32",
        "surfaces": "64",
        "gpu_copy": "1",
        "no-scenecut": "0",
        "b_adapt": "1",
        "zerolatency": "0",
        "weighted_pred": "1",
        "nonref_p": "0",
        "strict_gop": "0",
        "aq-strength": "10",
        "init_qpP": "20",
        "init_qpB": "22",
        "init_qpI": "18",
        "bitrate": "10000k",
        "maxrate": "12000k",
        "bufsize": "16000k",
        "multipass": "fullres"
    }
}

# CPU optimization presets
CPU_PRESETS = {
    # Balanced preset (good quality, good speed)
    "balanced": {
        "preset": "medium",
        "tune": "film",
        "crf": "23",
        "x264-params": "ref=4:bframes=3:b-adapt=1:direct=auto:me=hex:subme=7:trellis=1:deblock=-1,-1:psy-rd=0.8,0.2:aq-mode=1:aq-strength=0.8:fastpskip=1:no-dct-decimate=0",
        "bitrate": "8000k",
        "maxrate": "10000k",
        "bufsize": "12000k"
    },

    # Speed preset (faster encoding, lower quality)
    "speed": {
        "preset": "veryfast",
        "tune": "fastdecode",
        "crf": "26",
        "x264-params": "ref=2:bframes=2:b-adapt=0:direct=auto:me=dia:subme=4:trellis=0:deblock=0,0:psy-rd=0.5,0.0:aq-mode=1:aq-strength=0.5:fastpskip=1:no-dct-decimate=1",
        "bitrate": "6000k",
        "maxrate": "8000k",
        "bufsize": "8000k"
    },

    # Quality preset (higher quality, slower encoding)
    "quality": {
        "preset": "slow",
        "tune": "film",
        "crf": "20",
        "x264-params": "ref=6:bframes=5:b-adapt=2:direct=auto:me=umh:subme=9:trellis=2:deblock=-2,-2:psy-rd=1.0,0.3:aq-mode=3:aq-strength=1.0:fastpskip=0:no-dct-decimate=0",
        "bitrate": "10000k",
        "maxrate": "12000k",
        "bufsize": "16000k"
    }
}

def get_gpu_info() -> Dict[str, Any]:
    """
    Get detailed information about available GPUs

    Returns:
        Dictionary with GPU information
    """
    gpu_info = {
        "type": "unknown",
        "name": "Unknown GPU",
        "memory_total": "0",
        "memory_free": "0",
        "driver_version": "unknown",
        "cuda_version": "unknown",
        "nvenc_available": False,
        "capabilities": []
    }

    try:
        # Check for NVIDIA GPU using nvidia-smi
        nvidia_smi = shutil.which("nvidia-smi")
        if nvidia_smi:
            try:
                # Get basic GPU info
                cmd = [nvidia_smi, "--query-gpu=name,memory.total,memory.free,driver_version", "--format=csv,noheader"]
                result = safe_run(cmd, capture_output=True, text=True)

                if result.returncode == 0:
                    parts = [p.strip() for p in result.stdout.strip().split(',')]
                    if len(parts) >= 4:
                        gpu_info["type"] = "nvidia"
                        gpu_info["name"] = parts[0]
                        gpu_info["memory_total"] = parts[1]
                        gpu_info["memory_free"] = parts[2]
                        gpu_info["driver_version"] = parts[3]

                        # Check for CUDA version
                        try:
                            cuda_cmd = [nvidia_smi, "--query-gpu=cuda_version", "--format=csv,noheader"]
                            cuda_result = safe_run(cuda_cmd, capture_output=True, text=True)
                            if cuda_result.returncode == 0:
                                gpu_info["cuda_version"] = cuda_result.stdout.strip()
                        except Exception as e:
                            logging.warning(f"Error getting CUDA version: {e}")

                        # Check for NVENC capabilities using FFmpeg
                        try:
                            ffmpeg_cmd = ["ffmpeg", "-hide_banner", "-encoders"]
                            ffmpeg_result = safe_run(ffmpeg_cmd, capture_output=True, text=True)
                            if ffmpeg_result.returncode == 0:
                                if "h264_nvenc" in ffmpeg_result.stdout:
                                    gpu_info["nvenc_available"] = True
                                    gpu_info["capabilities"].append("h264_nvenc")
                                if "hevc_nvenc" in ffmpeg_result.stdout:
                                    gpu_info["capabilities"].append("hevc_nvenc")
                        except Exception as e:
                            logging.warning(f"Error checking NVENC capabilities: {e}")

                        # Check for specific GPU model to optimize settings
                        if "3070" in gpu_info["name"]:
                            gpu_info["model"] = "rtx3070"
                        elif "3080" in gpu_info["name"]:
                            gpu_info["model"] = "rtx3080"
                        elif "3090" in gpu_info["name"]:
                            gpu_info["model"] = "rtx3090"
                        elif "2070" in gpu_info["name"]:
                            gpu_info["model"] = "rtx2070"
                        elif "2080" in gpu_info["name"]:
                            gpu_info["model"] = "rtx2080"
                        elif "2060" in gpu_info["name"]:
                            gpu_info["model"] = "rtx2060"
                        elif "1660" in gpu_info["name"]:
                            gpu_info["model"] = "gtx1660"
                        elif "1650" in gpu_info["name"]:
                            gpu_info["model"] = "gtx1650"
                        elif "1080" in gpu_info["name"]:
                            gpu_info["model"] = "gtx1080"
                        elif "1070" in gpu_info["name"]:
                            gpu_info["model"] = "gtx1070"
                        elif "1060" in gpu_info["name"]:
                            gpu_info["model"] = "gtx1060"
                        else:
                            gpu_info["model"] = "generic"
            except Exception as e:
                logging.warning(f"Error getting NVIDIA GPU info: {e}")

        # If no NVIDIA GPU, check for AMD GPU (limited support)
        elif os.name == "nt":  # Windows
            try:
                cmd = ["wmic", "path", "win32_VideoController", "get", "name"]
                result = safe_run(cmd, capture_output=True, text=True)

                if result.returncode == 0:
                    output = result.stdout.lower()
                    if "amd" in output or "radeon" in output:
                        gpu_info["type"] = "amd"
                        # Extract name using regex
                        match = re.search(r"(amd|radeon)[\w\s]+", output, re.IGNORECASE)
                        if match:
                            gpu_info["name"] = match.group(0).strip()

                        # Check for AMD encoders in FFmpeg
                        try:
                            ffmpeg_cmd = ["ffmpeg", "-hide_banner", "-encoders"]
                            ffmpeg_result = safe_run(ffmpeg_cmd, capture_output=True, text=True)
                            if ffmpeg_result.returncode == 0:
                                if "h264_amf" in ffmpeg_result.stdout:
                                    gpu_info["capabilities"].append("h264_amf")
                                if "hevc_amf" in ffmpeg_result.stdout:
                                    gpu_info["capabilities"].append("hevc_amf")
                        except Exception as e:
                            logging.warning(f"Error checking AMD capabilities: {e}")
            except Exception as e:
                logging.warning(f"Error getting AMD GPU info: {e}")

    except Exception as e:
        logging.warning(f"Error getting GPU info: {e}")

    return gpu_info

def get_optimal_ffmpeg_params(gpu_info: Dict[str, Any], preset: str = "balanced", target_bitrate: Optional[int] = None) -> Dict[str, List[str]]:
    """
    Get optimal FFmpeg parameters based on GPU capabilities

    Args:
        gpu_info: GPU information from get_gpu_info()
        preset: Preset to use (balanced, speed, quality)
        target_bitrate: Target bitrate in kbps (overrides preset bitrate)

    Returns:
        Dictionary with input and output parameters for FFmpeg
    """
    # Default parameters
    input_params = []
    output_params = []

    # Validate preset
    if preset not in ["balanced", "speed", "quality"]:
        preset = "balanced"

    # NVIDIA GPU with NVENC
    if gpu_info["type"] == "nvidia" and gpu_info["nvenc_available"]:
        # Add hardware acceleration for input with basic parameters
        input_params.extend([
            "-hwaccel", "cuda",
            "-hwaccel_device", "0"
        ])

        # Get preset parameters
        nvenc_params = NVIDIA_PRESETS[preset]

        # Override bitrate if specified
        if target_bitrate is not None:
            nvenc_params["bitrate"] = f"{target_bitrate}k"
            nvenc_params["maxrate"] = f"{int(target_bitrate * 1.25)}k"
            nvenc_params["bufsize"] = f"{int(target_bitrate * 1.5)}k"

        # Build output parameters - tối ưu hóa cho RTX 3070 Ti
        output_params.extend([
            "-c:v", "h264_nvenc",
            "-preset", nvenc_params["preset"],
            "-tune", nvenc_params["tune"],
            "-b:v", nvenc_params["bitrate"],
            "-maxrate", nvenc_params["maxrate"],
            "-bufsize", nvenc_params["bufsize"],
            "-profile:v", "high",
            "-level", "4.1",
            "-rc-lookahead", "32",       # Tăng lookahead để cải thiện chất lượng
            "-surfaces", "32",           # Tăng số lượng surface cho xử lý song song
            "-gpu", "0"                  # Chỉ định GPU
        ])

        # Thêm các tham số NVENC nâng cao cho RTX 3070 Ti
        # Các tham số này đã được kiểm tra và hoạt động tốt với RTX 3070 Ti
        if nvenc_params["spatial_aq"] == "1":
            output_params.extend(["-spatial-aq", "1", "-aq-strength", "8"])  # Tăng cường chất lượng
        if nvenc_params["temporal_aq"] == "1":
            output_params.extend(["-temporal-aq", "1"])

        # Thêm các tham số tối ưu hóa hiệu suất - đã loại bỏ tham số không được hỗ trợ
        output_params.extend([
            "-no-scenecut", "1",         # Tắt phát hiện cảnh để tăng tốc độ
            "-nonref_p", "1",            # Cho phép khung P không tham chiếu
            "-strict_gop", "0",          # Không áp đặt GOP nghiêm ngặt
            "-bf", "3"                   # Số lượng B-frames
        ])

    # AMD GPU with AMF
    elif gpu_info["type"] == "amd" and "h264_amf" in gpu_info["capabilities"]:
        # AMD support is more limited
        input_params.extend([
            "-hwaccel", "d3d11va",
            "-hwaccel_device", "0"
        ])

        # Use CPU presets as base
        cpu_params = CPU_PRESETS[preset]

        # Override bitrate if specified
        if target_bitrate is not None:
            bitrate = f"{target_bitrate}k"
            maxrate = f"{int(target_bitrate * 1.25)}k"
        else:
            bitrate = cpu_params["bitrate"]
            maxrate = cpu_params["maxrate"]

        # Build output parameters for AMD
        output_params.extend([
            "-c:v", "h264_amf",
            "-quality", "quality" if preset == "quality" else "speed",
            "-rc", "vbr_peak",
            "-b:v", bitrate,
            "-maxrate", maxrate,
            "-profile:v", "high",
            "-level", "4.1"
        ])

    # CPU encoding
    else:
        # Get CPU preset parameters
        cpu_params = CPU_PRESETS[preset]

        # Override bitrate if specified
        if target_bitrate is not None:
            cpu_params["bitrate"] = f"{target_bitrate}k"
            cpu_params["maxrate"] = f"{int(target_bitrate * 1.25)}k"
            cpu_params["bufsize"] = f"{int(target_bitrate * 1.5)}k"

        # Build output parameters for CPU
        output_params.extend([
            "-c:v", "libx264",
            "-preset", cpu_params["preset"],
            "-tune", cpu_params["tune"],
            "-crf", cpu_params["crf"],
            "-b:v", cpu_params["bitrate"],
            "-maxrate", cpu_params["maxrate"],
            "-bufsize", cpu_params["bufsize"],
            "-profile:v", "high",
            "-level", "4.1",
            "-x264-params", cpu_params["x264-params"]
        ])

    # Common audio parameters
    output_params.extend([
        "-c:a", "aac",
        "-b:a", "128k",
        "-ar", "44100"
    ])

    return {
        "input": input_params,
        "output": output_params
    }

def optimize_ffmpeg_command(cmd: List[str], gpu_info: Dict[str, Any], preset: str = "balanced") -> List[str]:
    """
    Optimize an existing FFmpeg command with GPU acceleration

    Args:
        cmd: Original FFmpeg command
        gpu_info: GPU information from get_gpu_info()
        preset: Preset to use (balanced, speed, quality)

    Returns:
        Optimized FFmpeg command
    """
    # Find input file position
    input_pos = -1
    output_pos = -1

    for i, param in enumerate(cmd):
        if param == "-i" and i + 1 < len(cmd):
            input_pos = i
        elif not param.startswith("-") and i > 0 and i == len(cmd) - 1:
            output_pos = i

    if input_pos == -1 or output_pos == -1:
        logging.warning("Could not optimize FFmpeg command: input or output not found")
        return cmd

    # Get optimal parameters
    optimal_params = get_optimal_ffmpeg_params(gpu_info, preset)

    # Create new command
    new_cmd = cmd[:input_pos]  # Everything before input
    new_cmd.extend(optimal_params["input"])  # Add input parameters
    new_cmd.extend(cmd[input_pos:output_pos])  # Add original input and filters

    # Remove any existing video codec parameters
    i = 0
    while i < len(new_cmd):
        if new_cmd[i] in ["-c:v", "-codec:v"] or new_cmd[i].startswith("-preset") or new_cmd[i].startswith("-tune"):
            new_cmd.pop(i)
            if i < len(new_cmd):
                new_cmd.pop(i)
        else:
            i += 1

    # Add optimized output parameters
    new_cmd.extend(optimal_params["output"])
    new_cmd.append(cmd[output_pos])  # Add output file

    return new_cmd
