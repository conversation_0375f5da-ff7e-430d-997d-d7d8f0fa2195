"""
Parallel processing utilities for video processing
"""
import os
import time
import logging
import threading
import queue
import concurrent.futures
import psutil
from typing import List, Dict, Callable, Any, Optional, Tuple, Union

def process_segments_parallel(
    segments: List[Dict],
    process_func: Callable,
    max_workers: int = 2,
    **kwargs
) -> List[str]:
    """
    Xử lý các phân đoạn video song song

    Args:
        segments: <PERSON>h sách các phân đoạn cần xử lý
        process_func: Hàm xử lý một phân đoạn
        max_workers: Số lượng worker tối đa
        **kwargs: <PERSON><PERSON><PERSON> tham số bổ sung cho hàm xử lý

    Returns:
        Danh sách đường dẫn đến các file đã xử lý
    """
    processed_segments = []

    # Kiểm tra số lượng phân đoạn
    if not segments:
        return processed_segments

    # Giới hạn số lượng worker dựa trên số lượng phân đoạn
    actual_workers = min(max_workers, len(segments))

    print(f"🚀 Xử lý song song với {actual_workers} worker")
    logging.info(f"Xử lý song song với {actual_workers} worker")

    # Sử dụng ThreadPoolExecutor để xử lý song song
    with concurrent.futures.ThreadPoolExecutor(max_workers=actual_workers) as executor:
        # Tạo future cho mỗi phân đoạn
        future_to_segment = {
            executor.submit(process_func, segment, **kwargs): segment
            for segment in segments
        }

        # Xử lý kết quả khi hoàn thành
        for future in concurrent.futures.as_completed(future_to_segment):
            segment = future_to_segment[future]
            try:
                result = future.result()
                if result:
                    processed_segments.append(result)
                    print(f"✅ Đã xử lý phân đoạn {segment.get('part', '?')}/{len(segments)}")
                else:
                    print(f"❌ Lỗi khi xử lý phân đoạn {segment.get('part', '?')}/{len(segments)}")
            except Exception as e:
                print(f"❌ Lỗi khi xử lý phân đoạn {segment.get('part', '?')}/{len(segments)}: {str(e)}")
                logging.error(f"Lỗi khi xử lý phân đoạn {segment.get('part', '?')}: {str(e)}")

    return processed_segments

class WorkerPool:
    """
    Enhanced worker pool for parallel processing with resource management
    """
    def __init__(self, max_workers: Optional[int] = None, name: str = "WorkerPool",
                 min_memory_per_worker_gb: float = 2.0, cpu_usage_threshold: float = 85.0):
        """
        Initialize worker pool with resource management

        Args:
            max_workers: Maximum number of workers (None for auto-detection)
            name: Name of the worker pool for logging
            min_memory_per_worker_gb: Minimum memory per worker in GB
            cpu_usage_threshold: CPU usage threshold to throttle workers
        """
        self.name = name
        self.min_memory_per_worker_gb = min_memory_per_worker_gb
        self.cpu_usage_threshold = cpu_usage_threshold

        # Auto-detect optimal number of workers if not specified
        if max_workers is None:
            self.max_workers = self._get_optimal_workers()
        else:
            self.max_workers = max_workers

        self.executor = None
        self.active_workers = 0
        self.total_tasks = 0
        self.completed_tasks = 0
        self.failed_tasks = 0
        self._lock = threading.RLock()
        self._resource_monitor = None
        self._should_stop = False

        logging.info(f"{self.name} initialized with {self.max_workers} workers")

    def _get_optimal_workers(self) -> int:
        """
        Get optimal number of workers based on system resources

        Returns:
            Optimal number of workers
        """
        # Get CPU count
        cpu_count = os.cpu_count() or 4

        # Get available memory
        mem_info = psutil.virtual_memory()
        available_memory_gb = mem_info.available / (1024 * 1024 * 1024)

        # Calculate workers based on memory
        memory_based_workers = int(available_memory_gb / self.min_memory_per_worker_gb)

        # Calculate workers based on CPU
        cpu_based_workers = max(1, cpu_count // 2)

        # Take the minimum of the two
        optimal_workers = min(memory_based_workers, cpu_based_workers)

        # Cap at 8 workers to avoid excessive resource usage
        return min(optimal_workers, 8)

    def _monitor_resources(self):
        """
        Monitor system resources and adjust active workers
        """
        while not self._should_stop:
            try:
                # Check CPU usage
                cpu_usage = psutil.cpu_percent(interval=1)

                # Check memory usage
                mem_info = psutil.virtual_memory()
                available_memory_gb = mem_info.available / (1024 * 1024 * 1024)

                # Calculate maximum workers based on current resources
                memory_based_workers = int(available_memory_gb / self.min_memory_per_worker_gb)

                # Adjust active workers if CPU usage is too high
                with self._lock:
                    if cpu_usage > self.cpu_usage_threshold and self.active_workers > 1:
                        # Reduce active workers
                        self.active_workers = max(1, self.active_workers - 1)
                        logging.info(f"{self.name}: High CPU usage ({cpu_usage:.1f}%), reducing active workers to {self.active_workers}")
                    elif cpu_usage < self.cpu_usage_threshold * 0.7 and self.active_workers < self.max_workers and self.active_workers < memory_based_workers:
                        # Increase active workers
                        self.active_workers = min(self.max_workers, self.active_workers + 1, memory_based_workers)
                        logging.info(f"{self.name}: Low CPU usage ({cpu_usage:.1f}%), increasing active workers to {self.active_workers}")

                # Log resource usage periodically
                logging.debug(f"{self.name}: CPU: {cpu_usage:.1f}%, Memory: {available_memory_gb:.1f}GB available, Active workers: {self.active_workers}")

                # Sleep for a while
                time.sleep(5)
            except Exception as e:
                logging.error(f"{self.name}: Error in resource monitor: {e}")
                time.sleep(10)

    def start(self):
        """
        Start the worker pool
        """
        if self.executor is not None:
            logging.warning(f"{self.name} is already running")
            return

        self.active_workers = self.max_workers
        self.total_tasks = 0
        self.completed_tasks = 0
        self.failed_tasks = 0
        self._should_stop = False

        # Create thread pool executor
        self.executor = concurrent.futures.ThreadPoolExecutor(
            max_workers=self.max_workers,
            thread_name_prefix=self.name
        )

        # Start resource monitor
        self._resource_monitor = threading.Thread(
            target=self._monitor_resources,
            name=f"{self.name}-ResourceMonitor",
            daemon=True
        )
        self._resource_monitor.start()

        logging.info(f"{self.name} started with {self.max_workers} workers")

    def stop(self):
        """
        Stop the worker pool
        """
        if self.executor is None:
            logging.warning(f"{self.name} is not running")
            return

        # Signal resource monitor to stop
        self._should_stop = True

        # Shutdown executor
        self.executor.shutdown(wait=True)
        self.executor = None

        logging.info(f"{self.name} stopped. Completed: {self.completed_tasks}, Failed: {self.failed_tasks}, Total: {self.total_tasks}")

    def submit(self, fn: Callable, *args, **kwargs) -> concurrent.futures.Future:
        """
        Submit a task to the worker pool

        Args:
            fn: Function to execute
            *args: Arguments to pass to the function
            **kwargs: Keyword arguments to pass to the function

        Returns:
            Future object
        """
        if self.executor is None:
            self.start()

        with self._lock:
            self.total_tasks += 1

        # Wrap the function to track completion
        def wrapped_fn(*args, **kwargs):
            try:
                result = fn(*args, **kwargs)
                with self._lock:
                    self.completed_tasks += 1
                return result
            except Exception as e:
                with self._lock:
                    self.failed_tasks += 1
                logging.error(f"{self.name}: Task failed: {e}")
                raise

        # Submit the wrapped function
        return self.executor.submit(wrapped_fn, *args, **kwargs)

    def map(self, fn: Callable, *iterables, timeout: Optional[float] = None, chunksize: int = 1) -> List[Any]:
        """
        Map a function to multiple iterables

        Args:
            fn: Function to execute
            *iterables: Iterables to map
            timeout: Timeout in seconds
            chunksize: Chunk size for mapping

        Returns:
            List of results
        """
        if self.executor is None:
            self.start()

        # Count total tasks
        total_items = min(len(iterable) for iterable in iterables) if iterables else 0
        with self._lock:
            self.total_tasks += total_items

        # Wrap the function to track completion
        def wrapped_fn(*args):
            try:
                result = fn(*args)
                with self._lock:
                    self.completed_tasks += 1
                return result
            except Exception as e:
                with self._lock:
                    self.failed_tasks += 1
                logging.error(f"{self.name}: Task failed: {e}")
                raise

        # Map the wrapped function
        return list(self.executor.map(wrapped_fn, *iterables, timeout=timeout, chunksize=chunksize))

    def __enter__(self):
        """
        Enter context manager
        """
        self.start()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        Exit context manager
        """
        self.stop()

def get_optimal_worker_count() -> int:
    """
    Get optimal number of workers based on system resources

    Returns:
        Optimal number of workers
    """
    # Get CPU count
    cpu_count = os.cpu_count() or 4

    # Get available memory
    mem_info = psutil.virtual_memory()
    available_memory_gb = mem_info.available / (1024 * 1024 * 1024)

    # Calculate workers based on memory (assume 2GB per worker)
    memory_based_workers = int(available_memory_gb / 2.0)

    # Calculate workers based on CPU
    cpu_based_workers = max(1, cpu_count // 2)

    # Take the minimum of the two
    optimal_workers = min(memory_based_workers, cpu_based_workers)

    # Cap at 8 workers to avoid excessive resource usage
    return min(optimal_workers, 8)

def parallel_map(fn: Callable, items: List[Any], max_workers: Optional[int] = None,
                 timeout: Optional[float] = None, show_progress: bool = False) -> List[Any]:
    """
    Map a function to items in parallel

    Args:
        fn: Function to execute
        items: Items to process
        max_workers: Maximum number of workers (None for auto-detection)
        timeout: Timeout in seconds
        show_progress: Whether to show progress

    Returns:
        List of results
    """
    if not items:
        return []

    # Auto-detect optimal number of workers if not specified
    if max_workers is None:
        max_workers = get_optimal_worker_count()

    # Create worker pool
    with WorkerPool(max_workers=max_workers) as pool:
        # Map function to items
        if show_progress:
            results = []
            futures = [pool.submit(fn, item) for item in items]

            # Wait for futures with progress
            for i, future in enumerate(concurrent.futures.as_completed(futures)):
                try:
                    result = future.result(timeout=timeout)
                    results.append(result)
                    logging.info(f"Progress: {i+1}/{len(items)} ({(i+1)/len(items)*100:.1f}%)")
                except Exception as e:
                    logging.error(f"Task failed: {e}")
                    results.append(None)

            return results
        else:
            # Simple map without progress
            return pool.map(fn, items, timeout=timeout)
