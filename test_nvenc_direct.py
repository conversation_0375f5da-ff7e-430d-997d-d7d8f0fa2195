#!/usr/bin/env python3
"""
Direct test of NVENC encoding to verify it's working
"""
import subprocess
import time
import threading
import os

def monitor_gpu_encoding():
    """Monitor GPU encoding units during test"""
    print("🎮 Monitoring GPU encoding units...")
    
    for i in range(30):  # Monitor for 30 seconds
        try:
            result = subprocess.run([
                'nvidia-smi', 'dmon', '-s', 'pucvmet', '-c', '1'
            ], capture_output=True, text=True, timeout=3)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                if len(lines) >= 2:
                    data_line = lines[-1]
                    values = data_line.split()
                    if len(values) >= 7:
                        gpu_util = int(values[1])
                        enc_util = int(values[3])
                        
                        status = "🎬 ENCODING" if enc_util > 0 else ("🚀 GPU ACTIVE" if gpu_util > 30 else "💤 IDLE")
                        print(f"  GPU: {gpu_util:2d}% | ENC: {enc_util:2d}% | {status}")
                        
                        if enc_util > 0:
                            print(f"  ✅ NVENC DETECTED! Encoding utilization: {enc_util}%")
                            return True
        except:
            pass
        
        time.sleep(1)
    
    return False

def test_nvenc_encoding():
    """Test NVENC encoding directly"""
    print("🧪 Testing NVENC encoding directly...")
    
    # Create test input if it doesn't exist
    if not os.path.exists("test_input.mp4"):
        print("Creating test input video...")
        subprocess.run([
            'ffmpeg', '-y', '-f', 'lavfi', '-i', 'testsrc2=duration=10:size=1920x1080:rate=30',
            '-f', 'lavfi', '-i', 'sine=frequency=1000:duration=10',
            '-c:v', 'libx264', '-c:a', 'aac', 'test_input.mp4'
        ], capture_output=True)
    
    # Test NVENC encoding
    cmd = [
        'ffmpeg', '-y',
        '-i', 'test_input.mp4',
        '-c:v', 'h264_nvenc',
        '-preset', 'fast',
        '-cq', '23',
        '-c:a', 'aac',
        'test_nvenc_output.mp4'
    ]
    
    print(f"🔧 Command: {' '.join(cmd)}")
    
    # Start monitoring in background
    monitor_thread = threading.Thread(target=monitor_gpu_encoding)
    monitor_thread.daemon = True
    monitor_thread.start()
    
    # Run encoding
    start_time = time.time()
    result = subprocess.run(cmd, capture_output=True, text=True)
    end_time = time.time()
    
    print(f"\n📊 Results:")
    print(f"  Return code: {result.returncode}")
    print(f"  Duration: {end_time - start_time:.2f} seconds")
    
    if result.returncode == 0:
        print("  ✅ NVENC encoding successful!")
        
        # Check output file
        if os.path.exists('test_nvenc_output.mp4'):
            file_size = os.path.getsize('test_nvenc_output.mp4')
            print(f"  📁 Output file size: {file_size / (1024*1024):.2f} MB")
            
            # Check if it was actually encoded with NVENC
            probe_result = subprocess.run([
                'ffprobe', '-v', 'quiet', '-select_streams', 'v:0',
                '-show_entries', 'stream=encoder', '-of', 'csv=p=0',
                'test_nvenc_output.mp4'
            ], capture_output=True, text=True)
            
            if probe_result.returncode == 0:
                encoder = probe_result.stdout.strip()
                print(f"  🔧 Encoder used: {encoder}")
                if 'nvenc' in encoder.lower():
                    print("  ✅ Confirmed: NVENC was used!")
                else:
                    print("  ❌ Warning: NVENC may not have been used")
    else:
        print("  ❌ NVENC encoding failed!")
        print(f"  Error: {result.stderr}")
    
    # Wait for monitoring to complete
    monitor_thread.join(timeout=5)

def test_cpu_vs_gpu_performance():
    """Compare CPU vs GPU encoding performance"""
    print("\n🏁 Performance Comparison: CPU vs GPU")
    
    if not os.path.exists("test_input.mp4"):
        return
    
    # Test CPU encoding
    print("\n🔧 Testing CPU encoding (libx264)...")
    cmd_cpu = [
        'ffmpeg', '-y',
        '-i', 'test_input.mp4',
        '-c:v', 'libx264',
        '-preset', 'medium',
        '-crf', '23',
        '-c:a', 'aac',
        'test_cpu_output.mp4'
    ]
    
    start_time = time.time()
    result_cpu = subprocess.run(cmd_cpu, capture_output=True, text=True)
    cpu_time = time.time() - start_time
    
    # Test GPU encoding
    print("🚀 Testing GPU encoding (h264_nvenc)...")
    cmd_gpu = [
        'ffmpeg', '-y',
        '-i', 'test_input.mp4',
        '-c:v', 'h264_nvenc',
        '-preset', 'fast',
        '-cq', '23',
        '-c:a', 'aac',
        'test_gpu_output.mp4'
    ]
    
    start_time = time.time()
    result_gpu = subprocess.run(cmd_gpu, capture_output=True, text=True)
    gpu_time = time.time() - start_time
    
    print(f"\n📊 Performance Results:")
    print(f"  CPU (libx264): {cpu_time:.2f} seconds")
    print(f"  GPU (h264_nvenc): {gpu_time:.2f} seconds")
    
    if cpu_time > 0 and gpu_time > 0:
        speedup = cpu_time / gpu_time
        print(f"  🚀 GPU Speedup: {speedup:.2f}x faster")
        
        if speedup > 1.5:
            print("  ✅ GPU encoding is significantly faster!")
        elif speedup > 1.1:
            print("  ✅ GPU encoding is faster")
        else:
            print("  ⚠️ GPU encoding may not be much faster")

if __name__ == "__main__":
    print("🎮 NVENC Direct Test")
    print("=" * 50)
    
    # Test NVENC encoding
    test_nvenc_encoding()
    
    # Performance comparison
    test_cpu_vs_gpu_performance()
    
    print("\n🏁 Test complete!")
