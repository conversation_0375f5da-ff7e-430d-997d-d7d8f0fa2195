import os
import re
import sys
import shutil

def create_new_processor():
    """Tạo file advanced_video_processor.py mới với tên kênh ở phía dưới video"""
    
    # Đường dẫn đến file gốc và file mới
    original_file = "src/processors/advanced_video_processor.py"
    backup_file = "src/processors/advanced_video_processor.py.bak"
    
    # Tạo bản sao lưu
    shutil.copy2(original_file, backup_file)
    print(f"Đã tạo bản sao lưu: {backup_file}")
    
    # Đọc nội dung file
    with open(original_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Thêm hàm clean_text_for_display nếu chưa có
    has_clean_text_function = False
    for line in lines:
        if "def clean_text_for_display(" in line:
            has_clean_text_function = True
            break
    
    if not has_clean_text_function:
        # Tìm vị trí để thêm hàm
        import_end_index = 0
        for i, line in enumerate(lines):
            if line.strip().startswith("import ") or line.strip().startswith("from "):
                import_end_index = i
        
        # Thêm hàm clean_text_for_display sau các import
        clean_text_function = """
def clean_text_for_display(text):
    # Loại bỏ các ký tự đặc biệt có thể gây lỗi trong filter
    text = re.sub(r'[\'\\\\]', '', text)
    # Thay thế các ký tự đặc biệt khác bằng khoảng trắng
    text = re.sub(r'[^\\w\\s]', ' ', text)
    # Loại bỏ khoảng trắng thừa
    text = re.sub(r'\\s+', ' ', text).strip()
    return text
"""
        lines.insert(import_end_index + 1, clean_text_function)
    
    # Tìm và sửa tất cả các phần filter không có tên kênh
    new_lines = []
    i = 0
    while i < len(lines):
        line = lines[i]
        
        # Kiểm tra xem đây có phải là dòng bắt đầu của phần filter không
        if "# Không sử dụng title_filter và pad nữa, crop thành 1080x1080" in line:
            # Kiểm tra xem dòng tiếp theo có phải là return filter không
            if i + 1 < len(lines) and "return f\"[0:v]scale=1080:1080:force_original_aspect_ratio=increase,crop=1080:1080:(iw-1080)/2:(ih-1080)/2,setpts=PTS/1.1[v];[0:a]atempo=1.1[a]\"" in lines[i + 1]:
                # Thay thế bằng filter có tên kênh
                new_lines.append("                # Thêm tên kênh vào phía dưới video\n")
                new_lines.append("                # Tìm font chữ có sẵn cho tên kênh\n")
                new_lines.append("                font_path = find_available_font()\n")
                new_lines.append("                \n")
                new_lines.append("                # Thêm tên kênh vào phía dưới video\n")
                new_lines.append("                channel_text = \"\"\n")
                new_lines.append("                if font_path and hasattr(self, 'channel_name') and self.channel_name:\n")
                new_lines.append("                    # Xử lý tên kênh để tránh lỗi với các ký tự đặc biệt\n")
                new_lines.append("                    channel_name = self.channel_name\n")
                new_lines.append("                    # Loại bỏ các ký tự đặc biệt có thể gây lỗi trong filter\n")
                new_lines.append("                    safe_channel_name = re.sub(r'[^\\w\\s]', ' ', channel_name)\n")
                new_lines.append("                    # Loại bỏ khoảng trắng thừa\n")
                new_lines.append("                    safe_channel_name = re.sub(r'\\s+', ' ', safe_channel_name).strip()\n")
                new_lines.append("                    \n")
                new_lines.append("                    # Làm sạch tên kênh để hiển thị trong video\n")
                new_lines.append("                    clean_channel_name = clean_text_for_display(safe_channel_name)\n")
                new_lines.append("                    \n")
                new_lines.append("                    # Escape đường dẫn font để tránh lỗi với FFmpeg\n")
                new_lines.append("                    escaped_font_path = font_path.replace('\\\\', '\\\\\\\\') if font_path else \"\"\n")
                new_lines.append("                    \n")
                new_lines.append("                    # Thêm tên kênh vào phía dưới video\n")
                new_lines.append("                    channel_text = f\",drawtext=text='{clean_channel_name}':fontfile='{escaped_font_path}':fontsize=50:fontcolor=white:box=1:boxcolor=black@0.5:boxborderw=5:x=(w-text_w)/2:y=h-th-50\"\n")
                new_lines.append("                    \n")
                new_lines.append("                    print(f\"🔤 Đã thêm tên kênh '{clean_channel_name}' vào phía dưới video\")\n")
                new_lines.append("                \n")
                new_lines.append("                # Thêm channel_text vào filter\n")
                new_lines.append("                return f\"[0:v]scale=1080:1080:force_original_aspect_ratio=increase,crop=1080:1080:(iw-1080)/2:(ih-1080)/2,setpts=PTS/1.1{channel_text}[v];[0:a]atempo=1.1[a]\"\n")
                
                # Bỏ qua dòng tiếp theo (dòng return)
                i += 1
            else:
                new_lines.append(line)
        # Kiểm tra xem đây có phải là dòng bắt đầu của phần filter bị lỗi không
        elif "# Không sử dụng tiêu đề nữa" in line and i + 2 < len(lines) and "# Thêm tên kênh vào phía dưới video" in lines[i + 2]:
            # Bỏ qua các dòng bị lỗi
            new_lines.append(line)
            i += 1
            while i < len(lines) and "def _process_segment_wrapper" not in lines[i]:
                i += 1
            new_lines.append(lines[i])
        else:
            new_lines.append(line)
        
        i += 1
    
    # Ghi nội dung đã sửa vào file gốc
    with open(original_file, 'w', encoding='utf-8') as f:
        f.writelines(new_lines)
    
    print(f"Đã tạo file mới với tên kênh ở phía dưới video: {original_file}")

if __name__ == "__main__":
    create_new_processor()
