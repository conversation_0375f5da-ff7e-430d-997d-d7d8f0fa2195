from setuptools import setup, find_packages

setup(
    name="video-processor",
    version="1.0.0",
    packages=find_packages(),
    install_requires=[
        "yt-dlp>=2025.3.31",
        "rich>=13.0.0",
        "pydantic>=2.0.0",
    ],
    entry_points={
        "console_scripts": [
            "video-processor=src.main:main",
        ],
    },
    author="Augment Code",
    author_email="<EMAIL>",
    description="A professional YouTube video downloader and processor",
    keywords="youtube, video, download, process",
    python_requires=">=3.7",
)
