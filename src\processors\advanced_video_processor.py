"""
Module xử lý video nâng cao với nhiều hiệu ứng và tính năng
"""
import os
import re
import json
import random
import subprocess
import tempfile
import logging
import time
import glob
import psutil  # Thêm import psutil để kiểm tra tài nguyên hệ thống
import multiprocessing
import shutil  # Sử dụng trong cleanup()
from typing import List, Dict, Optional
from pathlib import Path
from ..utils.helpers import find_available_font
# Sử dụng hàm clean_text_for_display được định nghĩa trong file này thay vì import

# Đã xóa import Pillow vì chỉ sử dụng drawtext
PILLOW_AVAILABLE = False

# Import module xử lý song song
try:
    from ..utils.parallel_processor import process_segments_parallel
    # Kích hoạt xử lý song song để tăng tốc độ xử lý
    PARALLEL_PROCESSING_AVAILABLE = True
    # Xác định số lượng worker tối đa dựa trên CPU
    MAX_WORKERS = max(1, multiprocessing.cpu_count() - 1)  # Để lại 1 core cho hệ thống
    logging.info(f"Xử lý song song được kích hoạt với {MAX_WORKERS} worker")
except ImportError:
    PARALLEL_PROCESSING_AVAILABLE = False
    MAX_WORKERS = 1
    logging.warning("Module xử lý song song không khả dụng, sử dụng xử lý tuần tự")

class AdvancedVideoProcessor:
    """Xử lý video với các tính năng nâng cao"""

    def __init__(
        self,
        input_path: str,
        output_dir: str,
        channel_name: Optional[str] = None,
        video_title: Optional[str] = None,
        video_info: Optional[Dict] = None,
        settings: Optional[Dict] = None
    ):
        """Khởi tạo đối tượng xử lý video nâng cao

        Args:
            input_path: Đường dẫn đến file video đầu vào
            output_dir: Thư mục đầu ra
            channel_name: Tên kênh (tùy chọn)
            video_title: Tiêu đề video (tùy chọn)
            video_info: Thông tin video từ YouTube (tùy chọn)
            settings: Cấu hình xử lý (tùy chọn)
        """
        self.input_path = input_path
        self.output_dir = output_dir
        self.video_title = video_title or os.path.basename(input_path).split('.')[0]
        self.video_info = video_info or {}
        self.channel_name = channel_name
        self.settings = settings or {}
        # Không sử dụng subtitle_path vì đã vô hiệu hóa chức năng phụ đề

        # Tạo thư mục tạm để lưu trữ các file trung gian
        self.temp_dir = tempfile.mkdtemp()

        # Phân tích thông tin video
        self.video_metadata = self._analyze_video()

        # Trích xuất tên kênh nếu chưa được cung cấp
        if not self.channel_name:
            self.channel_name = self._extract_channel_name()

        # Trích xuất keywords
        self.keywords = self._extract_keywords()

        # Không tạo phụ đề tự động, chỉ sử dụng phụ đề từ YouTube nếu có

    def __del__(self):
        """Dọn dẹp thư mục tạm khi đối tượng bị hủy"""
        self.cleanup()

    # Phương thức optimize_title_text đã bị xóa vì phụ thuộc vào Pillow - chỉ sử dụng drawtext

    # Phương thức create_title_image đã bị xóa theo yêu cầu - chỉ sử dụng drawtext

    def cleanup(self):
        """Dọn dẹp tài nguyên và file tạm - phiên bản tối ưu hóa"""
        if not hasattr(self, 'temp_dir') or not os.path.exists(self.temp_dir):
            return

        try:
            # Giải phóng bộ nhớ bằng cách xóa các thuộc tính lớn
            large_attributes = ['video_metadata', 'segments', 'keywords', 'current_segment']
            for attr in large_attributes:
                if hasattr(self, attr):
                    setattr(self, attr, None)

            # Gọi garbage collector để giải phóng bộ nhớ
            import gc
            gc.collect()

            # Xóa các file phụ đề .vtt và .srt trong thư mục gốc
            for ext in ['.vtt', '.srt']:
                for file in glob.glob(f"*{ext}"):
                    try:
                        os.remove(file)
                        logging.debug(f"Xóa file phụ đề: {file}")
                    except Exception as e:
                        logging.debug(f"Không thể xóa file {file}: {str(e)}")

            # Xóa file trạng thái nếu có
            state_file = os.path.join(self.temp_dir, "processing_state.json")
            if os.path.exists(state_file):
                try:
                    os.remove(state_file)
                    logging.debug(f"Đã xóa file trạng thái: {state_file}")
                except Exception as e:
                    logging.debug(f"Không thể xóa file trạng thái: {str(e)}")

            # Xóa các file tạm thời trong thư mục tạm
            try:
                temp_files_count = 0
                for root, dirs, files in os.walk(self.temp_dir):
                    for file in files:
                        try:
                            file_path = os.path.join(root, file)
                            os.remove(file_path)
                            temp_files_count += 1
                        except Exception as e:
                            logging.debug(f"Không thể xóa file {file_path}: {str(e)}")

                if temp_files_count > 0:
                    logging.info(f"Đã xóa {temp_files_count} file tạm trong thư mục {self.temp_dir}")
            except Exception as e:
                logging.debug(f"Lỗi khi xóa các file trong thư mục tạm: {str(e)}")

            # Sử dụng shutil.rmtree để xóa thư mục tạm và tất cả nội dung bên trong
            try:
                shutil.rmtree(self.temp_dir, ignore_errors=True)
                logging.info(f"Xóa thư mục tạm: {self.temp_dir}")
            except Exception as e:
                logging.debug(f"Không thể xóa thư mục tạm bằng shutil.rmtree: {str(e)}")
                # Nếu shutil.rmtree không hoạt động, thử phương pháp thủ công
                try:
                    # Xóa tất cả các thư mục con
                    for root, dirs, _ in os.walk(self.temp_dir, topdown=False):
                        for dir in dirs:
                            try:
                                dir_path = os.path.join(root, dir)
                                os.rmdir(dir_path)
                            except Exception as e:
                                logging.debug(f"Không thể xóa thư mục con {dir_path}: {str(e)}")

                    # Xóa thư mục gốc
                    try:
                        os.rmdir(self.temp_dir)
                        logging.info(f"Đã xóa thư mục tạm: {self.temp_dir}")
                    except Exception as e:
                        logging.debug(f"Không thể xóa thư mục tạm {self.temp_dir}: {str(e)}")
                except Exception as e:
                    logging.debug(f"Lỗi khi xóa thư mục tạm thủ công: {str(e)}")

            # Xóa các file tạm thời khác trong thư mục hiện tại
            temp_patterns = ['*.part', '*.temp.*', '*_temp.*', '*.tmp', '*.bak', 'title_*.png']
            for pattern in temp_patterns:
                for file in glob.glob(pattern):
                    try:
                        os.remove(file)
                        logging.debug(f"Đã xóa file tạm: {file}")
                    except Exception as e:
                        logging.debug(f"Không thể xóa file tạm {file}: {str(e)}")

            # Gọi lại garbage collector sau khi xóa file
            gc.collect()

            logging.info("Đã hoàn thành dọn dẹp tài nguyên")

        except Exception as e:
            logging.error(f"Lỗi khi dọn dẹp tài nguyên: {str(e)}")

    def _analyze_video(self) -> Dict:
        """Phân tích thông tin video đầu vào"""
        metadata = {
            'duration': 0,
            'width': 0,
            'height': 0,
            'fps': 0,
            'bitrate': 0,
            'audio': False,
            'is_vertical': False,
            'aspect_ratio': 0
        }

        try:
            # Sử dụng FFprobe để lấy thông tin
            cmd = [
                'ffprobe',
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_format',
                '-show_streams',
                self.input_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, check=True, encoding='utf-8', errors='replace')
            info = json.loads(result.stdout)

            # Lấy thông tin từ kết quả
            if 'format' in info:
                metadata['duration'] = float(info['format'].get('duration', 0))
                metadata['bitrate'] = int(info['format'].get('bit_rate', 0)) // 1000  # Convert to kbps

            # Lấy thông tin từ các stream
            for stream in info.get('streams', []):
                if stream.get('codec_type') == 'video':
                    metadata['width'] = stream.get('width', 0)
                    metadata['height'] = stream.get('height', 0)

                    # Tính FPS
                    fps_str = stream.get('r_frame_rate', '0/1')
                    if '/' in fps_str:
                        num, den = map(int, fps_str.split('/'))
                        metadata['fps'] = num / den if den else 0

                    # Xác định tỷ lệ khung hình và hướng video
                    if metadata['width'] and metadata['height']:
                        metadata['aspect_ratio'] = metadata['width'] / metadata['height']
                        metadata['is_vertical'] = metadata['height'] > metadata['width']

                elif stream.get('codec_type') == 'audio':
                    metadata['audio'] = True

            logging.info(f"📊 Thông tin video:")
            logging.info(f"   - Độ phân giải: {metadata['width']}x{metadata['height']}")
            logging.info(f"   - Thời lượng: {metadata['duration']:.2f}s")
            logging.info(f"   - FPS: {metadata['fps']}")
            logging.info(f"   - Bitrate: {metadata['bitrate']}kbps")
            logging.info(f"   - Tỷ lệ khung hình: {metadata['aspect_ratio']:.2f}")
            logging.info(f"   - Loại video: {'Dọc (9:16)' if metadata['is_vertical'] else 'Ngang (16:9)'}")

        except Exception as e:
            logging.error(f"⚠️ Không thể phân tích thông tin video: {str(e)}")

        return metadata

    def _extract_channel_name(self) -> str:
        """Trích xuất tên kênh từ đường dẫn"""
        try:
            # Nếu có thông tin video, thử lấy tên kênh từ đó
            if self.video_info and 'channel' in self.video_info:
                return self.video_info['channel']

            # Nếu không, thử trích xuất từ đường dẫn output
            path_parts = Path(self.output_dir).parts

            # Tìm phần tử cuối cùng không phải là "new folder" hoặc tương tự
            for part in reversed(path_parts):
                if part.lower() not in ['new folder', 'videos', 'download', 'downloads']:
                    # Loại bỏ số ở đầu nếu có
                    channel_name = re.sub(r'^\d+\s+', '', part)
                    return channel_name

            # Nếu không tìm thấy, trả về tên thư mục cha
            return Path(self.output_dir).name

        except Exception as e:
            logging.error(f"⚠️ Không thể trích xuất tên kênh: {str(e)}")
            return "CHANNEL"

    def _create_auto_subtitle(self) -> None:
        """Tạo phụ đề tự động cho video - Đã vô hiệu hóa hoàn toàn để tăng tốc độ xử lý"""
        # Tính năng phụ đề đã bị vô hiệu hóa hoàn toàn để tăng tốc độ xử lý
        return None

    # Các phương thức liên quan đến phụ đề đã bị loại bỏ để tối ưu hóa code

    def _extract_keywords(self) -> List[str]:
        """Trích xuất keywords từ tiêu đề và tags của video"""
        keywords = []

        try:
            # Lấy từ tiêu đề
            if self.video_title:
                # Xử lý tiêu đề đa ngôn ngữ - sử dụng regex khác để hỗ trợ các ký tự Unicode
                # Tách các từ bằng khoảng trắng hoặc dấu cách
                title_parts = re.split(r'[\s\|\-\,\.\:\;]+', self.video_title)

                # Loại bỏ các từ phổ biến (stop words) - chỉ áp dụng cho tiếng Anh
                stop_words = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'about', 'like', 'through', 'over', 'before', 'after', 'between', 'under', 'during', 'without', 'of', 'from']

                # Lọc các từ có ít nhất 2 ký tự
                title_keywords = []
                for word in title_parts:
                    word = word.lower().strip()
                    # Kiểm tra xem từ có phải là tiếng Anh không
                    is_english = all(ord(c) < 128 for c in word)
                    # Nếu là tiếng Anh, loại bỏ stop words
                    if is_english:
                        if word not in stop_words and len(word) > 2:
                            title_keywords.append(word)
                    # Nếu không phải tiếng Anh, giữ nguyên nếu đủ dài
                    elif len(word) >= 2:
                        title_keywords.append(word)

                keywords.extend(title_keywords)

            # Lấy từ tags nếu có
            if self.video_info and 'tags' in self.video_info:
                tags = self.video_info['tags']
                if isinstance(tags, list):
                    keywords.extend(tags)

            # Thêm từ khóa từ tên kênh
            if self.channel_name:
                channel_keywords = re.findall(r'\w+', self.channel_name.lower())
                channel_keywords = [word for word in channel_keywords if len(word) > 2]
                keywords.extend(channel_keywords)

            # Thêm các từ khóa theo chủ đề phổ biến - phân loại theo ngôn ngữ
            # Xác định ngôn ngữ chính của video dựa trên tiêu đề
            is_russian = False
            is_english = True

            if self.video_title:
                # Kiểm tra xem có nhiều ký tự Cyrillic (tiếng Nga) không
                cyrillic_count = sum(1 for c in self.video_title if 0x0400 <= ord(c) <= 0x04FF)
                if cyrillic_count > len(self.video_title) * 0.3:  # Nếu hơn 30% là ký tự Cyrillic
                    is_russian = True
                    is_english = False

            # Từ khóa phổ biến (luôn thêm) - loại bỏ tiktok
            viral_keywords = ['viral', 'trending', 'fyp']
            keywords.extend(viral_keywords)

            # Từ khóa theo ngôn ngữ
            if is_russian:
                # Từ khóa tiếng Nga
                russian_topics = [
                    'для', 'мини', 'стол', 'фрезер', 'мастерской',
                    'дерево', 'инструмент', 'своимируками', 'самодельный'
                ]
                # Chọn ngẫu nhiên 3-4 từ khóa tiếng Nga
                russian_count = random.randint(3, 4)
                if russian_count <= len(russian_topics):
                    random_russian = random.sample(russian_topics, russian_count)
                    keywords.extend(random_russian)
            else:
                # Từ khóa tiếng Anh theo chủ đề
                english_topics = {
                    # Công nghệ & DIY
                    'tech': ['tech', 'technology', 'howto', 'tutorial', 'diy', 'tips', 'tricks', 'woodworking', 'tools', 'maker'],
                    # Giải trí
                    'fun': ['fun', 'funny', 'comedy', 'laugh', 'entertainment', 'amazing', 'wow'],
                    # Khác
                    'other': ['satisfying', 'relaxing', 'creative', 'inspiration', 'design', 'craft', 'handmade']
                }

                # Chọn ngẫu nhiên 1 từ khóa từ mỗi nhóm
                for _, topics in english_topics.items():
                    if topics:
                        random_topic = random.choice(topics)
                        keywords.append(random_topic)

            # Loại bỏ trùng lặp
            keywords = list(set(keywords))

            # Làm sạch từ khóa cho TikTok (không có khoảng trắng, ký tự đặc biệt)
            cleaned_keywords = []
            for kw in keywords:
                # Xử lý khác nhau cho từ tiếng Anh và không phải tiếng Anh
                is_english = all(ord(c) < 128 for c in kw)

                if is_english:
                    # Đối với tiếng Anh, loại bỏ ký tự đặc biệt và khoảng trắng
                    cleaned_kw = re.sub(r'[^\w]', '', kw.lower())
                    # Đảm bảo từ khóa có ít nhất 3 ký tự
                    if len(cleaned_kw) >= 3:
                        cleaned_keywords.append(cleaned_kw)
                else:
                    # Đối với ngôn ngữ khác, chỉ loại bỏ khoảng trắng và một số ký tự đặc biệt
                    cleaned_kw = kw.lower().strip()
                    # Loại bỏ khoảng trắng và một số ký tự đặc biệt
                    cleaned_kw = re.sub(r'[\s\,\.\!\?]', '', cleaned_kw)
                    # Đảm bảo từ khóa có ít nhất 2 ký tự
                    if len(cleaned_kw) >= 2:
                        cleaned_keywords.append(cleaned_kw)

            # Sắp xếp theo độ dài để ưu tiên từ khóa ngắn
            cleaned_keywords = sorted(cleaned_keywords, key=len)

            # Đảm bảo luôn có các từ khóa quan trọng - loại bỏ tiktok
            important_keywords = ['fyp', 'viral', 'trending']
            for kw in important_keywords:
                if kw not in cleaned_keywords:
                    cleaned_keywords.append(kw)

            # Đảm bảo luôn có đủ 6 từ khóa
            if len(cleaned_keywords) < 6:
                # Thêm các từ khóa mặc định nếu chưa đủ
                default_keywords = ['craft', 'howto', 'diy', 'laugh', 'wow', 'amazing', 'explore', 'learn']
                for kw in default_keywords:
                    if kw not in cleaned_keywords:
                        cleaned_keywords.append(kw)
                        if len(cleaned_keywords) >= 6:
                            break

            # Giới hạn số lượng từ khóa đúng 6 từ khóa
            return cleaned_keywords[:6]  # Lấy đúng 6 từ khóa

        except Exception as e:
            logging.error(f"⚠️ Không thể trích xuất keywords: {str(e)}")
            # Trả về các từ khóa mặc định nếu có lỗi - loại bỏ tiktok
            return ['viral', 'trending', 'fyp', 'foryou', 'explore', 'wow', 'amazing']

    def _generate_flip_timeline(self, segment_duration: float = None) -> List[Dict]:
        """Tạo timeline cho hiệu ứng lật ngang video

        Args:
            segment_duration: Độ dài của phân đoạn cần tạo timeline. Nếu None, sử dụng độ dài toàn bộ video.
        """
        timeline = []

        try:
            # Sử dụng độ dài phân đoạn nếu được cung cấp, nếu không sử dụng độ dài toàn bộ video
            duration = segment_duration if segment_duration is not None else self.video_metadata['duration']

            if duration < 10:  # Video quá ngắn, không lật
                return timeline

            # Số lần lật ngẫu nhiên (15-20 lần) - Tăng số lượng lật để lách bản quyền tốt hơn
            num_flips = random.randint(15, 20)

            # Đảm bảo không lật quá nhiều nếu video ngắn
            num_flips = min(num_flips, int(duration / 8))  # Giảm khoảng cách giữa các lần lật

            # Đảm bảo có ít nhất 1 lần lật nếu độ dài đủ lớn
            num_flips = max(num_flips, 1) if duration >= 10 else 0

            # Chia video thành các phần tương đối đều nhau
            flip_segment_duration = duration / (num_flips + 1) if num_flips > 0 else 0

            # Tạo danh sách các zoom level khác nhau để tăng tính ngẫu nhiên
            zoom_levels = [1.15, 1.2, 1.25, 1.3, 1.35]

            for i in range(num_flips):
                # Thời điểm bắt đầu lật (ngẫu nhiên trong phân đoạn)
                start_time = flip_segment_duration * (i + 0.5) + random.uniform(-flip_segment_duration/3, flip_segment_duration/3)
                start_time = max(0, min(start_time, duration - 5))  # Đảm bảo không vượt quá độ dài video

                # Thời gian lật (2-6 giây) - Tăng tính ngẫu nhiên của thời gian lật
                flip_duration = random.uniform(2, 6)

                # Đảm bảo không vượt quá độ dài video
                if start_time + flip_duration > duration:
                    flip_duration = duration - start_time

                if flip_duration >= 1:  # Chỉ thêm nếu đủ dài
                    # Chọn một zoom level ngẫu nhiên
                    zoom = random.choice(zoom_levels)

                    # Điều chỉnh thời điểm bắt đầu theo vị trí của phân đoạn trong video gốc
                    timeline.append({
                        'start': start_time,
                        'duration': flip_duration,
                        'zoom': zoom  # Zoom ngẫu nhiên
                    })

            if segment_duration is None:
                logging.info(f"🔄 Đã tạo timeline với {len(timeline)} lần lật ngang")
            else:
                print(f"🔄 Đã tạo timeline với {len(timeline)} lần lật ngang cho phân đoạn {duration:.1f}s")

        except Exception as e:
            logging.error(f"⚠️ Không thể tạo timeline lật ngang: {str(e)}")

        return timeline

    def _generate_segments(self) -> List[Dict]:
        """Tạo các phân đoạn video với độ dài ngẫu nhiên"""
        segments = []

        try:
            duration = self.video_metadata['duration']

            if duration < 60:  # Video quá ngắn, không cắt
                segments.append({
                    'start': 0,
                    'duration': duration,
                    'part': 1
                })
                return segments

            # Thời gian còn lại cần cắt
            remaining_duration = duration
            current_time = 0
            part = 1

            while remaining_duration > 0:
                # Độ dài phân đoạn ngẫu nhiên từ 1'30s đến 2'30s (90s đến 150s)
                segment_duration = random.uniform(90, 150)
                # Làm tròn độ dài phân đoạn để dễ đọc
                segment_duration = round(segment_duration, 1)

                # Nếu phần còn lại ngắn hơn 1'10s (70s) và không phải phân đoạn đầu tiên
                if remaining_duration < 70 and part > 1:
                    # Gộp với phân đoạn trước đó
                    segments[-1]['duration'] += remaining_duration
                    print(f"Gộp {remaining_duration:.1f}s còn lại vào phân đoạn trước đó (tổng: {segments[-1]['duration']:.1f}s)")
                    break

                # Nếu phần còn lại ngắn hơn độ dài phân đoạn
                if remaining_duration < segment_duration:
                    segment_duration = remaining_duration

                segments.append({
                    'start': current_time,
                    'duration': segment_duration,
                    'part': part
                })

                current_time += segment_duration
                remaining_duration -= segment_duration
                part += 1

            # Hiển thị thông tin chi tiết về các phân đoạn
            logging.info(f"✂️ Đã tạo {len(segments)} phân đoạn video")
            for i, segment in enumerate(segments):
                print(f"Phân đoạn {i+1}: {segment['duration']:.1f}s")

        except Exception as e:
            logging.error(f"⚠️ Không thể tạo phân đoạn video: {str(e)}")

            # Nếu có lỗi, tạo một phân đoạn duy nhất
            segments.append({
                'start': 0,
                'duration': self.video_metadata['duration'],
                'part': 1
            })

        return segments

    def _create_complex_filter(self, flip_timeline: List[Dict]) -> str:
        """Tạo complex filter cho FFmpeg với hiệu ứng lật ngang và zoom"""
        try:
            # Xác định tỷ lệ khung hình và crop nếu cần
            width = self.video_metadata['width']
            height = self.video_metadata['height']
            is_vertical = self.video_metadata['is_vertical']

            filters = []

            # Tăng tốc độ video 1.1x và giữ nguyên fps gốc (tối đa 30fps)
            # Lấy fps gốc nếu có
            fps = 30  # Mặc định 30fps
            if 'fps' in self.video_metadata and self.video_metadata['fps'] > 0:
                # Giới hạn fps tối đa là 30
                fps = min(30, self.video_metadata['fps'])
                print(f"\ud83c\udfa5 Sử dụng FPS gốc: {fps} (giới hạn tối đa 30fps)")

            # Tăng tốc độ video và giữ nguyên fps
            filters.append(f"[0:v]setpts={1/1.1}*PTS,fps={fps}[speed]")

            # Crop video ngang thành 1:1, giữ nguyên video dọc
            if not is_vertical:  # Video ngang (16:9)
                # Crop thành 1:1
                crop_size = min(width, height)
                x = (width - crop_size) // 2
                y = (height - crop_size) // 2
                filters.append(f"[speed]crop={crop_size}:{crop_size}:{x}:{y}[cropped]")
                last_output = "cropped"
            else:  # Video dọc (9:16)
                # Giữ nguyên
                last_output = "speed"

            # Thêm hiệu ứng lách bản quyền
            # 1. Thay đổi màu sắc nhẹ
            filters.append(f"[{last_output}]hue=s=0.9:h=0.02[color]")
            last_output = "color"

            # 2. Thêm noise nhẹ
            filters.append(f"[{last_output}]noise=alls=2:allf=t[noise]")
            last_output = "noise"

            # Xử lý lật ngang video theo timeline
            if flip_timeline:
                # Tạo các phân đoạn video
                segments = []
                last_end = 0

                for i, flip in enumerate(flip_timeline):
                    start = flip['start']
                    duration = flip['duration']
                    zoom = flip['zoom']

                    # Phân đoạn trước khi lật
                    if start > last_end:
                        filters.append(f"[{last_output}]trim=start={last_end}:end={start},setpts=PTS-STARTPTS[normal{i}]")
                        segments.append(f"[normal{i}]")

                    # Phân đoạn lật
                    filters.append(f"[{last_output}]trim=start={start}:end={start+duration},setpts=PTS-STARTPTS[flip_base{i}]")

                    # Thêm hiệu ứng lật và zoom
                    filters.append(f"[flip_base{i}]hflip,scale=iw*{zoom}:ih*{zoom},crop=iw/{zoom}:ih/{zoom}:(iw-iw/{zoom})/2:(ih-ih/{zoom})/2[flipped{i}]")
                    segments.append(f"[flipped{i}]")

                    last_end = start + duration

                # Phân đoạn cuối cùng sau khi lật
                if last_end < self.video_metadata['duration']:
                    filters.append(f"[{last_output}]trim=start={last_end}:end={self.video_metadata['duration']},setpts=PTS-STARTPTS[normal_last]")
                    segments.append(f"[normal_last]")

                # Nối các phân đoạn lại với nhau
                filters.append(f"{''.join(segments)}concat=n={len(segments)}:v=1:a=0[video_with_effects]")
                last_output = "video_with_effects"

            # Thêm watermark là tên kênh với font chữ đậm và opacity 75%
            # Tìm font chữ có sẵn
            font_path = find_available_font()

            if font_path and self.channel_name:
                # Sử dụng drawtext để thêm watermark
                # Xử lý tên kênh để tránh lỗi với các ký tự đặc biệt
                # Giữ nguyên chữ hoa và chữ thường trong tên kênh
                channel_name = self.channel_name
                # Loại bỏ các ký tự đặc biệt có thể gây lỗi trong filter
                safe_channel_name = re.sub(r'[^\w\s]', '', channel_name)
                # Đặt watermark ở góc dưới bên phải với box trong suốt 75%
                # Làm sạch tên kênh để hiển thị trong video, giữ lại các ký tự Cyrillic
                clean_channel_name = clean_text_for_display(safe_channel_name)

                # Sử dụng font thay vì fontfile
                font_name = os.path.basename(font_path) if font_path and os.path.exists(font_path) else ""
                font_param = f"font='{font_name}'" if font_name else "fontfile='{font_path}'"

                filters.append(f"[{last_output}]drawtext=text='{clean_channel_name}':{font_param}:fontsize=36:fontcolor=white@0.75:box=1:boxcolor=black@0.75:boxborderw=5:x=w-text_w-20:y=h-text_h-20:shadowcolor=black@0.5:shadowx=2:shadowy=2[watermarked]")
            else:
                # Nếu không có font hoặc tên kênh, sử dụng drawbox đơn giản
                logging.warning("\u26a0️ Không tìm thấy font hoặc tên kênh, sử dụng drawbox thay thế")
                filters.append(f"[{last_output}]drawbox=x=(iw-iw/4)/2:y=(ih-ih/4)/2:w=iw/4:h=ih/4:color=white@0.3:t=fill[watermarked]")

            # Xử lý âm thanh
            # Tăng tốc độ âm thanh 1.1x
            filters.append(f"[0:a]atempo=1.1[audio]")

            # Kết hợp video và âm thanh
            complex_filter = ";".join(filters)

            # Sử dụng filter đơn giản thay thế để tránh lỗi Unicode
            complex_filter = self._create_simple_filter()
            logging.info(f"✅ Đã tạo complex filter đơn giản không chứa ký tự Unicode")

            # Không hiển thị complex filter trên console, chỉ ghi vào log
            logging.debug(f"Complex filter: {complex_filter}")

            return complex_filter

        except Exception as e:
            logging.error(f"⚠️ Không thể tạo complex filter: {str(e)}")

            # Trả về filter đơn giản nếu có lỗi
            # Sử dụng filter đơn giản hơn, không sử dụng concat
            # Lấy fps gốc nếu có
            fps = 30  # Mặc định 30fps
            if 'fps' in self.video_metadata and self.video_metadata['fps'] > 0:
                # Giới hạn fps tối đa là 30
                fps = min(30, self.video_metadata['fps'])

            # Thêm watermark vào filter đơn giản
            font_path = find_available_font()
            if font_path and self.channel_name:
                # Xử lý tên kênh để tránh lỗi với các ký tự đặc biệt
                # Giữ nguyên chữ hoa và chữ thường trong tên kênh
                channel_name = self.channel_name
                # Loại bỏ các ký tự đặc biệt có thể gây lỗi trong filter
                safe_channel_name = re.sub(r'[^\w\s]', '', channel_name)

                # Xử lý video ngang và dọc khác nhau
                width = self.video_metadata['width']
                height = self.video_metadata['height']
                is_vertical = self.video_metadata['is_vertical']

                if not is_vertical:  # Video ngang (16:9)
                    # Crop thành 1:1
                    crop_size = min(width, height)
                    x = (width - crop_size) // 2
                    y = (height - crop_size) // 2
                    # Loại bỏ hiệu ứng hflip khỏi toàn bộ video, chỉ áp dụng cho các đoạn cụ thể theo timeline
                    # Làm sạch tên kênh để hiển thị trong video, giữ lại các ký tự Cyrillic
                    clean_channel_name = clean_text_for_display(safe_channel_name)

                    # Sử dụng font thay vì fontfile
                    font_name = os.path.basename(font_path) if font_path and os.path.exists(font_path) else ""
                    font_param = f"font='{font_name}'" if font_name else "fontfile='{font_path}'"

                    return f"[0:v]setpts=0.909*PTS,fps={fps},crop={crop_size}:{crop_size}:{x}:{y},hue=s=0.9:h=0.02,scale=iw*1.25:ih*1.25,crop=iw/1.25:ih/1.25:(iw-iw/1.25)/2:(ih-ih/1.25)/2,drawtext=text='{clean_channel_name}':{font_param}:fontsize=36:fontcolor=white@0.75:box=1:boxcolor=black@0.75:boxborderw=5:x=w-text_w-20:y=h-text_h-20:shadowcolor=black@0.5:shadowx=2:shadowy=2[watermarked];[0:a]atempo=1.1[audio]"
                else:  # Video dọc (9:16)
                    # Giữ nguyên video dọc
                    print(f"📱 Phát hiện video dọc (9:16) - Giữ nguyên tỷ lệ khung hình")
                    # Tính toán kích thước đầu ra giữ nguyên tỷ lệ 9:16
                    target_width = 1080
                    target_height = int(target_width * height / width)
                    # Đảm bảo chiều cao là số chẵn
                    target_height = target_height + (target_height % 2)
                    # Làm sạch tên kênh để hiển thị trong video, giữ lại các ký tự Cyrillic
                    clean_channel_name = clean_text_for_display(safe_channel_name)

                    # Sử dụng font thay vì fontfile
                    font_name = os.path.basename(font_path) if font_path and os.path.exists(font_path) else ""
                    font_param = f"font='{font_name}'" if font_name else "fontfile='{font_path}'"

                    return f"[0:v]setpts=0.909*PTS,fps={fps},scale={target_width}:{target_height},hue=s=0.9:h=0.02,drawtext=text='{clean_channel_name}':{font_param}:fontsize=36:fontcolor=white@0.75:box=1:boxcolor=black@0.75:boxborderw=5:x=w-text_w-20:y=h-text_h-20:shadowcolor=black@0.5:shadowx=2:shadowy=2[watermarked];[0:a]atempo=1.1[audio]"
            else:
                # Nếu không có font hoặc tên kênh, trả về filter không có watermark
                # Xử lý video ngang và dọc khác nhau
                width = self.video_metadata['width']
                height = self.video_metadata['height']
                is_vertical = self.video_metadata['is_vertical']

                if not is_vertical:  # Video ngang (16:9)
                    # Crop thành 1:1
                    crop_size = min(width, height)
                    x = (width - crop_size) // 2
                    y = (height - crop_size) // 2
                    # Loại bỏ hiệu ứng hflip khỏi toàn bộ video, chỉ áp dụng cho các đoạn cụ thể theo timeline
                    return f"[0:v]setpts=0.909*PTS,fps={fps},crop={crop_size}:{crop_size}:{x}:{y},hue=s=0.9:h=0.02,scale=iw*1.25:ih*1.25,crop=iw/1.25:ih/1.25:(iw-iw/1.25)/2:(ih-ih/1.25)/2[watermarked];[0:a]atempo=1.1[audio]"
                else:  # Video dọc (9:16)
                    # Giữ nguyên video dọc
                    print(f"📱 Phát hiện video dọc (9:16) - Giữ nguyên tỷ lệ khung hình")
                    # Tính toán kích thước đầu ra giữ nguyên tỷ lệ 9:16
                    target_width = 1080
                    target_height = int(target_width * height / width)
                    # Đảm bảo chiều cao là số chẵn
                    target_height = target_height + (target_height % 2)
                    return f"[0:v]setpts=0.909*PTS,fps={fps},scale={target_width}:{target_height},hue=s=0.9:h=0.02[watermarked];[0:a]atempo=1.1[audio]"

    def _cleanup_state_files_in_output_dir(self):
        """Xóa các file trạng thái trong thư mục đầu ra"""
        try:
            if hasattr(self, 'output_dir') and os.path.exists(self.output_dir):
                # Tìm và xóa các file processing_state_*.json trong thư mục đầu ra
                for file in os.listdir(self.output_dir):
                    if file.startswith("processing_state_") and file.endswith(".json"):
                        try:
                            file_path = os.path.join(self.output_dir, file)
                            os.remove(file_path)
                            logging.info(f"Da xoa file trang thai trong thu muc dau ra: {file}")
                        except Exception as e:
                            logging.error(f"Khong the xoa file trang thai {file}: {str(e)}")
        except Exception as e:
            logging.error(f"Loi khi xoa cac file trang thai trong thu muc dau ra: {str(e)}")

    def _create_intro_video(self, segment_path: str, duration: float = 5.0) -> str:
        """Tạo video intro ngắn 5 giây từ các clip ngắn trong video - Đã vô hiệu hóa để tăng tốc độ xử lý

        Args:
            segment_path: Đường dẫn đến file video gốc (không sử dụng, giữ để tương thích ngược)
            duration: Độ dài của intro (không sử dụng, giữ để tương thích ngược)

        Returns:
            Đường dẫn trống vì chức năng đã bị vô hiệu hóa
        """
        # Đã vô hiệu hóa chức năng tạo intro để tăng tốc độ xử lý
        # Sử dụng các tham số để tránh cảnh báo IDE
        if segment_path and duration > 0:
            logging.debug(f"Bỏ qua tạo intro cho {segment_path} với độ dài {duration}s")
        logging.info("Chức năng tạo intro đã bị vô hiệu hóa để tăng tốc độ xử lý")
        return ""

    def _get_title_filter(self):
        """Tạo filter cho tiêu đề video - đã vô hiệu hóa

        Returns:
            Chuỗi rỗng vì đã vô hiệu hóa việc thêm tiêu đề
        """
        # Không thêm tiêu đề nữa
        return ""

    def _create_simple_filter(self, flip_timeline=None, segment=None):
        """Tạo complex filter đơn giản không chứa ký tự Unicode với hiệu ứng lật ngang và watermark

        Args:
            flip_timeline: Timeline cho hiệu ứng lật ngang
            segment: Thông tin về phân đoạn đang xử lý (để hiển thị tiêu đề với số phần chính xác)
        """
        try:
            # Lấy thông tin về hướng video (dọc hay ngang)
            is_vertical = self.video_metadata['is_vertical']

            # Không thêm tên kênh vào video bằng drawtext để tránh lỗi font
            # Thông báo về việc bỏ qua thêm tên kênh
            if hasattr(self, 'channel_name') and self.channel_name:
                print(f"ℹ️ Bỏ qua thêm tên kênh '{self.channel_name}' để tránh lỗi font")

            # Đã vô hiệu hóa hoàn toàn chức năng phụ đề để tăng tốc độ xử lý

            # Xác định loại hiệu ứng âm thanh dựa trên trạng thái hiện tại
            current_audio_effect = getattr(self, 'current_audio_effect', 'complex')

            # Chọn hiệu ứng âm thanh phù hợp - đã sửa để đồng bộ hóa âm thanh và hình ảnh
            if current_audio_effect == 'complex':
                # Hiệu ứng âm thanh phức tạp (mặc định) - chỉ sử dụng atempo=1.1 để đồng bộ với video
                audio_filter = "atempo=1.1,volume=1.05"
                print("Sử dụng hiệu ứng âm thanh: phức tạp")
            elif current_audio_effect == 'simple':
                # Hiệu ứng âm thanh đơn giản - chỉ sử dụng atempo=1.1 để đồng bộ với video
                audio_filter = "atempo=1.1"
                print("Sử dụng hiệu ứng âm thanh: đơn giản")
            else:  # 'minimal' hoặc bất kỳ giá trị nào khác
                # Hiệu ứng âm thanh tối thiểu
                audio_filter = "atempo=1.1"
                print("Sử dụng hiệu ứng âm thanh: tối thiểu")

            # Xử lý video ngang thành 9:16 (1080x1920) với phần đen phía trên và dưới
            if not is_vertical:  # Video ngang (16:9)
                # Lấy tiêu đề video để hiển thị ở phần đen phía trên
                title_text = ""
                if hasattr(self, 'video_title') and self.video_title:
                    # Loại bỏ các ký tự đặc biệt có thể gây lỗi trong filter nhưng giữ lại khoảng trắng và dấu nháy đơn
                    # Thay thế các ký tự đặc biệt bằng khoảng trắng để giữ khoảng cách giữa các từ, nhưng giữ lại dấu nháy đơn (')
                    title_text = re.sub(r'[^\w\s\-\.\']+', ' ', self.video_title)
                    # Loại bỏ khoảng trắng thừa
                    title_text = re.sub(r'\s+', ' ', title_text).strip()
                    # Nếu video có nhiều phần và segment được truyền vào, thêm "Part X" vào tiêu đề
                    if segment and hasattr(self, 'segments') and len(self.segments) > 1:
                        part_number = segment.get('part', 1)
                        title_text = f"{title_text} - Part {part_number}"

                    # Không sử dụng tiêu đề nữa

                    else:
                        self.title_line1 = title_text
                        self.title_line2 = ""
                        self.title_needs_two_lines = False

                        # Đoạn code tạo hình ảnh tiêu đề đã bị xóa theo yêu cầu - chỉ sử dụng drawtext
                else:
                    title_text = "Video"

                # Nếu có timeline lật ngang, thêm hiệu ứng hflip
                if flip_timeline and len(flip_timeline) > 0:
                    # Tạo biểu thức điều kiện cho hflip
                    flip_conditions = []
                    for flip in flip_timeline:
                        start = flip['start']
                        end = start + flip['duration']
                        flip_conditions.append(f"between(t,{start},{end})")

                    flip_expr = "+".join(flip_conditions)

                    # Đoạn code tạo hình ảnh tiêu đề đã bị xóa theo yêu cầu - chỉ sử dụng drawtext

                    # Tạo video 1080x1920 với phần đen phía trên và dưới
                    # 1. Scale video để giữ nguyên tỷ lệ khung hình, chiều rộng 1080px
                    # 2. Crop video thành 1:1 (1080x1080)
                    # 3. Áp dụng hiệu ứng lật ngang cho video (không áp dụng cho tiêu đề)
                    # 4. Áp dụng các hiệu ứng khác cho video
                    # 5. Pad video với phần đen phía trên và dưới để tạo thành 1080x1920
                    # 6. Thêm tiêu đề vào phần đen phía trên (sau khi lật để tiêu đề không bị lật)

                    # Không sử dụng title_filter nữa
                    # Không sử dụng watermark_filter nữa để tránh lỗi font
                    complex_filter = f"""
                    [0:v]scale=1080:1080:force_original_aspect_ratio=increase,
                    crop=1080:1080:(iw-1080)/2:(ih-1080)/2,
                    setpts=PTS/1.1,
                    hflip=enable='{flip_expr}',
                    hue=s=0.85:h=0.02,
                    eq=brightness=0.02:saturation=1.1:contrast=1.05,
                    unsharp=5:5:1.0:5:5:0.0,
                    vignette=angle=PI/16
                    [v];
                    [0:a]{audio_filter}[a]
                    """.replace('\n', '')

                    # Ghi complex filter vào log thay vì hiển thị trên console
                    logging.debug(f"Complex filter: {complex_filter}")

                    # Hiển thị thông tin rút gọn
                    print(f"Số lần lật ngang: {len(flip_timeline)}")

                    return complex_filter
                else:
                    # Đoạn code tạo hình ảnh tiêu đề đã bị xóa theo yêu cầu - chỉ sử dụng drawtext

                    # Tạo video 1080x1920 với phần đen phía trên và dưới
                    # 1. Scale video để giữ nguyên tỷ lệ khung hình, chiều rộng 1080px
                    # 2. Crop video thành 1:1 (1080x1080)
                    # 3. Áp dụng các hiệu ứng khác cho video
                    # 4. Pad video với phần đen phía trên và dưới để tạo thành 1080x1920
                    # 5. Thêm tiêu đề vào phần đen phía trên

                    # Không sử dụng title_filter và pad nữa
                    # Không sử dụng watermark_filter nữa để tránh lỗi font
                    complex_filter = f"""
                    [0:v]scale=1080:1080:force_original_aspect_ratio=increase,
                    crop=1080:1080:(iw-1080)/2:(ih-1080)/2,
                    setpts=PTS/1.1,
                    hue=s=0.85:h=0.02,
                    eq=brightness=0.02:saturation=1.1:contrast=1.05,
                    unsharp=5:5:1.0:5:5:0.0,
                    vignette=angle=PI/16
                    [v];
                    [0:a]{audio_filter}[a]
                    """.replace('\n', '')

                    # Ghi complex filter vào log thay vì hiển thị trên console
                    logging.debug(f"Complex filter: {complex_filter}")

                    return complex_filter
            else:  # Video dọc (9:16)
                # Lấy tiêu đề video để hiển thị ở phần đen phía trên
                title_text = ""
                if hasattr(self, 'video_title') and self.video_title:
                    # Loại bỏ các ký tự đặc biệt có thể gây lỗi trong filter nhưng giữ lại khoảng trắng và dấu nháy đơn
                    # Thay thế các ký tự đặc biệt bằng khoảng trắng để giữ khoảng cách giữa các từ, nhưng giữ lại dấu nháy đơn (')
                    title_text = re.sub(r'[^\w\s\-\.\']+', ' ', self.video_title)
                    # Loại bỏ khoảng trắng thừa
                    title_text = re.sub(r'\s+', ' ', title_text).strip()
                    # Nếu video có nhiều phần và segment được truyền vào, thêm "Part X" vào tiêu đề
                    if segment and hasattr(self, 'segments') and len(self.segments) > 1:
                        part_number = segment.get('part', 1)
                        title_text = f"{title_text} - Part {part_number}"

                    # Nếu tiêu đề quá dài, chia thành hai dòng để hiển thị riêng biệt
                    # Giảm ngưỡng độ dài tiêu đề để chia dòng sớm hơn
                    if len(title_text) > 20:
                        words = title_text.split()
                        total_words = len(words)

                        # Tìm điểm chia phù hợp để cân bằng độ dài hai dòng
                        best_diff = float('inf')
                        best_mid_point = total_words // 2

                        # Thử các điểm chia khác nhau để tìm điểm cân bằng nhất
                        # Mở rộng phạm vi tìm kiếm để có nhiều lựa chọn hơn
                        start_range = max(1, total_words // 4)
                        end_range = min(total_words - 1, (total_words * 3) // 4)

                        for i in range(start_range, end_range + 1):
                            line1 = ' '.join(words[:i])
                            line2 = ' '.join(words[i:])

                            # Ưu tiên cân bằng độ dài ký tự hơn là số từ
                            diff = abs(len(line1) - len(line2))

                            # Thêm ưu tiên cho việc chia câu ở dấu câu
                            if i < total_words and words[i-1][-1] in [',', '.', ':', ';', '-', '!', '?']:
                                diff -= 5  # Giảm diff để ưu tiên chia ở dấu câu

                            if diff < best_diff:
                                best_diff = diff
                                best_mid_point = i

                        # Chia tiêu đề thành hai dòng cân bằng
                        self.title_line1 = ' '.join(words[:best_mid_point])
                        self.title_line2 = ' '.join(words[best_mid_point:])

                        # Đảm bảo mỗi dòng không quá dài
                        max_line_length = 30  # Giảm độ dài tối đa xuống 30 ký tự

                        print(f"📏 Chia tiêu đề thành 2 dòng: '{self.title_line1}' và '{self.title_line2}'")

                        # Nếu tiêu đề vẫn quá dài sau khi chia, ghi nhận để điều chỉnh font chữ
                        if len(self.title_line1) > max_line_length or len(self.title_line2) > max_line_length:
                            longest_line = max(len(self.title_line1), len(self.title_line2))
                            print(f"📏 Tiêu đề dài ({longest_line} ký tự), sẽ điều chỉnh kích thước font")

                            # Đoạn code tạo hình ảnh tiêu đề đã bị xóa theo yêu cầu - chỉ sử dụng drawtext

                        # Đặt cờ để biết tiêu đề cần hiển thị trên hai dòng
                        self.title_needs_two_lines = True
                    else:
                        self.title_line1 = title_text
                        self.title_line2 = ""
                        self.title_needs_two_lines = False

                        # Đoạn code tạo hình ảnh tiêu đề đã bị xóa theo yêu cầu - chỉ sử dụng drawtext
                else:
                    title_text = "Video"

                # Giữ nguyên video dọc (không crop)
                print(f"📱 Phát hiện video dọc (9:16) - Xuất dạng 1080x1920 với tiêu đề")
                logging.info(f"📱 Phát hiện video dọc (9:16) - Xuất dạng 1080x1920 với tiêu đề")

                # Lấy kích thước gốc của video
                width = self.video_metadata['width']
                height = self.video_metadata['height']

                # Tính toán kích thước đầu ra giữ nguyên tỷ lệ 9:16
                # Đảm bảo chiều rộng là 1080px (chuẩn cho TikTok)
                target_width = 1080
                # Tính chiều cao để giữ nguyên tỷ lệ
                target_height = int(target_width * height / width)
                # Đảm bảo chiều cao là số chẵn (yêu cầu của video encoding)
                target_height = target_height + (target_height % 2)

                # Nếu có timeline lật ngang, thêm hiệu ứng hflip
                if flip_timeline and len(flip_timeline) > 0:
                    # Tạo biểu thức điều kiện cho hflip
                    flip_conditions = []
                    for flip in flip_timeline:
                        start = flip['start']
                        end = start + flip['duration']
                        flip_conditions.append(f"between(t,{start},{end})")

                    flip_expr = "+".join(flip_conditions)

                    # Đoạn code tạo hình ảnh tiêu đề đã bị xóa theo yêu cầu - chỉ sử dụng drawtext

                    # Tìm font chữ có sẵn cho tên kênh
                    font_path = find_available_font()

                    # Thêm tên kênh vào phía dưới video với font lớn hơn, màu sắc nổi bật hơn và viền đậm hơn
                    channel_text = ""
                    if font_path and hasattr(self, 'channel_name') and self.channel_name:
                        # Xử lý tên kênh để tránh lỗi với các ký tự đặc biệt
                        channel_name = self.channel_name

                        # Không loại bỏ các ký tự Cyrillic, chỉ làm sạch text
                        # Sử dụng trực tiếp clean_text_for_display để giữ lại các ký tự Cyrillic
                        clean_channel_name = clean_text_for_display(channel_name)

                        # Escape đường dẫn font để tránh lỗi với FFmpeg
                        escaped_font_path = font_path.replace('\\', '\\\\') if font_path else ""

                        # Thêm tên kênh vào phía dưới video với font lớn hơn, màu sắc nổi bật hơn và viền đậm hơn
                    channel_text = f",drawtext=text='{clean_channel_name}':fontfile='{escaped_font_path}':fontsize=140:fontcolor=white:box=1:boxcolor=black@0.9:boxborderw=20:x=(w-text_w)/2:y=h-th-100"

                    print(f"🔤 Đã thêm tên kênh '{clean_channel_name}' vào phía dưới video")

                    # Thêm channel_text vào complex_filter
                    complex_filter = f"""
                    [0:v]scale=1080:1080:force_original_aspect_ratio=increase,
                    crop=1080:1080:(iw-1080)/2:(ih-1080)/2,
                    setpts=PTS/1.1,
                    hflip=enable='{flip_expr}',
                    hue=s=0.85:h=0.02,
                    eq=brightness=0.02:saturation=1.1:contrast=1.05,
                    unsharp=5:5:1.0:5:5:0.0,
                    vignette=angle=PI/16{channel_text}[v];
                    [0:a]{audio_filter}[a]
                    """.replace('\n', '')

                    # Ghi complex filter vào log thay vì hiển thị trên console
                    logging.debug(f"Complex filter: {complex_filter}")

                    # Hiển thị thông tin rút gọn
                    print(f"Số lần lật ngang: {len(flip_timeline)}")

                    return complex_filter
                else:
                    # Tìm font chữ có sẵn cho tên kênh
                    font_path = find_available_font()

                    # Thêm tên kênh vào phía dưới video với font lớn hơn, màu sắc nổi bật hơn và viền đậm hơn
                    channel_text = ""
                    if font_path and hasattr(self, 'channel_name') and self.channel_name:
                        # Xử lý tên kênh để tránh lỗi với các ký tự đặc biệt
                        channel_name = self.channel_name

                        # Không loại bỏ các ký tự Cyrillic, chỉ làm sạch text
                        # Sử dụng trực tiếp clean_text_for_display để giữ lại các ký tự Cyrillic
                        clean_channel_name = clean_text_for_display(channel_name)

                        # Escape đường dẫn font để tránh lỗi với FFmpeg
                        escaped_font_path = font_path.replace('\\', '\\\\') if font_path else ""

                        # Thêm tên kênh vào phía dưới video với font lớn hơn, màu sắc nổi bật hơn và viền đậm hơn
                    channel_text = f",drawtext=text='{clean_channel_name}':fontfile='{escaped_font_path}':fontsize=140:fontcolor=white:box=1:boxcolor=black@0.9:boxborderw=20:x=(w-text_w)/2:y=h-th-100"

                    print(f"🔤 Đã thêm tên kênh '{clean_channel_name}' vào phía dưới video")

                    # Thêm channel_text vào complex_filter
                    complex_filter = f"""
                    [0:v]scale=1080:1080:force_original_aspect_ratio=increase,
                    crop=1080:1080:(iw-1080)/2:(ih-1080)/2,
                    setpts=PTS/1.1,
                    hue=s=0.85:h=0.02,
                    eq=brightness=0.02:saturation=1.1:contrast=1.05,
                    unsharp=5:5:1.0:5:5:0.0,
                    vignette=angle=PI/16{channel_text}[v];
                    [0:a]{audio_filter}[a]
                    """.replace('\n', '')

                    # Ghi complex filter vào log thay vì hiển thị trên console
                    logging.debug(f"Complex filter: {complex_filter}")

                    return complex_filter

        except Exception as e:
            logging.error(f"Loi khi tao simple filter: {str(e)}")
            # Trả về một filter đơn giản nhất dựa trên loại video
            try:
                # Kiểm tra lại xem có phải video dọc không
                is_vertical = self.video_metadata.get('is_vertical', False)

                if is_vertical:
                    # Video dọc (9:16) - giữ nguyên tỷ lệ khung hình, thêm tiêu đề
                    logging.info("Sử dụng filter đơn giản cho video dọc (9:16)")
                    # Không sử dụng tiêu đề nữa

                    # Thêm tên kênh vào phía dưới video với font lớn hơn, màu sắc nổi bật hơn và viền đậm hơn
                    # Tìm font chữ có sẵn cho tên kênh
                    font_path = find_available_font()

                    # Thêm tên kênh vào phía dưới video với font lớn hơn, màu sắc nổi bật hơn và viền đậm hơn
                    channel_text = ""
                    if font_path and hasattr(self, 'channel_name') and self.channel_name:
                        # Xử lý tên kênh để tránh lỗi với các ký tự đặc biệt
                        channel_name = self.channel_name
                        # Loại bỏ các ký tự đặc biệt có thể gây lỗi trong filter
                        safe_channel_name = re.sub(r'[^\w\s]', ' ', channel_name)
                        # Loại bỏ khoảng trắng thừa
                        safe_channel_name = re.sub(r'\s+', ' ', safe_channel_name).strip()

                        # Làm sạch tên kênh để hiển thị trong video
                        clean_channel_name = clean_text_for_display(safe_channel_name)

                        # Escape đường dẫn font để tránh lỗi với FFmpeg
                        escaped_font_path = font_path.replace('\\', '\\\\') if font_path else ""

                        # Thêm tên kênh vào phía dưới video với font lớn hơn, màu sắc nổi bật hơn và viền đậm hơn
                    channel_text = f",drawtext=text='{clean_channel_name}':fontfile='{escaped_font_path}':fontsize=140:fontcolor=white:box=1:boxcolor=black@0.9:boxborderw=20:x=(w-text_w)/2:y=h-th-100"

                    print(f"🔤 Đã thêm tên kênh '{clean_channel_name}' vào phía dưới video")

                    # Thêm channel_text vào filter
                    return f"[0:v]scale=1080:1080:force_original_aspect_ratio=increase,crop=1080:1080:(iw-1080)/2:(ih-1080)/2,setpts=PTS/1.1{channel_text}[v];[0:a]atempo=1.1[a]"
                else:
                    # Video ngang (16:9) - crop thành 1:1 và xuất dạng 9:16 với phần đen phía trên và dưới
                    logging.info("Sử dụng filter đơn giản cho video ngang (16:9)")
                    # Không sử dụng tiêu đề nữa

                    # Thêm tên kênh vào phía dưới video với font lớn hơn, màu sắc nổi bật hơn và viền đậm hơn
                    # Tìm font chữ có sẵn cho tên kênh
                    font_path = find_available_font()

                    # Thêm tên kênh vào phía dưới video với font lớn hơn, màu sắc nổi bật hơn và viền đậm hơn
                    channel_text = ""
                    # Kiểm tra xem có bỏ qua tên kênh không (do lỗi font)
                    skip_channel = getattr(self, 'skip_channel_name', False)
                    if skip_channel:
                        print("⚠️ Bỏ qua hiển thị tên kênh do lỗi font")
                        logging.warning("Bỏ qua hiển thị tên kênh do lỗi font")
                    elif font_path and hasattr(self, 'channel_name') and self.channel_name:
                        # Xử lý tên kênh để tránh lỗi với các ký tự đặc biệt
                        channel_name = self.channel_name

                        # Không loại bỏ các ký tự Cyrillic, chỉ làm sạch text
                        # Sử dụng trực tiếp clean_text_for_display để giữ lại các ký tự Cyrillic
                        clean_channel_name = clean_text_for_display(channel_name)

                        # Escape đường dẫn font để tránh lỗi với FFmpeg
                        escaped_font_path = font_path.replace('\\', '\\\\') if font_path else ""

                        # Sử dụng hàm get_font_param từ cyrillic_fix để lấy tham số font phù hợp
                    from src.config.cyrillic_fix import get_font_param

                    # Kiểm tra xem có đang thử lại với font khác không
                    fallback_index = getattr(self, 'font_fallback_index', 0)
                    font_param = get_font_param(font_path, fallback_index)

                    # Thêm tên kênh vào phía dưới video với font lớn hơn, màu sắc nổi bật hơn và viền đậm hơn
                    # Thêm tham số enable='between(t,0,999999)' để đảm bảo text luôn hiển thị
                    channel_text = f",drawtext=text='{clean_channel_name}'{font_param}:fontsize=140:fontcolor=white:box=1:boxcolor=black@0.9:boxborderw=20:x=(w-text_w)/2:y=h-th-100:enable='between(t,0,999999)'"

                    # Ghi log font đang sử dụng
                    logging.info(f"Sử dụng font fallback index {fallback_index} cho tên kênh: {clean_channel_name}")

                    print(f"🔤 Đã thêm tên kênh '{clean_channel_name}' vào phía dưới video")

                    # Thêm channel_text vào filter
                    return f"[0:v]scale=1080:1080:force_original_aspect_ratio=increase,crop=1080:1080:(iw-1080)/2:(ih-1080)/2,setpts=PTS/1.1{channel_text}[v];[0:a]atempo=1.1[a]"
            except:
                # Nếu không thể xác định loại video, sử dụng filter an toàn nhất
                logging.error("Không thể xác định loại video, sử dụng filter an toàn")
                # Không sử dụng tiêu đề nữa

                # Thêm tên kênh vào phía dưới video với font lớn hơn, màu sắc nổi bật hơn và viền đậm hơn
                # Tìm font chữ có sẵn cho tên kênh
                font_path = find_available_font()

                # Thêm tên kênh vào phía dưới video với font lớn hơn, màu sắc nổi bật hơn và viền đậm hơn
                channel_text = ""
                # Kiểm tra xem có bỏ qua tên kênh không (do lỗi font)
                skip_channel = getattr(self, 'skip_channel_name', False)
                if skip_channel:
                    print("⚠️ Bỏ qua hiển thị tên kênh do lỗi font")
                    logging.warning("Bỏ qua hiển thị tên kênh do lỗi font")
                elif font_path and hasattr(self, 'channel_name') and self.channel_name:
                    # Xử lý tên kênh để tránh lỗi với các ký tự đặc biệt
                    channel_name = self.channel_name

                    # Không loại bỏ các ký tự Cyrillic, chỉ làm sạch text
                    # Sử dụng trực tiếp clean_text_for_display để giữ lại các ký tự Cyrillic
                    clean_channel_name = clean_text_for_display(channel_name)

                    # Escape đường dẫn font để tránh lỗi với FFmpeg
                    escaped_font_path = font_path.replace('\\', '\\\\') if font_path else ""

                    # Sử dụng hàm get_font_param từ cyrillic_fix để lấy tham số font phù hợp
                    from src.config.cyrillic_fix import get_font_param

                    # Kiểm tra xem có đang thử lại với font khác không
                    fallback_index = getattr(self, 'font_fallback_index', 0)
                    font_param = get_font_param(font_path, fallback_index)

                    # Thêm tên kênh vào phía dưới video với font lớn hơn, màu sắc nổi bật hơn và viền đậm hơn
                    # Thêm tham số enable='between(t,0,999999)' để đảm bảo text luôn hiển thị
                    channel_text = f",drawtext=text='{clean_channel_name}'{font_param}:fontsize=140:fontcolor=white:box=1:boxcolor=black@0.9:boxborderw=20:x=(w-text_w)/2:y=h-th-100:enable='between(t,0,999999)'"

                    # Ghi log font đang sử dụng
                    logging.info(f"Sử dụng font fallback index {fallback_index} cho tên kênh: {clean_channel_name}")

                    print(f"🔤 Đã thêm tên kênh '{clean_channel_name}' vào phía dưới video")

                # Thêm channel_text vào filter
                return f"[0:v]scale=1080:1080:force_original_aspect_ratio=increase,crop=1080:1080:(iw-1080)/2:(ih-1080)/2,setpts=PTS/1.1{channel_text}[v];[0:a]atempo=1.1[a]"

    def _process_segment_wrapper(self, segment):
        """Wrapper cho phương thức _process_segment để sử dụng trong xử lý song song - phiên bản tối ưu hóa hiệu suất

        Args:
            segment: Phân đoạn cần xử lý, bao gồm cả output_path và flip_timeline

        Returns:
            Đường dẫn đến file đã xử lý hoặc None nếu xử lý thất bại
        """
        max_retries = 3
        output_path = segment.get('output_path')
        flip_timeline = segment.get('flip_timeline', [])
        segment_part = segment.get('part', '?')

        # Lưu thông tin segment hiện tại để sử dụng cho hashtag
        self.current_segment = segment

        # Kiểm tra xem file đã tồn tại chưa và có kích thước hợp lệ không
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            if file_size > 1024 * 1024:  # Lớn hơn 1MB được coi là hợp lệ
                logging.info(f"File đã tồn tại, bỏ qua: {output_path} ({file_size / (1024 * 1024):.2f}MB)")
                print(f"⏩ File đã tồn tại, bỏ qua: {os.path.basename(output_path)} ({file_size / (1024 * 1024):.2f}MB)")
                return output_path
            else:
                # Nếu file tồn tại nhưng kích thước quá nhỏ, có thể bị hỏng
                logging.warning(f"File tồn tại nhưng kích thước quá nhỏ ({file_size / 1024:.2f}KB), xử lý lại")
                print(f"⚠️ File tồn tại nhưng kích thước quá nhỏ ({file_size / 1024:.2f}KB), xử lý lại")
                # Xóa file hỏng
                try:
                    os.remove(output_path)
                    logging.info(f"Đã xóa file hỏng: {output_path}")
                except Exception as e:
                    logging.error(f"Không thể xóa file hỏng: {output_path}, lỗi: {str(e)}")

        # Kiểm tra tài nguyên hệ thống trước khi xử lý
        try:
            # Lấy thông tin CPU và RAM
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            memory_percent = memory.percent

            # Kiểm tra xem có đủ tài nguyên để xử lý không
            if cpu_percent > 90 or memory_percent > 90:
                logging.warning(f"Tài nguyên hệ thống đang cao: CPU {cpu_percent}%, RAM {memory_percent}%")
                print(f"⚠️ Tài nguyên hệ thống đang cao: CPU {cpu_percent}%, RAM {memory_percent}%")

                # Nếu tài nguyên quá cao, chờ lâu hơn
                if cpu_percent > 95 or memory_percent > 95:
                    wait_time = 5
                    print(f"Chờ {wait_time}s để giảm tải hệ thống...")
                    time.sleep(wait_time)
                else:
                    # Chờ 2 giây trước khi tiếp tục để giảm tải hệ thống
                    time.sleep(2)
        except Exception as e:
            logging.debug(f"Không thể kiểm tra tài nguyên hệ thống: {str(e)}")

        # Chiến lược thử lại thông minh với backoff
        backoff_times = [2, 5, 10]  # Thời gian chờ giữa các lần thử (giây)

        # Xử lý phân đoạn với timeline riêng và thử lại nếu gặp lỗi
        for attempt in range(max_retries):
            try:
                # Đặt retry_count để đánh dấu đây là lần thử lại
                if attempt > 0:
                    setattr(self, f"retry_count_{segment_part}", attempt)
                    # Nếu đây là lần thử lại, thử với hiệu ứng đơn giản hơn
                    if attempt == 1:
                        # Lần thử thứ 2: Sử dụng hiệu ứng đơn giản
                        setattr(self, 'current_audio_effect', 'simple')
                        print(f"🔄 Lần thử lại {attempt+1}: Sử dụng hiệu ứng âm thanh đơn giản")
                    elif attempt == 2:
                        # Lần thử thứ 3: Sử dụng hiệu ứng tối thiểu và giảm bitrate
                        setattr(self, 'current_audio_effect', 'minimal')
                        setattr(self, 'reduce_bitrate', True)
                        print(f"🔄 Lần thử lại {attempt+1}: Sử dụng hiệu ứng âm thanh tối thiểu và giảm bitrate")

                print(f"\n⏳ Đang xử lý phân đoạn {segment_part} (lần thử {attempt+1}/{max_retries})...")
                start_time = time.time()

                # Xử lý phân đoạn
                success = self._process_segment(segment, flip_timeline, output_path)
                end_time = time.time()
                processing_time = end_time - start_time

                if success:
                    # Kiểm tra kích thước file đầu ra để đảm bảo chất lượng
                    if os.path.exists(output_path):
                        file_size = os.path.getsize(output_path)
                        file_size_mb = file_size / (1024 * 1024)
                        expected_min_size = segment.get('duration', 10) * 100 * 1024  # Ước tính ~100KB/giây

                        print(f"\n✅ Đã xử lý thành công phân đoạn {segment_part} trong {processing_time:.1f}s")
                        print(f"📊 Kích thước file: {file_size_mb:.2f}MB")

                        # Nếu file quá nhỏ so với thời lượng và đây không phải là lần thử cuối cùng
                        if file_size < expected_min_size and segment.get('duration', 0) > 5 and attempt < max_retries - 1:
                            # Nếu file quá nhỏ so với thời lượng, có thể bị lỗi
                            logging.warning(f"File đầu ra có kích thước nhỏ hơn dự kiến: {file_size_mb:.2f}MB (dự kiến: {expected_min_size / (1024 * 1024):.2f}MB)")
                            print(f"⚠️ File đầu ra có kích thước nhỏ hơn dự kiến, thử lại với chất lượng cao hơn")

                            # Xóa file hiện tại để thử lại
                            try:
                                os.remove(output_path)
                                logging.info(f"Đã xóa file có kích thước nhỏ: {output_path}")
                            except Exception as e:
                                logging.error(f"Không thể xóa file: {output_path}, lỗi: {str(e)}")

                            # Đặt cờ để tăng bitrate trong lần thử tiếp theo
                            setattr(self, 'increase_bitrate', True)

                            # Chờ trước khi thử lại
                            wait_time = backoff_times[attempt]
                            print(f"Chờ {wait_time}s trước khi thử lại...")
                            time.sleep(wait_time)
                            continue

                        # Nếu file đầu ra hợp lệ, reset các thuộc tính tạm thời và trả về đường dẫn
                        self._reset_temp_attributes(segment_part)
                        return output_path
                else:
                    if attempt < max_retries - 1:  # Chỉ hiển thị thông báo nếu còn lần thử lại
                        print(f"⚠️ Thử lại lần {attempt+1}/{max_retries} cho phân đoạn {segment_part}")
                        # Chờ theo chiến lược backoff
                        wait_time = backoff_times[attempt]
                        print(f"Chờ {wait_time}s trước khi thử lại...")
                        time.sleep(wait_time)
            except Exception as e:
                end_time = time.time()
                processing_time = end_time - start_time
                logging.error(f"Lỗi khi xử lý phân đoạn {segment_part} (lần {attempt+1}): {str(e)}")
                print(f"❌ Lỗi khi xử lý phân đoạn {segment_part} (lần {attempt+1}/{max_retries}) sau {processing_time:.1f}s: {str(e)}")

                # Ghi chi tiết lỗi vào log
                import traceback
                logging.debug(f"Chi tiết lỗi phân đoạn {segment_part}: {traceback.format_exc()}")

                # Nếu lỗi có liên quan đến bộ nhớ, giải phóng bộ nhớ ngay lập tức
                if "memory" in str(e).lower() or "allocation" in str(e).lower():
                    print("Phát hiện lỗi bộ nhớ, giải phóng bộ nhớ...")
                    import gc
                    gc.collect()
                    time.sleep(2)  # Chờ thêm thời gian để hệ thống giải phóng bộ nhớ

                # Chờ theo chiến lược backoff
                if attempt < max_retries - 1:
                    wait_time = backoff_times[attempt]
                    print(f"Chờ {wait_time}s trước khi thử lại...")
                    time.sleep(wait_time)

        # Nếu đã thử hết số lần mà vẫn thất bại
        logging.error(f"Không thể xử lý phân đoạn {segment_part} sau {max_retries} lần thử")
        print(f"❌ Không thể xử lý phân đoạn {segment_part} sau {max_retries} lần thử, bỏ qua và tiếp tục")

        # Reset các thuộc tính tạm thời
        self._reset_temp_attributes(segment_part)
        return None

    def _reset_temp_attributes(self, segment_part):
        """Reset các thuộc tính tạm thời sau khi xử lý xong hoặc gặp lỗi"""
        # Danh sách các thuộc tính cần reset
        temp_attrs = [
            f"retry_count_{segment_part}",
            'current_audio_effect',
            'increase_bitrate',
            'reduce_bitrate',
            'retry_count',
            'font_fallback_index',
            'skip_channel_name'
        ]

        # Reset từng thuộc tính nếu tồn tại
        for attr in temp_attrs:
            if hasattr(self, attr):
                delattr(self, attr)

        # Giải phóng bộ nhớ
        import gc
        gc.collect()

    def _process_segment(self, segment: Dict, flip_timeline: List[Dict] = None, output_path: str = None) -> bool:
        """Xử lý một phân đoạn video

        Args:
            segment: Thông tin về phân đoạn cần xử lý
            flip_timeline: Timeline cho hiệu ứng lật ngang (không được sử dụng trong phiên bản đơn giản hóa)
            output_path: Đường dẫn đến file đầu ra

        Returns:
            True nếu xử lý thành công, False nếu thất bại
        """
        # Kiểm tra tài nguyên hệ thống trước khi xử lý
        try:
            # Lấy thông tin CPU và RAM
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            memory_percent = memory.percent

            # Kiểm tra xem có đủ tài nguyên để xử lý không
            if cpu_percent > 95 or memory_percent > 95:
                logging.warning(f"Tài nguyên hệ thống đang quá tải: CPU {cpu_percent}%, RAM {memory_percent}%")
                # Chờ 5 giây trước khi tiếp tục
                time.sleep(5)
        except Exception as e:
            logging.debug(f"Không thể kiểm tra tài nguyên hệ thống: {str(e)}")
        try:
            start = segment['start']
            duration = segment['duration']

            # Không cần lấy thông tin về kích thước video ở đây
            # Vì chúng ta sử dụng _create_simple_filter để tạo complex filter

            # Sử dụng _create_simple_filter để tạo complex filter với hiệu ứng lật ngang
            # Truyền thêm tham số segment để có thể hiển thị tiêu đề với số phần chính xác
            complex_filter = self._create_simple_filter(flip_timeline, segment)

            # Ghi complex filter vào log thay vì hiển thị trên console
            logging.debug(f"Complex filter: {complex_filter}")

            # In thông tin chi tiết về số lần lật ngang
            if flip_timeline and len(flip_timeline) > 0:
                print(f"So lan lat ngang: {len(flip_timeline)} lan")
                # Hiển thị chi tiết về từng lần lật
                for i, flip in enumerate(flip_timeline, 1):
                    print(f"   - Lat {i}: {flip['start']:.1f}s - {flip['start'] + flip['duration']:.1f}s (Zoom: {flip['zoom']}x)")
            else:
                print(f"So lan lat ngang: 0 lan")

            # Kiểm tra xem có thể sử dụng GPU không
            use_gpu = False

            # Kiểm tra GPU NVIDIA RTX 3070 Ti
            try:
                gpu_info = subprocess.run(
                    ["nvidia-smi", "--query-gpu=name", "--format=csv,noheader"],
                    capture_output=True,
                    text=True
                )
                if gpu_info.returncode == 0 and '3070' in gpu_info.stdout.lower():
                    use_gpu = True
                    print(f"Su dung NVIDIA RTX 3070 Ti de xu ly video")
            except:
                pass

            # Đã vô hiệu hóa chức năng tạo intro để tăng tốc độ xử lý

            # Tạo lệnh FFmpeg
            cmd = [
                'ffmpeg', '-y'
            ]

            # Sử dụng GPU cho giải mã - phải đặt trước input
            if use_gpu:
                cmd.extend([
                    '-hwaccel', 'cuda',
                    '-hwaccel_device', '0'
                    # Đã loại bỏ tham số hwaccel_output_format vì không tương thích với một số phiên bản FFmpeg
                ])

            # Thêm các tham số input cho video chính
            cmd.extend([
                '-ss', str(start),
                '-t', str(duration),
                '-i', self.input_path
            ])

            # Đã vô hiệu hóa hoàn toàn chức năng intro để tăng tốc độ xử lý

            # Sử dụng filter_complex và mapping trực tiếp
            cmd.extend([
                    '-filter_complex', complex_filter,
                    '-map', '[v]',
                    '-map', '[a]'
            ])

            # Không thể sử dụng cả -filter_complex và -vf cùng một lúc
            # Nên chúng ta sẽ thêm phụ đề vào complex_filter thay vì sử dụng -vf riêng
            # Đã thêm subtitle_filter vào complex_filter ở phương thức _create_complex_filter

            # Sử dụng GPU nếu có thể
            if use_gpu:
                print(f"Su dung NVIDIA RTX 3070 Ti de xu ly video")

            # In thông tin về thời lượng video
            print(f"\nDang xu ly phan doan {segment['part']}/{len(self.segments)}")
            print(f"Thoi diem: {start:.1f}s - {start + duration:.1f}s (Do dai: {duration:.1f}s)")
            print(f"File dau ra: {os.path.basename(output_path)}")

            # Tối ưu hóa bitrate và fps
            # Lấy bitrate gốc của video nếu có, giới hạn tối đa 10000 kbps theo yêu cầu
            bitrate = 9000  # Mặc định 9000 kbps
            fps = 30  # Mặc định 30fps

            # Kiểm tra xem có thông tin bitrate gốc không
            if 'bit_rate' in self.video_metadata and self.video_metadata['bit_rate'] > 0:
                # Chuyển từ bit/s sang kbps
                original_bitrate = self.video_metadata['bit_rate'] // 1000
                # Sử dụng bitrate gốc nhưng giới hạn tối đa 10000 kbps
                bitrate = min(10000, original_bitrate)
                # Đảm bảo bitrate tối thiểu 8000 kbps để chất lượng luôn tốt
                bitrate = max(8000, bitrate)
                print(f"Su dung bitrate goc: {bitrate}k (gioi han toi da 10000k, toi thieu 8000k)")

            # Lấy fps gốc nếu có
            if 'fps' in self.video_metadata and self.video_metadata['fps'] > 0:
                # Giới hạn fps tối đa là 30
                fps = min(30, self.video_metadata['fps'])
                print(f"Su dung fps goc: {fps} (gioi han toi da 30fps)")

            # Sử dụng GPU nếu có thể
            if use_gpu:
                print(f"Su dung NVIDIA RTX 3070 Ti de xu ly video")
                cmd.extend([
                    '-c:v', 'h264_nvenc',
                    '-preset', 'fast',  # Sử dụng preset tương thích
                    '-profile:v', 'high',  # Profile high cho chất lượng tốt hơn
                    '-pix_fmt', 'yuv420p',
                    '-b:v', f"{bitrate}k",  # Bitrate mục tiêu
                    '-maxrate', f"{bitrate}k",  # Giới hạn maxrate bằng đúng bitrate mục tiêu
                    '-bufsize', f"{bitrate}k",  # Giảm buffer size để đảm bảo CBR
                    '-r', f"{fps}",   # Sử dụng fps gốc của video (tối đa 30fps)
                    '-g', f"{int(fps * 2)}"  # GOP size gấp đôi framerate
                ])
            else:
                cmd.extend([
                    '-c:v', 'libx264',
                    '-preset', 'veryfast',  # Sử dụng veryfast để tăng tốc độ xử lý
                    '-tune', 'film',        # Tối ưu hóa cho nội dung phim
                    '-profile:v', 'high',   # Profile high cho chất lượng tốt hơn
                    '-pix_fmt', 'yuv420p',
                    '-b:v', f"{bitrate}k",  # Bitrate mục tiêu
                    '-maxrate', f"{bitrate}k",  # Giới hạn maxrate bằng đúng bitrate mục tiêu
                    '-minrate', f"{bitrate}k",  # Đặt minrate bằng bitrate mục tiêu để đảm bảo CBR
                    '-bufsize', f"{bitrate}k",  # Giảm buffer size để đảm bảo CBR
                    '-x264-params', f"nal-hrd=cbr:force-cfr=1:bitrate={bitrate}:vbv-maxrate={bitrate}:vbv-bufsize={bitrate}",  # Đảm bảo CBR
                    '-r', f"{fps}",         # Sử dụng fps gốc của video (tối đa 30fps)
                    '-g', f"{int(fps * 2)}",  # GOP size gấp đôi framerate
                    '-bf', '2',             # Giảm số lượng B-frames để tăng tốc độ xử lý
                    '-refs', '2',           # Giảm số lượng frame tham chiếu để tăng tốc độ xử lý
                    '-threads', '0',        # Sử dụng tất cả các lõi CPU có sẵn
                    # Đã loại bỏ tham số x264opts vì có thể gây lỗi với một số phiên bản FFmpeg
                    '-movflags', 'faststart' # Tối ưu hóa cho phát trực tuyến (đã loại bỏ dấu + vì không tương thích)
                ])

            # Tối ưu hóa các tham số âm thanh và đường dẫn đến file đầu ra
            cmd.extend([
                '-c:a', 'aac',
                '-b:a', '192k',    # Tăng bitrate âm thanh để chất lượng tốt hơn
                '-ar', '48000',    # Tần số lấy mẫu cao hơn cho chất lượng tốt hơn
                '-ac', '2',        # Âm thanh stereo
                # Đã loại bỏ tham số aresample=async=1000 vì không tương thích với phiên bản FFmpeg hiện tại
                '-async', '1',     # Đồng bộ hóa audio
                # Tối ưu hóa hiệu suất và giảm thông báo lỗi
                '-hide_banner',
                '-loglevel', 'error',
                '-stats',          # Hiển thị thông tin thống kê
                '-threads', '0',   # Sử dụng tất cả các lõi CPU có sẵn
                '-strict', 'experimental',
                '-shortest',       # Đảm bảo video và audio có cùng độ dài
                '-vsync', '1',     # Sử dụng vsync=1 thay vì cfr để tương thích tốt hơn
                '-async', '1',     # Đồng bộ hóa audio
                '-movflags', 'faststart',  # Tối ưu hóa cho phát trực tuyến (đã loại bỏ write_colr và dấu + vì không tương thích)
                # Tối ưu hóa hiệu suất
                '-max_muxing_queue_size', '9999',  # Tăng kích thước hàng đợi muxing
                '-fflags', 'discardcorrupt',  # Bỏ qua các frame bị hỏng (đã loại bỏ genpts và nofillin vì không tương thích)
                # Đã loại bỏ tham số flags vì không tương thích
                # Đã loại bỏ tham số avioflags vì không tương thích
                # Tối ưu hóa cho xử lý nhanh
                '-probesize', '32M',  # Tăng kích thước probe để phân tích file tốt hơn
                '-analyzeduration', '2G',  # Tăng thời gian phân tích
                output_path
            ])

            # Hiển thị thông tin đơn giản về phân đoạn đang xử lý
            print(f"\nDang xu ly phan doan {segment['part']}/{len(self.segments) if hasattr(self, 'segments') else '?'}")
            print(f"File dau ra: {os.path.basename(output_path)}")

            # Hiển thị thông tin về hiệu ứng lật ngang
            if flip_timeline:
                print(f"So lan lat ngang: {len(flip_timeline)} lan")
                for i, flip in enumerate(flip_timeline, 1):
                    print(f"   - Lat {i}: {flip['start']:.1f}s - {flip['start'] + flip['duration']:.1f}s (Zoom: {flip['zoom']}x)")

            # Không cần tạo file tạm thời vì đã vô hiệu hóa chức năng phụ đề
            # Sử dụng trực tiếp đường dẫn đầu ra cuối cùng
            cmd[-1] = output_path

            # Thực thi lệnh
            logging.info(f"⏳ Đang xử lý phân đoạn {segment['part']}...")
            print(f"\nBat dau xu ly: {time.strftime('%H:%M:%S')}")
            start_time = time.time()

            # Cấu hình fontconfig để hỗ trợ tốt hơn cho ký tự Cyrillic
            env = os.environ.copy()

            # Đặt đường dẫn đến thư mục fonts của dự án
            project_fonts_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "fonts")

            # Thêm thư mục fonts vào biến môi trường FONTCONFIG_PATH
            if os.path.exists(project_fonts_dir):
                env["FONTCONFIG_PATH"] = project_fonts_dir
            else:
                env["FONTCONFIG_PATH"] = "."

            # Thêm biến môi trường để cải thiện việc tìm kiếm font
            env["FC_DEBUG"] = "0"  # Tắt debug để tránh thông báo lỗi

            # Đặt biến môi trường để ưu tiên font Arial cho ký tự Cyrillic
            if os.path.exists(os.path.join(project_fonts_dir, "arial.ttf")):
                env["FC_FONT"] = os.path.join(project_fonts_dir, "arial.ttf")

            # Sử dụng binary mode để tránh lỗi Unicode
            process = subprocess.run(cmd, capture_output=True, text=False, env=env)

            # Xử lý đầu ra và lỗi một cách an toàn
            stderr = ""
            try:
                if process.stderr:
                    stderr = process.stderr.decode('utf-8', errors='replace')
            except Exception as e:
                logging.warning(f"Không thể decode đầu ra của FFmpeg: {str(e)}")
                stderr = "Không thể đọc đầu ra của FFmpeg do lỗi Unicode"

            # Ghi log đầu ra của FFmpeg nếu cần debug
            if process.returncode != 0 and process.stdout:
                try:
                    stdout_text = process.stdout.decode('utf-8', errors='replace')
                    logging.debug(f"FFmpeg stdout: {stdout_text[:500]}..." if len(stdout_text) > 500 else f"FFmpeg stdout: {stdout_text}")
                except:
                    pass

            # Tính thời gian xử lý
            end_time = time.time()
            processing_time = end_time - start_time
            processing_ratio = processing_time / segment['duration']

            if process.returncode != 0:
                # Lọc bỏ các cảnh báo fontconfig
                if stderr and "fontconfig" in stderr.lower():
                    error_msg = "Fontconfig error (ignored)"
                else:
                    error_msg = stderr if stderr else "FFmpeg error: None"

                # Kiểm tra xem có lỗi liên quan đến font không
                if stderr and ("font" in stderr.lower() or "text" in stderr.lower() or "unicode" in stderr.lower() or "utf" in stderr.lower()):
                    logging.error(f"Lỗi liên quan đến font khi xử lý phân đoạn {segment['part']}: {error_msg}")
                    print(f"\nLỗi liên quan đến font khi xử lý phân đoạn {segment['part']}. Thử lại với font khác...")

                    # Tăng chỉ số font fallback để thử với font khác trong lần tiếp theo
                    current_fallback = getattr(self, 'font_fallback_index', 0)
                    setattr(self, 'font_fallback_index', current_fallback + 1)

                    # Nếu đã thử quá nhiều font, thử không hiển thị tên kênh
                    if current_fallback >= 5:  # Đã thử 5 font khác nhau
                        logging.warning("Đã thử quá nhiều font, thử xử lý video không có tên kênh")
                        print("Đã thử quá nhiều font, thử xử lý video không có tên kênh")
                        setattr(self, 'skip_channel_name', True)

                    return False

                logging.error(f"Loi khi xu ly phan doan {segment['part']}: {error_msg}")
                print(f"\nLoi khi xu ly phan doan {segment['part']}: {error_msg[:100]}..." if len(error_msg) > 100 else f"\nLoi khi xu ly phan doan {segment['part']}: {error_msg}")
                return False

            # Kiểm tra xem file đầu ra có tồn tại không
            if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                print(f"\n✅ Đã xử lý video thành công (không thêm phụ đề)")

                # Tính kích thước file
                file_size_mb = os.path.getsize(output_path) / (1024 * 1024)
                logging.info(f"Da xu ly phan doan {segment['part']} thanh cong: {output_path}")
                print(f"\nDa xu ly phan doan {segment['part']} thanh cong!")
                print(f"Thoi gian xu ly: {processing_time:.1f}s (Toc do: {processing_ratio:.2f}x thoi gian thuc)")
                print(f"Kich thuoc file: {file_size_mb:.2f}MB")
                return True
            else:
                logging.error(f"File dau ra khong ton tai hoac rong: {output_path}")
                print(f"\nFile dau ra khong ton tai hoac rong: {output_path}")
                return False

        except Exception as e:
            logging.error(f"Loi khi xu ly phan doan {segment['part']}: {str(e)}")
            print(f"\nLoi khi xu ly phan doan {segment['part']}: {str(e)}")

            # Thử lại tối đa 2 lần nếu xử lý thất bại
            retry_count = getattr(self, 'retry_count', 0) + 1
            setattr(self, 'retry_count', retry_count)

            if retry_count <= 2:
                logging.warning(f"Thu lai lan {retry_count}/2 cho phan doan {segment['part']}")
                print(f"Thu lai lan {retry_count}/2 cho phan doan {segment['part']}")

                # Nếu lỗi có liên quan đến âm thanh, thử với hiệu ứng âm thanh đơn giản hơn
                if "aac" in str(e) or "audio" in str(e) or "sound" in str(e) or "NaN" in str(e) or "Inf" in str(e) or "sync" in str(e).lower():
                    logging.warning("Phát hiện lỗi âm thanh hoặc đồng bộ, thử với hiệu ứng đơn giản hơn")
                    print("Phát hiện lỗi âm thanh hoặc đồng bộ, thử với hiệu ứng đơn giản hơn")
                    # Lưu trữ hiệu ứng âm thanh hiện tại
                    current_audio_effect = getattr(self, 'current_audio_effect', 'complex')

                    # Nếu đang sử dụng hiệu ứng phức tạp, chuyển sang hiệu ứng đơn giản
                    if current_audio_effect == 'complex':
                        setattr(self, 'current_audio_effect', 'simple')
                        print("Chuyển sang hiệu ứng âm thanh đơn giản để đảm bảo đồng bộ hóa tốt hơn")
                    # Nếu đang sử dụng hiệu ứng đơn giản, chuyển sang hiệu ứng tối thiểu
                    elif current_audio_effect == 'simple':
                        setattr(self, 'current_audio_effect', 'minimal')
                        print("Chuyển sang hiệu ứng âm thanh tối thiểu để đảm bảo đồng bộ hóa tốt hơn")

                return self._process_segment(segment, flip_timeline, output_path)
            else:
                logging.error(f"Khong the xu ly phan doan {segment['part']} sau 2 lan thu, bo qua va tiep tuc")
                print(f"Khong the xu ly phan doan {segment['part']} sau 2 lan thu, bo qua va tiep tuc")
                setattr(self, 'retry_count', 0)  # Reset retry count
                setattr(self, 'current_audio_effect', 'complex')  # Reset audio effect
                return False

    def _generate_output_filename(self, segment: Dict) -> str:
        """Tạo tên file đầu ra theo format yêu cầu"""
        try:
            # Lấy tên video gốc (không bao gồm phần mở rộng)
            if self.video_title:
                # Giữ nguyên tiêu đề video, chỉ thay thế các ký tự không hợp lệ trong tên file Windows
                # Windows không cho phép các ký tự \ / : * ? " < > | trong tên file
                base_name = self.video_title

                # Loại bỏ #shorts và @ khỏi tiêu đề trong mọi trường hợp
                base_name = re.sub(r'#shorts', '', base_name, flags=re.IGNORECASE)
                base_name = re.sub(r'#short', '', base_name, flags=re.IGNORECASE)
                # Loại bỏ ký tự @ trong tiêu đề
                base_name = re.sub(r'@\w+', '', base_name)

                # Giữ nguyên dấu : và thay bằng dấu - để tạo tên file hợp lệ
                base_name = base_name.replace(':', ' -')

                # Kiểm tra xem có ký tự Cyrillic không
                has_cyrillic = any(0x0400 <= ord(c) <= 0x04FF for c in base_name)

                if has_cyrillic:
                    # Nếu có ký tự Cyrillic, chuyển đổi thành ASCII để tránh lỗi
                    # Thay thế các ký tự Cyrillic bằng chữ cái Latin tương ứng
                    cyrillic_map = {
                        'а': 'a', 'б': 'b', 'в': 'v', 'г': 'g', 'д': 'd', 'е': 'e', 'ё': 'yo', 'ж': 'zh',
                        'з': 'z', 'и': 'i', 'й': 'y', 'к': 'k', 'л': 'l', 'м': 'm', 'н': 'n', 'о': 'o',
                        'п': 'p', 'р': 'r', 'с': 's', 'т': 't', 'у': 'u', 'ф': 'f', 'х': 'kh', 'ц': 'ts',
                        'ч': 'ch', 'ш': 'sh', 'щ': 'sch', 'ъ': '', 'ы': 'y', 'ь': '', 'э': 'e', 'ю': 'yu',
                        'я': 'ya',
                        'А': 'A', 'Б': 'B', 'В': 'V', 'Г': 'G', 'Д': 'D', 'Е': 'E', 'Ё': 'Yo', 'Ж': 'Zh',
                        'З': 'Z', 'И': 'I', 'Й': 'Y', 'К': 'K', 'Л': 'L', 'М': 'M', 'Н': 'N', 'О': 'O',
                        'П': 'P', 'Р': 'R', 'С': 'S', 'Т': 'T', 'У': 'U', 'Ф': 'F', 'Х': 'Kh', 'Ц': 'Ts',
                        'Ч': 'Ch', 'Ш': 'Sh', 'Щ': 'Sch', 'Ъ': '', 'Ы': 'Y', 'Ь': '', 'Э': 'E', 'Ю': 'Yu',
                        'Я': 'Ya'
                    }

                    transliterated_name = ''
                    for char in base_name:
                        if 0x0400 <= ord(char) <= 0x04FF:  # Nếu là ký tự Cyrillic
                            transliterated_name += cyrillic_map.get(char, '_')
                        else:
                            transliterated_name += char

                    base_name = transliterated_name
                    print(f"🔄 Đã chuyển đổi tên file Cyrillic sang Latin: {base_name}")

                # Thay thế tất cả các ký tự không phải chữ cái, số, khoảng trắng, dấu gạch ngang bằng khoảng trắng
                # Điều này đảm bảo không có ký tự đặc biệt nào trong tên file
                base_name = re.sub(r'[^a-zA-Z0-9 \-]', '', base_name)

                # Loại bỏ khoảng trắng thừa
                base_name = re.sub(r'\s+', ' ', base_name).strip()
            else:
                # Nếu không có tiêu đề, sử dụng tên file đầu vào
                base_name = os.path.basename(self.input_path).split('.')[0]
                # Loại bỏ phần _temp nếu có
                base_name = base_name.replace('_temp', '')

            # Đảm bảo có đúng 6 từ khóa (bao gồm cả 1 tag quan trọng)
            # Sắp xếp từ khóa theo độ dài để ưu tiên từ khóa ngắn
            sorted_keywords = sorted(self.keywords, key=len)

            # Trích xuất từ khóa từ tiêu đề video
            title_keywords = []
            if self.video_title:
                # Trích xuất các từ từ tiêu đề
                title_words = re.findall(r'\w+', self.video_title.lower())
                # Lọc các từ có ít nhất 3 ký tự và chưa có trong danh sách từ khóa
                title_keywords = [word for word in title_words if len(word) >= 3 and word not in sorted_keywords]

            # Trích xuất từ khóa từ tags của YouTube nếu có
            youtube_tags = []
            if self.video_info and 'video_tags' in self.video_info and self.video_info['video_tags']:
                # Lọc các tag có ít nhất 3 ký tự và chưa có trong danh sách từ khóa
                youtube_tags = [tag.lower() for tag in self.video_info['video_tags']
                               if len(tag) >= 3 and tag.lower() not in sorted_keywords]

                # Ghi log số lượng tag từ YouTube để theo dõi
                logging.info(f"Tìm thấy {len(youtube_tags)} tag từ YouTube: {youtube_tags[:10]}...")
                print(f"📋 Tìm thấy {len(youtube_tags)} tag từ YouTube")

            # Kết hợp các từ khóa từ các nguồn khác nhau - ưu tiên YouTube tags
            all_keywords = youtube_tags + title_keywords + sorted_keywords

            # Loại bỏ các từ khóa trùng lặp
            unique_keywords = []
            for kw in all_keywords:
                if kw not in unique_keywords:
                    unique_keywords.append(kw)

            # Nếu vẫn chưa đủ 6 từ khóa, thêm các từ khóa mặc định
            if len(unique_keywords) < 6:
                default_keywords = ['craft', 'howto', 'diy', 'laugh', 'wow', 'amazing', 'explore', 'learn']
                for kw in default_keywords:
                    if kw not in unique_keywords:
                        unique_keywords.append(kw)
                        if len(unique_keywords) >= 6:
                            break

            # Chọn đúng 6 từ khóa đầu tiên
            selected_keywords = unique_keywords[:6]

            # Sử dụng segment ID (nếu có) để tạo hashtag khác nhau cho mỗi video con
            segment_id = 0
            if hasattr(self, 'current_segment') and self.current_segment:
                segment_id = self.current_segment.get('part', 0)

            # Tạo seed ngẫu nhiên dựa trên segment_id để đảm bảo mỗi video con có hashtag khác nhau
            # nhưng vẫn nhất quán khi chạy lại script
            random.seed(segment_id + int(time.time() / 86400))  # Thay đổi seed mỗi ngày

            # Thêm các hashtag phổ biến của TikTok để tăng khả năng xuất hiện trong For You Page
            # Sử dụng segment_id để chọn hashtag khác nhau cho mỗi video, tránh TikTok phát hiện batch rendering
            trending_tags = ['fyp', 'viral', 'trending', 'foryou', 'foryoupage', 'xyzbca', 'tiktok', 'explore',
                            'explorepage', 'fypシ', 'viralvideo', 'tiktoktrending', 'fyppage', 'foryou',
                            'tiktokviral', 'tiktokalgorithm', 'foryoupage', 'fypage']

            # Chọn 2 hashtag ngẫu nhiên từ danh sách trending_tags dựa trên segment_id
            # Đảm bảo mỗi phân đoạn có hashtag khác nhau
            random_trending_tags = random.sample(trending_tags, min(3, len(trending_tags)))

            # Thêm vào danh sách từ khóa đã chọn
            selected_keywords.extend(random_trending_tags)

            print(f"🏷️ Hashtag trending cho phân đoạn {segment_id}: {random_trending_tags}")

            # Danh sách các từ khóa có giá trị cao trong tìm kiếm
            valuable_tags = [
                # Từ khóa chung
                'satisfying', 'oddlysatisfying', 'asmr', 'relaxing', 'tutorial', 'howto', 'lifehack', 'tips', 'trick',
                'amazing', 'awesome', 'incredible', 'mindblowing', 'interesting', 'learn', 'educational', 'creative',
                'art', 'diy', 'handmade', 'homemade', 'craft',
                # Từ khóa theo chủ đề
                'technology', 'tech', 'gadget', 'innovation', 'science', 'experiment', 'engineering', 'coding', 'programming',
                'cooking', 'recipe', 'food', 'baking', 'chef', 'kitchen', 'delicious', 'yummy', 'tasty', 'homecooking',
                'fitness', 'workout', 'exercise', 'gym', 'health', 'wellness', 'motivation', 'inspiration', 'lifestyle',
                'beauty', 'makeup', 'skincare', 'fashion', 'style', 'outfit', 'clothes', 'accessories', 'jewelry',
                'travel', 'adventure', 'explore', 'wanderlust', 'vacation', 'trip', 'journey', 'destination', 'tourism',
                'music', 'song', 'dance', 'singer', 'musician', 'artist', 'band', 'concert', 'performance', 'talent',
                'comedy', 'funny', 'humor', 'joke', 'laugh', 'entertainment', 'fun', 'hilarious', 'meme', 'prank',
                'gaming', 'game', 'videogame', 'gamer', 'gameplay', 'streamer', 'esports', 'console', 'pc', 'mobile',
                'nature', 'animals', 'wildlife', 'pets', 'dog', 'cat', 'bird', 'fish', 'plants', 'flowers', 'garden'
            ]

            # Sử dụng segment ID (nếu có) để tạo hashtag khác nhau cho mỗi video con
            segment_id = 0
            if hasattr(self, 'current_segment') and self.current_segment:
                segment_id = self.current_segment.get('part', 0)

            # Tạo seed ngẫu nhiên dựa trên segment_id để đảm bảo mỗi video con có hashtag khác nhau
            # nhưng vẫn nhất quán khi chạy lại script
            random.seed(segment_id + int(time.time() / 86400))  # Thay đổi seed mỗi ngày

            # Chọn ngẫu nhiên 2 tag phổ biến (1 chính và 1 phụ) dựa trên segment_id
            main_tag_options = ['fyp', 'viral', 'trending', 'tiktok', 'explore']
            secondary_tag_options = ['foryou', 'foryoupage', 'fypシ', 'explorepage', 'viralvideo', 'tiktoktrending']

            # Sử dụng segment_id để chọn tag khác nhau cho mỗi video con
            random_main_tag = main_tag_options[segment_id % len(main_tag_options)]
            random_secondary_tag = secondary_tag_options[(segment_id + 3) % len(secondary_tag_options)]

            # Đảm bảo 2 tag không trùng nhau
            while random_secondary_tag in [random_main_tag]:
                random_secondary_tag = random.choice(['foryou', 'foryoupage', 'xyzbca'])

            # Thêm các tag phổ biến vào danh sách
            if random_main_tag not in selected_keywords:
                selected_keywords.append(random_main_tag)

            if random_secondary_tag not in selected_keywords and len(selected_keywords) < 6:
                selected_keywords.append(random_secondary_tag)

            # Loại bỏ các từ không có giá trị trong tìm kiếm
            low_value_keywords = ['cant', 'fine', 'this', 'well', 'believe', 'actually', 'worked', 'so', 'the', 'and', 'for', 'with', 'that', 'have', 'you', 'not', 'was', 'but', 'they', 'say', 'use', 'from', 'some', 'what', 'there', 'when', 'where', 'who', 'will', 'more', 'are', 'your', 'which', 'their', 'about', 'out', 'them', 'then', 'these', 'would', 'into', 'has', 'more', 'her', 'like', 'him', 'than', 'been', 'its', 'who', 'now', 'did']

            # Lọc bỏ các từ khóa không có giá trị
            filtered_keywords = [kw for kw in selected_keywords if kw.lower() not in low_value_keywords]

            # Lấy segment_id để tạo hashtag khác nhau cho mỗi video con
            segment_id = 0
            if hasattr(self, 'current_segment') and self.current_segment:
                segment_id = self.current_segment.get('part', 0)

            # Chọn các từ khóa có giá trị cao dựa trên segment_id
            # Mỗi video con sẽ có các từ khóa khác nhau

            # Tạo seed ngẫu nhiên dựa trên segment_id để đảm bảo mỗi video con có hashtag khác nhau
            # nhưng vẫn nhất quán khi chạy lại script
            random.seed(segment_id + int(time.time() / 43200))  # Thay đổi seed mỗi 12 giờ

            # Chọn ngẫu nhiên các từ khóa có giá trị cao dựa trên segment_id
            # Số lượng từ khóa cần thêm phụ thuộc vào số lượng từ khóa hiện có
            needed_count = max(2, 6 - len(filtered_keywords))  # Đảm bảo có ít nhất 2 từ khóa có giá trị cao

            # Chọn ngẫu nhiên các từ khóa có giá trị cao
            selected_valuable_tags = []

            # Ưu tiên chọn từ khóa từ YouTube tags nếu có
            if youtube_tags and len(youtube_tags) > 0:
                # Chọn ngẫu nhiên 2 tag từ YouTube nếu có đủ
                youtube_sample_count = min(2, len(youtube_tags))
                youtube_sample = random.sample(youtube_tags, youtube_sample_count)
                selected_valuable_tags.extend(youtube_sample)
                print(f"🎯 Sử dụng {youtube_sample_count} tag từ YouTube: {youtube_sample}")

            # Nếu vẫn cần thêm từ khóa, chọn từ danh sách valuable_tags
            if len(selected_valuable_tags) < needed_count:
                remaining_count = needed_count - len(selected_valuable_tags)
                # Chọn ngẫu nhiên các từ khóa có giá trị cao
                random_valuable_tags = random.sample(valuable_tags, min(remaining_count, len(valuable_tags)))
                selected_valuable_tags.extend(random_valuable_tags)

            # Thêm các từ khóa có giá trị cao vào danh sách
            for tag in selected_valuable_tags:
                if tag not in filtered_keywords:
                    filtered_keywords.append(tag)

            print(f"💎 Từ khóa có giá trị cao cho phân đoạn {segment_id}: {selected_valuable_tags}")

            # Thêm các tag phổ biến
            if random_main_tag not in filtered_keywords:
                filtered_keywords.append(random_main_tag)

            if random_secondary_tag not in filtered_keywords:
                filtered_keywords.append(random_secondary_tag)

            # Đảm bảo có đủ hashtag (tối thiểu 6, tối đa 8)
            min_hashtags = 6
            max_hashtags = 8

            # Nếu chưa đủ hashtag tối thiểu, thêm từ danh sách trending_tags
            if len(filtered_keywords) < min_hashtags:
                needed_count = min_hashtags - len(filtered_keywords)
                # Chọn thêm từ trending_tags
                for tag in random_trending_tags:
                    if tag not in filtered_keywords:
                        filtered_keywords.append(tag)
                        needed_count -= 1
                        if needed_count <= 0:
                            break

            # Giới hạn số lượng hashtag tối đa
            selected_keywords = filtered_keywords[:max_hashtags]

            # Thêm một chút ngẫu nhiên để tránh việc TikTok phát hiện video render hàng loạt
            # Đảo ngẫu nhiên thứ tự các hashtag dựa trên segment_id
            random.seed(segment_id + int(time.time() / 3600))  # Thay đổi seed mỗi giờ
            random.shuffle(selected_keywords)

            # Đảm bảo hashtag fyp luôn xuất hiện đầu tiên (nếu có)
            if 'fyp' in selected_keywords:
                selected_keywords.remove('fyp')
                selected_keywords.insert(0, 'fyp')

            # Tạo chuỗi từ khóa - sử dụng định dạng hashtag TikTok (không có khoảng trắng giữa các hashtag)
            keywords_str = ""
            if selected_keywords:
                keywords_str = " " + " ".join([f"#{kw}" for kw in selected_keywords])

            print(f"🔖 Hashtags cuối cùng cho phân đoạn {segment_id}: {selected_keywords}")

            # Thêm số phần - chỉ thêm Part X nếu có nhiều hơn 1 phần
            part_str = ""
            total_segments = len(self.segments) if hasattr(self, 'segments') else 0

            # Kiểm tra lại số lượng phân đoạn
            if total_segments > 1:
                part_str = f" - Part {segment['part']}"
                print(f"📽️ Thêm nhãn phần vì video có {total_segments} phần (đang xử lý phần {segment['part']})")
            else:
                print(f"🎬 Video chỉ có 1 phần: Giữ nguyên tên không thêm 'Part'")

            # Tạo tên file hoàn chỉnh - đảm bảo đường dẫn đầu ra đúng
            output_dir = self.output_dir
            # Loại bỏ phần "new folder" nếu có trong đường dẫn
            if "\\new folder" in output_dir.lower():
                output_dir = output_dir.replace("\\new folder", "")
                output_dir = output_dir.replace("\\New folder", "")
                output_dir = output_dir.replace("\\NEW FOLDER", "")

            # Tạo tên file hoàn chỉnh
            filename = f"{base_name}{part_str}{keywords_str}.mp4"

            # Đảm bảo tên file không quá dài (giới hạn 200 ký tự để tránh vượt quá giới hạn đường dẫn Windows)
            if len(filename) > 200:
                # Cắt bớt phần giữa nhưng giữ lại phần đầu và các từ khóa
                max_base_len = 150  # Độ dài tối đa cho phần tên cơ sở
                if len(base_name) > max_base_len:
                    # Tính toán độ dài cần cắt để giữ lại nhiều thông tin nhất có thể
                    # Giữ lại 70% đầu và 30% cuối của tên
                    first_part_len = int(max_base_len * 0.7)
                    last_part_len = max_base_len - first_part_len - 3  # trừ 3 cho dấu "..."
                    base_name = base_name[:first_part_len] + "..." + base_name[-last_part_len:] if last_part_len > 0 else base_name[:max_base_len-3] + "..."

                # Tạo lại tên file với tên cơ sở đã cắt ngắn
                filename = f"{base_name}{part_str}{keywords_str}.mp4"

                # Nếu vẫn còn quá dài, giảm số lượng từ khóa
                if len(filename) > 100:
                    # Giảm dần số lượng từ khóa cho đến khi đủ ngắn
                    for i in range(len(selected_keywords), 0, -1):
                        keywords_str = " " + " ".join([f"#{kw}" for kw in selected_keywords[:i]])
                        filename = f"{base_name}{part_str}{keywords_str}.mp4"
                        if len(filename) <= 100:
                            break

            # Đảm bảo tên file không có ký tự đặc biệt gây lỗi
            # Chỉ thay thế các ký tự không hợp lệ trong Windows, giữ nguyên các ký tự Unicode và ngôn ngữ khác
            # Không thay thế dấu : vì đã được xử lý ở trên
            # Thay thế các ký tự không hợp lệ bằng khoảng trắng thay vì dấu gạch dưới
            safe_filename = re.sub(r'[\\/*?"<>|]', ' ', filename)

            # Kiểm tra xem có ký tự Cyrillic trong tên file không
            has_cyrillic = any(0x0400 <= ord(c) <= 0x04FF for c in safe_filename)

            if has_cyrillic:
                # Nếu có ký tự Cyrillic, chuyển đổi thành ASCII để tránh lỗi
                # Thay thế các ký tự Cyrillic bằng chữ cái Latin tương ứng
                cyrillic_map = {
                    'а': 'a', 'б': 'b', 'в': 'v', 'г': 'g', 'д': 'd', 'е': 'e', 'ё': 'yo', 'ж': 'zh',
                    'з': 'z', 'и': 'i', 'й': 'y', 'к': 'k', 'л': 'l', 'м': 'm', 'н': 'n', 'о': 'o',
                    'п': 'p', 'р': 'r', 'с': 's', 'т': 't', 'у': 'u', 'ф': 'f', 'х': 'kh', 'ц': 'ts',
                    'ч': 'ch', 'ш': 'sh', 'щ': 'sch', 'ъ': '', 'ы': 'y', 'ь': '', 'э': 'e', 'ю': 'yu',
                    'я': 'ya',
                    'А': 'A', 'Б': 'B', 'В': 'V', 'Г': 'G', 'Д': 'D', 'Е': 'E', 'Ё': 'Yo', 'Ж': 'Zh',
                    'З': 'Z', 'И': 'I', 'Й': 'Y', 'К': 'K', 'Л': 'L', 'М': 'M', 'Н': 'N', 'О': 'O',
                    'П': 'P', 'Р': 'R', 'С': 'S', 'Т': 'T', 'У': 'U', 'Ф': 'F', 'Х': 'Kh', 'Ц': 'Ts',
                    'Ч': 'Ch', 'Ш': 'Sh', 'Щ': 'Sch', 'Ъ': '', 'Ы': 'Y', 'Ь': '', 'Э': 'E', 'Ю': 'Yu',
                    'Я': 'Ya'
                }

                transliterated_name = ''
                for char in safe_filename:
                    if 0x0400 <= ord(char) <= 0x04FF:  # Nếu là ký tự Cyrillic
                        transliterated_name += cyrillic_map.get(char, '_')
                    else:
                        transliterated_name += char

                safe_filename = transliterated_name
                print(f"🔄 Đã chuyển đổi tên file Cyrillic sang Latin: {safe_filename}")

            # Thay thế tất cả các ký tự không phải chữ cái, số, khoảng trắng, dấu gạch ngang bằng khoảng trắng
            # Điều này đảm bảo không có ký tự đặc biệt nào trong tên file
            safe_filename = re.sub(r'[^a-zA-Z0-9 \-]', '', safe_filename)

            # Loại bỏ khoảng trắng thừa
            safe_filename = re.sub(r'\s+', ' ', safe_filename).strip()

            # Đảm bảo tên file không trống
            if not safe_filename:
                safe_filename = f"video_{int(time.time())}"
                print(f"⚠️ Tên file trống sau khi xử lý, sử dụng tên mặc định: {safe_filename}")

            # Đảm bảo thư mục đầu ra tồn tại
            os.makedirs(output_dir, exist_ok=True)

            # Đường dẫn đầy đủ đến file đầu ra
            full_output_path = os.path.join(output_dir, safe_filename)

            logging.info(f"📝 Tên file gốc: {filename}")
            logging.info(f"📝 Tên file an toàn: {safe_filename}")
            logging.info(f"📝 Đường dẫn đầy đủ: {full_output_path}")

            return full_output_path

        except Exception as e:
            logging.error(f"⚠️ Không thể tạo tên file đầu ra: {str(e)}")
            # Tên file mặc định nếu có lỗi
            # Đảm bảo có đúng 6 hashtag (2 tag phổ biến và 4 tag có giá trị cao)
            # Lấy segment_id để tạo hashtag khác nhau cho mỗi video con
            segment_id = 0
            if hasattr(self, 'current_segment') and self.current_segment:
                segment_id = self.current_segment.get('part', 0)

            # Tạo seed ngẫu nhiên dựa trên segment_id
            random.seed(segment_id + int(time.time() / 86400))  # Thay đổi seed mỗi ngày

            # Chọn tag phổ biến dựa trên segment_id
            main_tag_options = ['fyp', 'viral', 'trending', 'tiktok', 'explore']
            secondary_tag_options = ['foryou', 'foryoupage', 'fypシ', 'explorepage', 'viralvideo', 'tiktoktrending']

            random_main_tag = main_tag_options[segment_id % len(main_tag_options)]
            random_secondary_tag = secondary_tag_options[(segment_id + 3) % len(secondary_tag_options)]

            # Sử dụng các từ khóa có giá trị cao - mở rộng danh sách
            high_value_tags = [
                # Từ khóa chung
                'satisfying', 'tutorial', 'howto', 'lifehack', 'amazing', 'awesome', 'incredible',
                'interesting', 'learn', 'educational', 'creative', 'diy', 'technology', 'science',
                'cooking', 'fitness', 'beauty', 'travel', 'music', 'comedy', 'gaming', 'nature',
                # Từ khóa theo chủ đề
                'oddlysatisfying', 'asmr', 'relaxing', 'tips', 'trick', 'mindblowing',
                'art', 'handmade', 'homemade', 'craft', 'gadget', 'innovation', 'experiment',
                'engineering', 'coding', 'programming', 'recipe', 'food', 'baking', 'chef',
                'kitchen', 'delicious', 'yummy', 'tasty', 'homecooking', 'workout', 'exercise',
                'gym', 'health', 'wellness', 'motivation', 'inspiration', 'lifestyle', 'makeup',
                'skincare', 'fashion', 'style', 'outfit', 'adventure', 'explore', 'wanderlust',
                'vacation', 'trip', 'journey', 'song', 'dance', 'singer', 'musician', 'artist',
                'band', 'concert', 'performance', 'talent', 'funny', 'humor', 'joke', 'laugh',
                'entertainment', 'fun', 'hilarious', 'meme', 'prank', 'videogame', 'gamer',
                'gameplay', 'streamer', 'esports', 'animals', 'wildlife', 'pets', 'dog', 'cat',
                'bird', 'fish', 'plants', 'flowers', 'garden'
            ]

            # Tạo seed ngẫu nhiên dựa trên segment_id
            random.seed(segment_id + int(time.time() / 21600))  # Thay đổi seed mỗi 6 giờ

            # Chọn ngẫu nhiên 4-6 tag có giá trị cao dựa trên segment_id
            tag_count = random.randint(4, 6)
            selected_high_value_tags = random.sample(high_value_tags, min(tag_count, len(high_value_tags)))

            # Chọn 2-3 tag trending khác nhau cho mỗi phân đoạn
            trending_options = ['fyp', 'viral', 'trending', 'foryou', 'foryoupage', 'xyzbca',
                               'tiktok', 'explore', 'explorepage', 'fypシ', 'viralvideo',
                               'tiktoktrending', 'fyppage', 'tiktokviral', 'tiktokalgorithm']

            trending_count = random.randint(2, 3)
            selected_trending_tags = random.sample(trending_options, min(trending_count, len(trending_options)))

            # Đảo ngẫu nhiên thứ tự các hashtag
            all_tags = selected_high_value_tags + selected_trending_tags
            random.shuffle(all_tags)

            # Đảm bảo hashtag fyp luôn xuất hiện đầu tiên (nếu có)
            if 'fyp' in all_tags:
                all_tags.remove('fyp')
                all_tags.insert(0, 'fyp')
            elif 'fyp' in trending_options:
                all_tags.insert(0, 'fyp')

            # Giới hạn số lượng hashtag tối đa
            all_tags = all_tags[:8]  # Tối đa 8 hashtag

            hashtags = ' '.join([f'#{tag}' for tag in all_tags])
            default_name = os.path.join(self.output_dir, f"video_{int(time.time())} - Part {segment['part']} {hashtags}.mp4")

            print(f"🔄 Tạo tên file mặc định với hashtags: {all_tags}")
            return default_name

    def process(self) -> List[str]:
        """Xử lý video và trả về danh sách các file đầu ra"""
        output_files = []
        error_count = 0
        max_retries = 3

        # Tạo file trạng thái để lưu tiến trình xử lý
        state_file = os.path.join(self.temp_dir, "processing_state.json")

        # Kiểm tra xem có trạng thái đã lưu trước đó không
        saved_state = {}
        if os.path.exists(state_file):
            try:
                with open(state_file, 'r', encoding='utf-8') as f:
                    saved_state = json.load(f)
                print(f"💾 Đã tìm thấy trạng thái xử lý trước đó, tiếp tục từ phân đoạn {saved_state.get('current_segment', 1)}")
            except Exception as e:
                logging.error(f"❌ Lỗi khi đọc file trạng thái: {str(e)}")

        try:
            # Tạo các phân đoạn video
            segments = self._generate_segments()

            # Lưu trữ segments vào thuộc tính của đối tượng để sử dụng trong các hàm khác
            self.segments = segments

            # In thông tin về số lượng phân đoạn
            total_segments = len(segments)
            total_duration = sum(segment['duration'] for segment in segments)
            print(f"📌 Video có {total_segments} phân đoạn, tổng thời lượng: {total_duration:.1f}s ({total_duration/60:.1f} phút)")

            # Hiển thị thông tin chi tiết về các phân đoạn
            print("\n📊 Thông tin chi tiết về các phân đoạn:")
            print("┌" + "─" * 50 + "┐")
            print("│ " + "Phần".ljust(6) + " | " + "Thời điểm bắt đầu".ljust(15) + " | " + "Độ dài".ljust(10) + " | " + "Trạng thái".ljust(10) + " │")
            print("├" + "─" * 50 + "┤")

            # Xác định phân đoạn bắt đầu dựa trên trạng thái đã lưu
            start_segment = saved_state.get('current_segment', 1)
            completed_files = saved_state.get('completed_files', [])

            # Hiển thị thông tin từng phân đoạn
            for segment in segments:
                status = "✅ Hoàn thành" if segment['part'] < start_segment else "⏳ Chờ xử lý"
                print(f"│ {segment['part']:2d}    | {segment['start']:.1f}s".ljust(24) + f" | {segment['duration']:.1f}s".ljust(13) + f" | {status}".ljust(13) + " │")
            print("└" + "─" * 50 + "┘\n")

            # Hiển thị thông tin về tiến trình
            if start_segment > 1:
                completed_count = start_segment - 1
                completed_percent = (completed_count / total_segments) * 100
                completed_duration = sum(segment['duration'] for segment in segments if segment['part'] < start_segment)
                print(f"🔁 Tiếp tục xử lý từ phân đoạn {start_segment} ({completed_percent:.1f}% đã hoàn thành)")
                print(f"⏳ Đã xử lý {completed_duration:.1f}s/{total_duration:.1f}s ({completed_duration/60:.1f}/{total_duration/60:.1f} phút)")

                # Thêm các file đã xử lý trước đó vào danh sách đầu ra
                for file in completed_files:
                    if os.path.exists(file):
                        output_files.append(file)
                        print(f"✅ Đã tìm thấy file đã xử lý trước đó: {os.path.basename(file)}")

            # Lọc các phân đoạn chưa được xử lý
            segments_to_process = [segment for segment in segments if segment['part'] >= start_segment]

            # Kiểm tra xem có thể sử dụng xử lý song song không
            if PARALLEL_PROCESSING_AVAILABLE and len(segments_to_process) > 1:
                # Sử dụng số lượng worker đã được xác định ở đầu file
                max_workers = MAX_WORKERS

                # Kiểm tra tài nguyên hệ thống hiện tại
                try:
                    # Lấy thông tin CPU và RAM
                    cpu_percent = psutil.cpu_percent(interval=1)
                    memory = psutil.virtual_memory()
                    memory_percent = memory.percent
                    total_ram_gb = memory.total / (1024 * 1024 * 1024)

                    print(f"💻 Tài nguyên hệ thống: CPU {cpu_percent}%, RAM {memory_percent}% ({total_ram_gb:.1f}GB)")

                    # Điều chỉnh số lượng worker dựa trên tài nguyên hiện tại
                    if cpu_percent > 80 or memory_percent > 80:
                        adjusted_workers = max(1, max_workers - 1)
                        print(f"⚠️ Tài nguyên hệ thống cao, giảm số lượng worker xuống {adjusted_workers}")
                        max_workers = adjusted_workers

                    # Điều chỉnh số tiến trình dựa trên RAM
                    if total_ram_gb < 8:  # Ít hơn 8GB RAM
                        max_workers = min(max_workers, 1)
                        print(f"⚠️ RAM thấp (<8GB), giới hạn xuống {max_workers} worker")
                    elif total_ram_gb < 16:  # 8-16GB RAM
                        max_workers = min(max_workers, 2)
                        print(f"ℹ️ RAM trung bình (8-16GB), giới hạn xuống {max_workers} worker")
                except Exception as e:
                    logging.debug(f"Không thể kiểm tra tài nguyên hệ thống: {str(e)}")

                # Nếu có GPU, điều chỉnh số lượng tiến trình để tối ưu hóa sử dụng GPU
                try:
                    gpu_info = subprocess.run(
                        ["nvidia-smi", "--query-gpu=name,memory.total,memory.free", "--format=csv,noheader"],
                        capture_output=True,
                        text=True
                    )
                    if gpu_info.returncode == 0:
                        gpu_output = gpu_info.stdout.strip()
                        print(f"🎮 GPU: {gpu_output}")

                        # Phân tích thông tin GPU
                        if '3070' in gpu_output.lower():
                            # RTX 3070 Ti có 8GB VRAM, tối ưu hóa số tiến trình
                            max_workers = min(max_workers, 2)  # Giảm xuống 2 tiến trình theo yêu cầu
                            print(f"🎮 Phát hiện GPU NVIDIA RTX 3070 Ti, tối ưu hóa số tiến trình song song: {max_workers}")
                        elif 'rtx' in gpu_output.lower():
                            # GPU RTX khác, điều chỉnh dựa trên dòng RTX
                            if '3090' in gpu_output.lower() or '4090' in gpu_output.lower():
                                max_workers = min(max_workers, 4)  # GPU cao cấp với nhiều VRAM
                            else:
                                max_workers = min(max_workers, 2)  # GPU trung cấp
                            print(f"🎮 Phát hiện GPU NVIDIA RTX, tối ưu hóa số tiến trình song song: {max_workers}")
                except Exception as e:
                    logging.debug(f"Không thể kiểm tra thông tin GPU: {str(e)}")

                # Kiểm tra không gian đĩa còn lại
                try:
                    output_dir = self.output_dir
                    disk_usage = psutil.disk_usage(output_dir)
                    free_space_gb = disk_usage.free / (1024 * 1024 * 1024)

                    print(f"💾 Không gian đĩa còn lại: {free_space_gb:.1f}GB ({disk_usage.percent}% đã sử dụng)")

                    # Nếu không gian đĩa ít, giảm số lượng worker để tránh lỗi
                    if free_space_gb < 5:  # Ít hơn 5GB
                        max_workers = 1
                        print(f"⚠️ Không gian đĩa thấp, giảm xuống {max_workers} worker để tránh lỗi")
                    elif free_space_gb < 10:  # Ít hơn 10GB
                        max_workers = min(max_workers, 2)
                        print(f"ℹ️ Không gian đĩa hạn chế, giới hạn xuống {max_workers} worker")
                except Exception as e:
                    logging.debug(f"Không thể kiểm tra không gian đĩa: {str(e)}")

                print(f"🚀 Sử dụng xử lý song song với {max_workers} worker")

                # Chuẩn bị các phân đoạn với timeline và tên file đầu ra
                for segment in segments_to_process:
                    # Lưu thông tin segment hiện tại để sử dụng cho hashtag
                    self.current_segment = segment
                    # Tạo tên file đầu ra (bao gồm đường dẫn đầy đủ)
                    segment['output_path'] = self._generate_output_filename(segment)
                    # Tạo timeline lật ngang riêng cho từng phân đoạn
                    segment['flip_timeline'] = self._generate_flip_timeline(segment_duration=segment['duration'])
                    print(f"📍 Phân đoạn {segment['part']}: Độ dài {segment['duration']:.1f}s, {len(segment['flip_timeline'])} lần lật ngang, Hashtag ID: {segment['part']}")

                # Xử lý song song các phân đoạn
                processed_paths = process_segments_parallel(
                    segments_to_process,
                    self._process_segment_wrapper,
                    max_workers=max_workers
                )

                # Thêm các file đã xử lý vào danh sách đầu ra
                for path in processed_paths:
                    if path and os.path.exists(path):
                        output_files.append(path)
                        # Cập nhật trạng thái đã hoàn thành
                        saved_state['completed_files'] = output_files
                        with open(state_file, 'w', encoding='utf-8') as f:
                            json.dump(saved_state, f, ensure_ascii=False, indent=2)
            else:
                # Xử lý tuần tự nếu không thể sử dụng xử lý song song
                print("Sử dụng xử lý tuần tự cho các phân đoạn video")

                # Xử lý từng phân đoạn
                for segment in segments_to_process:
                    # Cập nhật trạng thái hiện tại
                    saved_state['current_segment'] = segment['part']
                    saved_state['completed_files'] = output_files
                    with open(state_file, 'w', encoding='utf-8') as f:
                        json.dump(saved_state, f, ensure_ascii=False, indent=2)

                    # Lưu thông tin segment hiện tại để sử dụng cho hashtag
                    self.current_segment = segment

                    # Tạo tên file đầu ra (bao gồm đường dẫn đầy đủ)
                    output_path = self._generate_output_filename(segment)
                    print(f"Hashtag ID cho phân đoạn {segment['part']}: {segment['part']}")

                    # Tạo timeline lật ngang riêng cho từng phân đoạn
                    segment_flip_timeline = self._generate_flip_timeline(segment_duration=segment['duration'])
                    print(f"📍 Phân đoạn {segment['part']}: Độ dài {segment['duration']:.1f}s, {len(segment_flip_timeline)} lần lật ngang")

                    # Xử lý phân đoạn với timeline riêng và thử lại nếu gặp lỗi
                    success = False
                    for attempt in range(max_retries):
                        try:
                            # Đặt retry_count để đánh dấu đây là lần thử lại
                            if attempt > 0:
                                self.retry_count = attempt

                            if self._process_segment(segment, segment_flip_timeline, output_path):
                                output_files.append(output_path)
                                success = True
                                # Cập nhật trạng thái đã hoàn thành
                                saved_state['completed_files'] = output_files
                                with open(state_file, 'w', encoding='utf-8') as f:
                                    json.dump(saved_state, f, ensure_ascii=False, indent=2)
                                break
                            else:
                                if attempt < max_retries - 1:  # Chỉ hiển thị thông báo nếu còn lần thử lại
                                    print(f"Thu lai lan {attempt+1}/{max_retries} cho phan doan {segment['part']}")
                        except Exception as e:
                            error_count += 1
                            logging.error(f"Loi khi xu ly phan doan {segment['part']} (lan {attempt+1}): {str(e)}")
                            print(f"Loi khi xu ly phan doan {segment['part']} (lan {attempt+1}): {str(e)}")
                            # Chờ 2 giây trước khi thử lại
                            time.sleep(2)

                    # Xóa thuộc tính retry_count sau khi hoàn thành
                    if hasattr(self, 'retry_count'):
                        delattr(self, 'retry_count')

                    if not success:
                        logging.error(f"Khong the xu ly phan doan {segment['part']} sau {max_retries} lan thu")
                        print(f"Khong the xu ly phan doan {segment['part']} sau {max_retries} lan thu, bo qua va tiep tuc")

            logging.info(f"Da xu ly thanh cong {len(output_files)}/{len(segments)} phan doan")

            # Xóa file trạng thái vì đã hoàn thành
            state_file = os.path.join(self.temp_dir, "processing_state.json")
            if os.path.exists(state_file):
                try:
                    os.remove(state_file)
                    logging.info(f"Da xoa file trang thai vi da hoan thanh xu ly")
                except Exception as e:
                    logging.error(f"Khong the xoa file trang thai: {str(e)}")

            # Xóa các file trạng thái trong thư mục đầu ra
            self._cleanup_state_files_in_output_dir()

            # Dọn dẹp tài nguyên
            self.cleanup()

            return output_files

        except Exception as e:
            logging.error(f"Loi khi xu ly video: {str(e)}")
            import traceback
            logging.error(f"Chi tiet loi: {traceback.format_exc()}")
            return output_files

def clean_text_for_display(text):
    """Làm sạch văn bản để hiển thị trong video

    Args:
        text: Văn bản cần làm sạch

    Returns:
        Văn bản đã được làm sạch
    """
    # Loại bỏ các ký tự đặc biệt có thể gây lỗi trong filter
    text = re.sub(r'[\'\\\\]', '', text)
    # Thay thế các ký tự đặc biệt khác bằng khoảng trắng
    text = re.sub(r'[^\w\s]', ' ', text)
    # Loại bỏ khoảng trắng thừa
    text = re.sub(r'\s+', ' ', text).strip()
    return text
