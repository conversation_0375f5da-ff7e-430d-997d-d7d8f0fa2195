"""
<PERSON><PERSON><PERSON> hàm xử lý liên quan đến GPU
"""
import subprocess
import logging
from typing import Dict, List, Optional

def check_gpu_capabilities() -> Dict[str, any]:
    """
    Kiểm tra khả năng GPU và trả về cấu hình phù hợp
    """
    from ..config.settings import DEFAULT_GPU_SETTINGS

    # Tối ưu hóa xử lý video: Kiểm tra GPU chi tiết hơn
    gpu_info = get_gpu_info()

    if gpu_info['available']:
        gpu_name = gpu_info['name'].lower()
        gpu_memory = gpu_info['memory']

        # Tạo cấu hình dựa trên loại GPU
        gpu_settings = DEFAULT_GPU_SETTINGS.copy()

        # NVIDIA GPU
        if 'nvidia' in gpu_name:
            gpu_settings['type'] = 'nvidia'
            gpu_settings['encoder'] = 'h264_nvenc'

            # Tối ưu hóa dựa trê<PERSON> thế hệ GPU
            if any(x in gpu_name for x in ['rtx', '30', '40', 'a100', 'a6000']):
                # RTX 30/40 series - Ampere/Ada Lovelace
                gpu_settings['preset'] = 'p1'  # Chất lượng cao nhất
                gpu_settings['rc'] = 'vbr_hq'
                gpu_settings['cq'] = 18
                gpu_settings['enabled'] = True
                gpu_settings['options'] = [
                    '-spatial-aq', '1',
                    '-temporal-aq', '1',
                    '-refs', '3',
                    '-b_ref_mode', 'middle',
                    '-rc-lookahead', '32'
                ]
                print(f"✅ Phát hiện NVIDIA RTX GPU cao cấp: {gpu_name}")
            elif any(x in gpu_name for x in ['rtx', '20', 'quadro']):
                # RTX 20 series - Turing
                gpu_settings['preset'] = 'p2'
                gpu_settings['rc'] = 'vbr_hq'
                gpu_settings['cq'] = 20
                gpu_settings['options'] = [
                    '-spatial-aq', '1',
                    '-rc-lookahead', '24'
                ]
                print(f"✅ Phát hiện NVIDIA RTX GPU: {gpu_name}")
            elif any(x in gpu_name for x in ['gtx', '16', '10']):
                # GTX 10/16 series - Pascal/Turing
                gpu_settings['preset'] = 'p4'
                gpu_settings['rc'] = 'vbr'
                gpu_settings['cq'] = 23
                print(f"✅ Phát hiện NVIDIA GTX GPU: {gpu_name}")
            else:
                # Older NVIDIA GPUs
                gpu_settings['preset'] = 'p6'
                gpu_settings['rc'] = 'vbr'
                gpu_settings['cq'] = 26
                print(f"✅ Phát hiện NVIDIA GPU cũ: {gpu_name}")

            # Điều chỉnh dựa trên bộ nhớ GPU
            if gpu_memory > 8000:  # > 8GB
                gpu_settings['hwaccel'] = ['-hwaccel', 'cuda', '-hwaccel_output_format', 'cuda']
                # Thêm các tham số tối ưu cho RTX 3070 Ti
                if '3070' in gpu_name:
                    gpu_settings['options'].extend([
                        '-gpu', '0',
                        '-surfaces', '16',  # Tăng số lượng surface cho xử lý song song
                        '-multipass', '1',   # Sử dụng chế độ multipass để tăng chất lượng
                        '-weighted_pred', '1',  # Dự đoán có trọng số
                        '-bluray-compat', '0',   # Tắt tương thích Bluray để tối ưu hóa
                        '-tune', 'hq',  # Tối ưu cho chất lượng cao
                        '-rc-lookahead', '32',  # Tăng lookahead để cải thiện chất lượng
                        '-spatial_aq', '1',  # Phân phối bitrate tốt hơn trong frame
                        '-temporal_aq', '1',  # Phân phối bitrate tốt hơn giữa các frame
                        '-coder', '1',  # CABAC coder cho nén tốt hơn
                        '-b_ref_mode', 'middle',  # Tối ưu B-frame references
                        '-qmin', '1',  # Cho phép QP thấp nhất để đạt chất lượng tốt nhất
                        '-qmax', '51',  # Giới hạn QP cao nhất
                        '-bf', '3'  # Số lượng B-frames tối đa
                    ])
                    print(f"✅ Đã tối ưu hóa đặc biệt cho RTX 3070 Ti với cấu hình cao cấp")
                print(f"✅ Sử dụng CUDA hardware acceleration với {gpu_memory}MB VRAM")

            print(f"✅ Sử dụng NVIDIA GPU encoder (h264_nvenc) với preset {gpu_settings['preset']}")
            return gpu_settings

        # AMD GPU
        elif any(x in gpu_name for x in ['amd', 'radeon', 'rx']):
            gpu_settings['type'] = 'amd'
            gpu_settings['encoder'] = 'h264_amf'
            gpu_settings['preset'] = 'quality'
            gpu_settings['rc'] = 'vbr_peak'
            print(f"✅ Phát hiện AMD GPU: {gpu_name}")
            print(f"✅ Sử dụng AMD GPU encoder (h264_amf)")
            return gpu_settings

        # Intel GPU
        elif any(x in gpu_name for x in ['intel', 'hd graphics', 'uhd graphics', 'iris']):
            gpu_settings['type'] = 'intel'
            gpu_settings['encoder'] = 'h264_qsv'
            gpu_settings['preset'] = 'medium'
            gpu_settings['rc'] = 'vbr'
            print(f"✅ Phát hiện Intel GPU: {gpu_name}")
            print(f"✅ Sử dụng Intel GPU encoder (h264_qsv)")
            return gpu_settings

    # Fallback to CPU if no GPU found or not recognized
    print("⚠️ Không phát hiện GPU hỗ trợ, sử dụng CPU")
    return {
        "type": "cpu",
        "encoder": "libx264",
        "preset": "medium",
        "rc": "crf",
        "cq": 23,
        "hwaccel": []
    }

def get_gpu_info() -> Dict[str, any]:
    """
    Lấy thông tin chi tiết về GPU
    """
    gpu_info = {
        'available': False,
        'name': 'Unknown',
        'memory': 0,
        'driver': 'Unknown',
        'type': 'Unknown'
    }

    # Kiểm tra NVIDIA GPU
    try:
        # Lấy tên GPU
        name_result = subprocess.run(
            ["nvidia-smi", "--query-gpu=name", "--format=csv,noheader"],
            capture_output=True,
            text=True
        )

        # Lấy bộ nhớ GPU
        memory_result = subprocess.run(
            ["nvidia-smi", "--query-gpu=memory.total", "--format=csv,noheader"],
            capture_output=True,
            text=True
        )

        # Lấy phiên bản driver
        driver_result = subprocess.run(
            ["nvidia-smi", "--query-gpu=driver_version", "--format=csv,noheader"],
            capture_output=True,
            text=True
        )

        if name_result.returncode == 0:
            gpu_info['available'] = True
            gpu_info['name'] = name_result.stdout.strip()
            gpu_info['type'] = 'nvidia'

            # Xử lý bộ nhớ
            if memory_result.returncode == 0:
                memory_str = memory_result.stdout.strip()
                # Chuyển đổi "8192 MiB" thành 8192
                if 'MiB' in memory_str:
                    gpu_info['memory'] = int(memory_str.split()[0])

            # Xử lý phiên bản driver
            if driver_result.returncode == 0:
                gpu_info['driver'] = driver_result.stdout.strip()
    except FileNotFoundError:
        # Không có NVIDIA GPU hoặc không có nvidia-smi
        pass

    # Nếu không có NVIDIA GPU, kiểm tra AMD GPU (nếu cần)
    if not gpu_info['available']:
        try:
            # Kiểm tra AMD GPU bằng rocm-smi hoặc các công cụ khác
            # (Có thể bổ sung sau)
            pass
        except:
            pass

    return gpu_info

def get_gpu_name() -> Optional[str]:
    """
    Lấy tên GPU nếu có
    """
    gpu_info = get_gpu_info()
    if gpu_info['available']:
        return gpu_info['name']
    return None

def get_gpu_memory_info() -> Dict[str, int]:
    """
    Lấy thông tin bộ nhớ GPU
    """
    try:
        result = subprocess.run(
            ["nvidia-smi", "--query-gpu=memory.total,memory.used", "--format=csv,nounits,noheader"],
            capture_output=True,
            text=True
        )
        if result.returncode == 0:
            total, used = map(int, result.stdout.strip().split(','))
            return {
                "total": total,
                "used": used,
                "free": total - used
            }
    except:
        pass
    return {"total": 0, "used": 0, "free": 0}

def optimize_gpu_settings(video_info: Dict[str, any]) -> Dict[str, any]:
    """
    Tối ưu cài đặt GPU dựa trên thông tin video
    """
    from ..config.settings import DEFAULT_GPU_SETTINGS
    import psutil

    settings = DEFAULT_GPU_SETTINGS.copy()
    settings['enabled'] = True  # Đảm bảo GPU được bật

    # Kiểm tra xem có phải RTX 3070 Ti không
    gpu_name = get_gpu_name()
    is_rtx_3070ti = gpu_name and '3070' in gpu_name.lower()
    is_rtx = gpu_name and 'rtx' in gpu_name.lower()

    # Kiểm tra tài nguyên hệ thống
    system_memory = psutil.virtual_memory().total / (1024 * 1024)  # MB
    cpu_cores = psutil.cpu_count(logical=False)
    cpu_threads = psutil.cpu_count(logical=True)

    # Lấy thông tin bộ nhớ GPU
    gpu_memory_info = get_gpu_memory_info()
    gpu_free_memory = gpu_memory_info.get("free", 0)

    # Tối ưu hóa đặc biệt cho RTX 3070 Ti
    if is_rtx_3070ti:
        settings['hwaccel'] = ['-hwaccel', 'cuda', '-hwaccel_output_format', 'cuda']
        settings['options'] = [
            '-gpu', '0',
            '-surfaces', '16',
            '-multipass', '1',
            '-weighted_pred', '1',
            '-spatial-aq', '1',
            '-temporal-aq', '1',
            '-refs', '3',
            '-b_ref_mode', 'middle',
            '-rc-lookahead', '32',
            '-tune', 'hq',
            '-coder', '1',
            '-qmin', '1',
            '-qmax', '51',
            '-bf', '3'
        ]
        print(f"✅ Đã tối ưu hóa đặc biệt cho RTX 3070 Ti")
    elif is_rtx:
        # Tối ưu cho các GPU RTX khác
        settings['hwaccel'] = ['-hwaccel', 'cuda', '-hwaccel_output_format', 'cuda']
        settings['options'] = [
            '-gpu', '0',
            '-surfaces', '8',
            '-multipass', '1',
            '-weighted_pred', '1',
            '-spatial-aq', '1',
            '-temporal-aq', '1',
            '-refs', '2',
            '-tune', 'hq'
        ]
        print(f"✅ Đã tối ưu hóa cho NVIDIA RTX GPU: {gpu_name}")

    # Điều chỉnh preset dựa trên độ phân giải và tài nguyên
    video_height = video_info.get("height", 0)
    video_width = video_info.get("width", 0)
    video_duration = video_info.get("duration", 0)
    is_high_res = video_height > 1080 or video_width > 1920
    is_long_video = video_duration > 600  # >10 phút

    # Tối ưu preset dựa trên độ phân giải và GPU
    if is_high_res:
        if gpu_free_memory > 4000:  # >4GB free VRAM
            settings["preset"] = "p2" if is_rtx_3070ti else "p3"  # Chất lượng cao hơn cho video 4K
        else:
            settings["preset"] = "p3" if is_rtx_3070ti else "p4"  # Giảm chất lượng nếu VRAM thấp
    elif video_height <= 720:
        settings["preset"] = "p1"  # Tốc độ nhanh hơn cho video độ phân giải thấp
    else:
        # 1080p - sử dụng preset tốt nhất cho RTX 3070 Ti
        settings["preset"] = "p1" if is_rtx_3070ti else "p2"

    # Điều chỉnh cq (chất lượng) dựa trên bitrate gốc và tài nguyên
    original_bitrate = video_info.get("bitrate", 0)
    target_bitrate = min(10000, max(8000, original_bitrate))  # Giới hạn trong khoảng 8000-10000 kbps

    # Điều chỉnh chất lượng dựa trên bitrate gốc
    if original_bitrate > 10000:  # >10Mbps
        settings["cq"] = 18 if is_rtx_3070ti else 19  # Chất lượng cao hơn
    elif original_bitrate < 2000:  # <2Mbps
        settings["cq"] = 21 if is_rtx_3070ti else 23  # Giảm chất lượng
    else:
        settings["cq"] = 19 if is_rtx_3070ti else 21

    # Điều chỉnh bitrate dựa trên độ phân giải
    if is_high_res:
        settings["bitrate"] = target_bitrate
        settings["maxrate"] = target_bitrate
        settings["bufsize"] = target_bitrate * 2
    else:
        settings["bitrate"] = target_bitrate
        settings["maxrate"] = target_bitrate
        settings["bufsize"] = target_bitrate * 2

    # Sử dụng VBR HQ cho RTX
    if is_rtx:
        settings["rc"] = "vbr_hq"

    # Thêm thông tin về tài nguyên hệ thống
    print(f"ℹ️ Tài nguyên hệ thống: {cpu_cores} cores, {cpu_threads} threads, {system_memory:.0f}MB RAM")
    print(f"ℹ️ GPU memory: {gpu_free_memory}MB free")
    print(f"ℹ️ Video: {video_width}x{video_height}, {original_bitrate}kbps, {video_duration:.1f}s")
    print(f"ℹ️ Cấu hình encoder: {settings['encoder']}, preset={settings['preset']}, rc={settings['rc']}, cq={settings['cq']}")

    return settings