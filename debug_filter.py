#!/usr/bin/env python3
"""
Debug script to test filter_complex issues
"""
import subprocess
import os
import json

def test_filter_complex():
    """Test different filter_complex configurations"""

    # Use the test video we just created
    test_video = "test_input.mp4"

    if not os.path.exists(test_video):
        print("❌ Test video not found")
        return

    print(f"🎬 Using test video: {test_video}")

    # Check input streams
    print("\n🔍 Checking input streams:")
    probe_cmd = ['ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_streams', test_video]
    try:
        result = subprocess.run(probe_cmd, capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            streams_info = json.loads(result.stdout)
            video_streams = [s for s in streams_info.get('streams', []) if s.get('codec_type') == 'video']
            audio_streams = [s for s in streams_info.get('streams', []) if s.get('codec_type') == 'audio']
            print(f"📹 Input has {len(video_streams)} video stream(s) and {len(audio_streams)} audio stream(s)")
            if video_streams:
                v = video_streams[0]
                print(f"   Video: {v.get('width')}x{v.get('height')}, {v.get('codec_name')}")
            if audio_streams:
                a = audio_streams[0]
                print(f"   Audio: {a.get('codec_name')}, {a.get('channels')} channels")
        else:
            print(f"❌ Failed to probe input: {result.stderr}")
            return
    except Exception as e:
        print(f"❌ Error probing input: {e}")
        return

    # Test different filter configurations
    tests = [
        {
            "name": "Simple copy",
            "filter": None,
            "cmd": ['ffmpeg', '-y', '-i', test_video, '-c', 'copy', 'test_copy.mp4']
        },
        {
            "name": "Simple scale",
            "filter": None,
            "cmd": ['ffmpeg', '-y', '-i', test_video, '-vf', 'scale=1920:1080', '-c:a', 'copy', 'test_scale.mp4']
        },
        {
            "name": "Filter complex video only",
            "filter": "[0:v]scale=1920:1080[v]",
            "cmd": ['ffmpeg', '-y', '-i', test_video, '-filter_complex', '[0:v]scale=1920:1080[v]', '-map', '[v]', '-map', '0:a?', '-c:a', 'aac', 'test_filter_v.mp4']
        },
        {
            "name": "Filter complex with fps",
            "filter": "[0:v]fps=30,scale=1920:1080[v]",
            "cmd": ['ffmpeg', '-y', '-i', test_video, '-filter_complex', '[0:v]fps=30,scale=1920:1080[v]', '-map', '[v]', '-map', '0:a?', '-c:a', 'aac', 'test_filter_fps.mp4']
        }
    ]

    for test in tests:
        print(f"\n🧪 Testing: {test['name']}")
        print(f"Command: {' '.join(test['cmd'])}")

        try:
            result = subprocess.run(test['cmd'], capture_output=True, text=True, timeout=60)
            if result.returncode == 0:
                output_file = test['cmd'][-1]
                print(f"✅ Success: {output_file}")

                # Check output streams
                probe_result = subprocess.run(['ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_streams', output_file],
                                            capture_output=True, text=True, timeout=10)
                if probe_result.returncode == 0:
                    streams_info = json.loads(probe_result.stdout)
                    video_streams = [s for s in streams_info.get('streams', []) if s.get('codec_type') == 'video']
                    audio_streams = [s for s in streams_info.get('streams', []) if s.get('codec_type') == 'audio']
                    print(f"   Output: {len(video_streams)} video, {len(audio_streams)} audio streams")
                    if video_streams:
                        v = video_streams[0]
                        print(f"   Video: {v.get('width')}x{v.get('height')}, {v.get('codec_name')}")
                    if not video_streams:
                        print("   ❌ NO VIDEO STREAM IN OUTPUT!")
                else:
                    print(f"   ⚠️ Could not probe output")
            else:
                print(f"❌ Failed: {result.stderr}")
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_filter_complex()
