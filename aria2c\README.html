<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="generator" content="Docutils 0.16: http://docutils.sourceforge.net/" />
<title>aria2 - The ultra fast download utility</title>
<style type="text/css">

/*
:Author: <PERSON> (<EMAIL>)
:Id: $Id: html4css1.css 7952 2016-07-26 18:15:59Z milde $
:Copyright: This stylesheet has been placed in the public domain.

Default cascading style sheet for the HTML output of Docutils.

See http://docutils.sf.net/docs/howto/html-stylesheets.html for how to
customize this style sheet.
*/

/* used to remove borders from tables and images */
.borderless, table.borderless td, table.borderless th {
  border: 0 }

table.borderless td, table.borderless th {
  /* Override padding for "table.docutils td" with "! important".
     The right padding separates the table cells. */
  padding: 0 0.5em 0 0 ! important }

.first {
  /* Override more specific margin styles with "! important". */
  margin-top: 0 ! important }

.last, .with-subtitle {
  margin-bottom: 0 ! important }

.hidden {
  display: none }

.subscript {
  vertical-align: sub;
  font-size: smaller }

.superscript {
  vertical-align: super;
  font-size: smaller }

a.toc-backref {
  text-decoration: none ;
  color: black }

blockquote.epigraph {
  margin: 2em 5em ; }

dl.docutils dd {
  margin-bottom: 0.5em }

object[type="image/svg+xml"], object[type="application/x-shockwave-flash"] {
  overflow: hidden;
}

/* Uncomment (and remove this text!) to get bold-faced definition list terms
dl.docutils dt {
  font-weight: bold }
*/

div.abstract {
  margin: 2em 5em }

div.abstract p.topic-title {
  font-weight: bold ;
  text-align: center }

div.admonition, div.attention, div.caution, div.danger, div.error,
div.hint, div.important, div.note, div.tip, div.warning {
  margin: 2em ;
  border: medium outset ;
  padding: 1em }

div.admonition p.admonition-title, div.hint p.admonition-title,
div.important p.admonition-title, div.note p.admonition-title,
div.tip p.admonition-title {
  font-weight: bold ;
  font-family: sans-serif }

div.attention p.admonition-title, div.caution p.admonition-title,
div.danger p.admonition-title, div.error p.admonition-title,
div.warning p.admonition-title, .code .error {
  color: red ;
  font-weight: bold ;
  font-family: sans-serif }

/* Uncomment (and remove this text!) to get reduced vertical space in
   compound paragraphs.
div.compound .compound-first, div.compound .compound-middle {
  margin-bottom: 0.5em }

div.compound .compound-last, div.compound .compound-middle {
  margin-top: 0.5em }
*/

div.dedication {
  margin: 2em 5em ;
  text-align: center ;
  font-style: italic }

div.dedication p.topic-title {
  font-weight: bold ;
  font-style: normal }

div.figure {
  margin-left: 2em ;
  margin-right: 2em }

div.footer, div.header {
  clear: both;
  font-size: smaller }

div.line-block {
  display: block ;
  margin-top: 1em ;
  margin-bottom: 1em }

div.line-block div.line-block {
  margin-top: 0 ;
  margin-bottom: 0 ;
  margin-left: 1.5em }

div.sidebar {
  margin: 0 0 0.5em 1em ;
  border: medium outset ;
  padding: 1em ;
  background-color: #ffffee ;
  width: 40% ;
  float: right ;
  clear: right }

div.sidebar p.rubric {
  font-family: sans-serif ;
  font-size: medium }

div.system-messages {
  margin: 5em }

div.system-messages h1 {
  color: red }

div.system-message {
  border: medium outset ;
  padding: 1em }

div.system-message p.system-message-title {
  color: red ;
  font-weight: bold }

div.topic {
  margin: 2em }

h1.section-subtitle, h2.section-subtitle, h3.section-subtitle,
h4.section-subtitle, h5.section-subtitle, h6.section-subtitle {
  margin-top: 0.4em }

h1.title {
  text-align: center }

h2.subtitle {
  text-align: center }

hr.docutils {
  width: 75% }

img.align-left, .figure.align-left, object.align-left, table.align-left {
  clear: left ;
  float: left ;
  margin-right: 1em }

img.align-right, .figure.align-right, object.align-right, table.align-right {
  clear: right ;
  float: right ;
  margin-left: 1em }

img.align-center, .figure.align-center, object.align-center {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

table.align-center {
  margin-left: auto;
  margin-right: auto;
}

.align-left {
  text-align: left }

.align-center {
  clear: both ;
  text-align: center }

.align-right {
  text-align: right }

/* reset inner alignment in figures */
div.align-right {
  text-align: inherit }

/* div.align-center * { */
/*   text-align: left } */

.align-top    {
  vertical-align: top }

.align-middle {
  vertical-align: middle }

.align-bottom {
  vertical-align: bottom }

ol.simple, ul.simple {
  margin-bottom: 1em }

ol.arabic {
  list-style: decimal }

ol.loweralpha {
  list-style: lower-alpha }

ol.upperalpha {
  list-style: upper-alpha }

ol.lowerroman {
  list-style: lower-roman }

ol.upperroman {
  list-style: upper-roman }

p.attribution {
  text-align: right ;
  margin-left: 50% }

p.caption {
  font-style: italic }

p.credits {
  font-style: italic ;
  font-size: smaller }

p.label {
  white-space: nowrap }

p.rubric {
  font-weight: bold ;
  font-size: larger ;
  color: maroon ;
  text-align: center }

p.sidebar-title {
  font-family: sans-serif ;
  font-weight: bold ;
  font-size: larger }

p.sidebar-subtitle {
  font-family: sans-serif ;
  font-weight: bold }

p.topic-title {
  font-weight: bold }

pre.address {
  margin-bottom: 0 ;
  margin-top: 0 ;
  font: inherit }

pre.literal-block, pre.doctest-block, pre.math, pre.code {
  margin-left: 2em ;
  margin-right: 2em }

pre.code .ln { color: grey; } /* line numbers */
pre.code, code { background-color: #eeeeee }
pre.code .comment, code .comment { color: #5C6576 }
pre.code .keyword, code .keyword { color: #3B0D06; font-weight: bold }
pre.code .literal.string, code .literal.string { color: #0C5404 }
pre.code .name.builtin, code .name.builtin { color: #352B84 }
pre.code .deleted, code .deleted { background-color: #DEB0A1}
pre.code .inserted, code .inserted { background-color: #A3D289}

span.classifier {
  font-family: sans-serif ;
  font-style: oblique }

span.classifier-delimiter {
  font-family: sans-serif ;
  font-weight: bold }

span.interpreted {
  font-family: sans-serif }

span.option {
  white-space: nowrap }

span.pre {
  white-space: pre }

span.problematic {
  color: red }

span.section-subtitle {
  /* font-size relative to parent (h1..h6 element) */
  font-size: 80% }

table.citation {
  border-left: solid 1px gray;
  margin-left: 1px }

table.docinfo {
  margin: 2em 4em }

table.docutils {
  margin-top: 0.5em ;
  margin-bottom: 0.5em }

table.footnote {
  border-left: solid 1px black;
  margin-left: 1px }

table.docutils td, table.docutils th,
table.docinfo td, table.docinfo th {
  padding-left: 0.5em ;
  padding-right: 0.5em ;
  vertical-align: top }

table.docutils th.field-name, table.docinfo th.docinfo-name {
  font-weight: bold ;
  text-align: left ;
  white-space: nowrap ;
  padding-left: 0 }

/* "booktabs" style (no vertical lines) */
table.docutils.booktabs {
  border: 0px;
  border-top: 2px solid;
  border-bottom: 2px solid;
  border-collapse: collapse;
}
table.docutils.booktabs * {
  border: 0px;
}
table.docutils.booktabs th {
  border-bottom: thin solid;
  text-align: left;
}

h1 tt.docutils, h2 tt.docutils, h3 tt.docutils,
h4 tt.docutils, h5 tt.docutils, h6 tt.docutils {
  font-size: 100% }

ul.auto-toc {
  list-style-type: none }

</style>
</head>
<body>
<div class="document" id="aria2-the-ultra-fast-download-utility">
<h1 class="title">aria2 - The ultra fast download utility</h1>

<div class="section" id="disclaimer">
<h1>Disclaimer</h1>
<p>This program comes with no warranty.
You must use this program at your own risk.</p>
</div>
<div class="section" id="introduction">
<h1>Introduction</h1>
<p>aria2 is a utility for downloading files. The supported protocols are
HTTP(S), FTP, SFTP, BitTorrent, and Metalink. aria2 can download a
file from multiple sources/protocols and tries to utilize your maximum
download bandwidth. It supports downloading a file from
HTTP(S)/FTP/SFTP and BitTorrent at the same time, while the data
downloaded from HTTP(S)/FTP/SFTP is uploaded to the BitTorrent
swarm. Using Metalink's chunk checksums, aria2 automatically validates
chunks of data while downloading a file like BitTorrent.</p>
<p>The project page is located at <a class="reference external" href="https://aria2.github.io/">https://aria2.github.io/</a>.</p>
<p>See <a class="reference external" href="https://aria2.github.io/manual/en/html/">aria2 Online Manual</a> (<a class="reference external" href="https://aria2.github.io/manual/ru/html/">Russian translation</a>, <a class="reference external" href="https://aria2.github.io/manual/pt/html/">Portuguese
translation</a>) to learn
how to use aria2.</p>
</div>
<div class="section" id="features">
<h1>Features</h1>
<p>Here is a list of features:</p>
<ul class="simple">
<li>Command-line interface</li>
<li>Download files through HTTP(S)/FTP/SFTP/BitTorrent</li>
<li>Segmented downloading</li>
<li>Metalink version 4 (RFC 5854) support(HTTP/FTP/SFTP/BitTorrent)</li>
<li>Metalink version 3.0 support(HTTP/FTP/SFTP/BitTorrent)</li>
<li>Metalink/HTTP (RFC 6249) support</li>
<li>HTTP/1.1 implementation</li>
<li>HTTP Proxy support</li>
<li>HTTP BASIC authentication support</li>
<li>HTTP Proxy authentication support</li>
<li>Well-known environment variables for proxy: <tt class="docutils literal">http_proxy</tt>,
<tt class="docutils literal">https_proxy</tt>, <tt class="docutils literal">ftp_proxy</tt>, <tt class="docutils literal">all_proxy</tt> and <tt class="docutils literal">no_proxy</tt></li>
<li>HTTP gzip, deflate content encoding support</li>
<li>Verify peer using given trusted CA certificate in HTTPS</li>
<li>Client certificate authentication in HTTPS</li>
<li>Chunked transfer encoding support</li>
<li>Load Cookies from file using the Firefox3 format, Chromium/Google Chrome
and the Mozilla/Firefox
(1.x/2.x)/Netscape format.</li>
<li>Save Cookies in the Mozilla/Firefox (1.x/2.x)/Netscape format.</li>
<li>Custom HTTP Header support</li>
<li>Persistent Connections support</li>
<li>FTP/SFTP through HTTP Proxy</li>
<li>Download/Upload speed throttling</li>
<li>BitTorrent extensions: Fast extension, DHT, PEX, MSE/PSE,
Multi-Tracker, UDP tracker</li>
<li>BitTorrent <a class="reference external" href="http://getright.com/seedtorrent.html">WEB-Seeding</a>.
aria2 requests chunks more than piece size to reduce the request
overhead. It also supports pipelined requests with piece size.</li>
<li>BitTorrent Local Peer Discovery</li>
<li>Rename/change the directory structure of BitTorrent downloads
completely</li>
<li>JSON-RPC (over HTTP and WebSocket)/XML-RPC interface</li>
<li>Run as a daemon process</li>
<li>Selective download in multi-file torrent/Metalink</li>
<li>Chunk checksum validation in Metalink</li>
<li>Can disable segmented downloading in Metalink</li>
<li>Netrc support</li>
<li>Configuration file support</li>
<li>Download URIs found in a text file or stdin and the destination
directory and output file name can be specified optionally</li>
<li>Parameterized URI support</li>
<li>IPv6 support with Happy Eyeballs</li>
<li>Disk cache to reduce disk activity</li>
</ul>
</div>
<div class="section" id="versioning-and-release-schedule">
<h1>Versioning and release schedule</h1>
<p>We use 3 numbers for aria2 version: MAJOR.MINOR.PATCH.  We will ship
MINOR update on 15th of every month.  We may skip a release if we have
no changes since the last release.  The feature and documentation
freeze happens 10 days before the release day (5th day of the month)
for translation teams.  We will raise an issue about the upcoming
release around that day.</p>
<p>We may release PATCH releases between regular releases if we have
security issues.</p>
<p>MAJOR version will stay at 1 for the time being.</p>
</div>
<div class="section" id="how-to-get-source-code">
<h1>How to get source code</h1>
<p>We maintain the source code at Github:
<a class="reference external" href="https://github.com/aria2/aria2">https://github.com/aria2/aria2</a></p>
<p>To get the latest source code, run following command:</p>
<pre class="literal-block">
$ git clone https://github.com/aria2/aria2.git
</pre>
<p>This will create aria2 directory in your current directory and source
files are stored there.</p>
</div>
<div class="section" id="dependency">
<h1>Dependency</h1>
<table border="1" class="docutils">
<colgroup>
<col width="35%" />
<col width="65%" />
</colgroup>
<thead valign="bottom">
<tr><th class="head">features</th>
<th class="head">dependency</th>
</tr>
</thead>
<tbody valign="top">
<tr><td>HTTPS</td>
<td>OSX or GnuTLS or OpenSSL or Windows</td>
</tr>
<tr><td>SFTP</td>
<td>libssh2</td>
</tr>
<tr><td>BitTorrent</td>
<td>None. Optional: libnettle+libgmp or libgcrypt
or OpenSSL (see note)</td>
</tr>
<tr><td>Metalink</td>
<td>libxml2 or Expat.</td>
</tr>
<tr><td>Checksum</td>
<td>None. Optional: OSX or libnettle or libgcrypt
or OpenSSL or Windows (see note)</td>
</tr>
<tr><td>gzip, deflate in HTTP</td>
<td>zlib</td>
</tr>
<tr><td>Async DNS</td>
<td>C-Ares</td>
</tr>
<tr><td>Firefox3/Chromium cookie</td>
<td>libsqlite3</td>
</tr>
<tr><td>XML-RPC</td>
<td>libxml2 or Expat.</td>
</tr>
<tr><td>JSON-RPC over WebSocket</td>
<td>libnettle or libgcrypt or OpenSSL</td>
</tr>
</tbody>
</table>
<div class="admonition note">
<p class="first admonition-title">Note</p>
<p class="last">libxml2 has precedence over Expat if both libraries are installed.
If you prefer Expat, run configure with <tt class="docutils literal"><span class="pre">--without-libxml2</span></tt>.</p>
</div>
<div class="admonition note">
<p class="first admonition-title">Note</p>
<p>On Apple OSX the OS-level SSL/TLS support will be preferred. Hence
neither GnuTLS nor OpenSSL are required on that platform. If you'd
like to disable this behavior, run configure with
<tt class="docutils literal"><span class="pre">--without-appletls</span></tt>.</p>
<p>GnuTLS has precedence over OpenSSL if both libraries are installed.
If you prefer OpenSSL, run configure with <tt class="docutils literal"><span class="pre">--without-gnutls</span></tt>
<tt class="docutils literal"><span class="pre">--with-openssl</span></tt>.</p>
<p class="last">On Windows there is SSL implementation available that is based on
the native Windows SSL capabilities (Schannel) and it will be
preferred.  Hence neither GnuTLS nor OpenSSL are required on that
platform.  If you'd like to disable this behavior, run configure
with <tt class="docutils literal"><span class="pre">--without-wintls</span></tt>.</p>
</div>
<div class="admonition note">
<p class="first admonition-title">Note</p>
<p>On Apple OSX the OS-level checksum support will be preferred,
unless aria2 is configured with <tt class="docutils literal"><span class="pre">--without-appletls</span></tt>.</p>
<p>libnettle has precedence over libgcrypt if both libraries are
installed.  If you prefer libgcrypt, run configure with
<tt class="docutils literal"><span class="pre">--without-libnettle</span> <span class="pre">--with-libgcrypt</span></tt>. If OpenSSL is selected over
GnuTLS, neither libnettle nor libgcrypt will be used.</p>
<p>If none of the optional dependencies are installed, an internal
implementation that only supports md5 and sha1 will be used.</p>
<p class="last">On Windows there is SSL implementation available that is based on
the native Windows capabilities and it will be preferred, unless
aria2 is configured with <tt class="docutils literal"><span class="pre">--without-wintls</span></tt>.</p>
</div>
<p>A user can have one of the following configurations for SSL and crypto
libraries:</p>
<ul class="simple">
<li>OpenSSL</li>
<li>GnuTLS + libgcrypt</li>
<li>GnuTLS + libnettle</li>
<li>Apple TLS (OSX only)</li>
<li>Windows TLS (Windows only)</li>
</ul>
<p>You can disable BitTorrent and Metalink support by providing
<tt class="docutils literal"><span class="pre">--disable-bittorrent</span></tt> and <tt class="docutils literal"><span class="pre">--disable-metalink</span></tt> to the configure
script respectively.</p>
<p>In order to enable async DNS support, you need c-ares.</p>
<ul class="simple">
<li>c-ares: <a class="reference external" href="http://c-ares.haxx.se/">http://c-ares.haxx.se/</a></li>
</ul>
</div>
<div class="section" id="how-to-build">
<h1>How to build</h1>
<p>aria2 is primarily written in C++. Initially it was written based on
C++98/C++03 standard features. We are now migrating aria2 to C++11
standard. The current source code requires C++11 aware compiler. For
well-known compilers, such as g++ and clang, the <tt class="docutils literal"><span class="pre">-std=c++11</span></tt> or
<tt class="docutils literal"><span class="pre">-std=c++0x</span></tt> flag must be supported.</p>
<p>In order to build aria2 from the source package, you need following
development packages (package name may vary depending on the
distribution you use):</p>
<ul class="simple">
<li>libgnutls-dev    (Required for HTTPS, BitTorrent, Checksum support)</li>
<li>nettle-dev       (Required for BitTorrent, Checksum support)</li>
<li>libgmp-dev       (Required for BitTorrent)</li>
<li>libssh2-1-dev    (Required for SFTP support)</li>
<li>libc-ares-dev    (Required for async DNS support)</li>
<li>libxml2-dev      (Required for Metalink support)</li>
<li>zlib1g-dev       (Required for gzip, deflate decoding support in HTTP)</li>
<li>libsqlite3-dev   (Required for Firefox3/Chromium cookie support)</li>
<li>pkg-config       (Required to detect installed libraries)</li>
</ul>
<p>You can use libgcrypt-dev instead of nettle-dev and libgmp-dev:</p>
<ul class="simple">
<li>libgpg-error-dev (Required for BitTorrent, Checksum support)</li>
<li>libgcrypt-dev    (Required for BitTorrent, Checksum support)</li>
</ul>
<p>You can use libssl-dev instead of
libgnutls-dev, nettle-dev, libgmp-dev, libgpg-error-dev and libgcrypt-dev:</p>
<ul class="simple">
<li>libssl-dev       (Required for HTTPS, BitTorrent, Checksum support)</li>
</ul>
<p>You can use libexpat1-dev instead of libxml2-dev:</p>
<ul class="simple">
<li>libexpat1-dev    (Required for Metalink support)</li>
</ul>
<p>On Fedora you need the following packages: gcc, gcc-c++, kernel-devel,
libgcrypt-devel, libxml2-devel, openssl-devel, gettext-devel, cppunit</p>
<p>If you downloaded source code from git repository, you have to install
following packages to get autoconf macros:</p>
<ul class="simple">
<li>libxml2-dev</li>
<li>libcppunit-dev</li>
<li>autoconf</li>
<li>automake</li>
<li>autotools-dev</li>
<li>autopoint</li>
<li>libtool</li>
</ul>
<p>And run following command to generate configure script and other files
necessary to build the program:</p>
<pre class="literal-block">
$ autoreconf -i
</pre>
<p>Also you need <a class="reference external" href="http://sphinx-doc.org/">Sphinx</a> to build man page.</p>
<p>If you are building aria2 for Mac OS X, take a look at
the makerelease-osx.mk GNU Make makefile.</p>
<p>The quickest way to build aria2 is first run configure script:</p>
<pre class="literal-block">
$ ./configure
</pre>
<p>To build statically linked aria2, use <tt class="docutils literal">ARIA2_STATIC=yes</tt>
command-line option:</p>
<pre class="literal-block">
$ ./configure ARIA2_STATIC=yes
</pre>
<p>After configuration is done, run <tt class="docutils literal">make</tt> to compile the program:</p>
<pre class="literal-block">
$ make
</pre>
<p>See <a class="reference internal" href="#cross-compiling-windows-binary">Cross-compiling Windows binary</a> to create a Windows binary.
See <a class="reference internal" href="#cross-compiling-android-binary">Cross-compiling Android binary</a> to create an Android binary.</p>
<p>The configure script checks available libraries and enables as many
features as possible except for experimental features not enabled by
default.</p>
<p>Since 1.1.0, aria2 checks the certificate of HTTPS servers by default.
If you build with OpenSSL or the recent version of GnuTLS which has
<tt class="docutils literal">gnutls_certificate_set_x509_system_trust()</tt> function and the
library is properly configured to locate the system-wide CA
certificates store, aria2 will automatically load those certificates
at the startup. If it is not the case, I recommend to supply the path
to the CA bundle file. For example, in Debian the path to CA bundle
file is '/etc/ssl/certs/ca-certificates.crt' (in ca-certificates
package). This may vary depending on your distribution. You can give
it to configure script using <tt class="docutils literal"><span class="pre">--with-ca-bundle</span> option</tt>:</p>
<pre class="literal-block">
$ ./configure --with-ca-bundle='/etc/ssl/certs/ca-certificates.crt'
$ make
</pre>
<p>Without <tt class="docutils literal"><span class="pre">--with-ca-bundle</span></tt> option, you will encounter the error when
accessing HTTPS servers because the certificate cannot be verified
without CA bundle. In such case, you can specify the CA bundle file
using aria2's <tt class="docutils literal"><span class="pre">--ca-certificate</span></tt> option.  If you don't have CA bundle
file installed, then the last resort is disable the certificate
validation using <tt class="docutils literal"><span class="pre">--check-certificate=false</span></tt>.</p>
<p>Using the native OSX (AppleTLS) and/or Windows (WinTLS) implementation
will automatically use the system certificate store, so
<tt class="docutils literal"><span class="pre">--with-ca-bundle</span></tt> is not necessary and will be ignored when using
these implementations.</p>
<p>By default, the bash_completion file named <tt class="docutils literal">aria2c</tt> is installed to
the directory <tt class="docutils literal">$prefix/share/doc/aria2/bash_completion</tt>.  To change
the install directory of the file, use <tt class="docutils literal"><span class="pre">--with-bashcompletiondir</span></tt>
option.</p>
<p>After a <tt class="docutils literal">make</tt> the executable is located at <tt class="docutils literal">src/aria2c</tt>.</p>
<p>aria2 uses CppUnit for automated unit testing. To run the unit test:</p>
<pre class="literal-block">
$ make check
</pre>
</div>
<div class="section" id="cross-compiling-windows-binary">
<h1>Cross-compiling Windows binary</h1>
<p>In this section, we describe how to build a Windows binary using a
mingw-w64 (<a class="reference external" href="http://mingw-w64.org/doku.php">http://mingw-w64.org/doku.php</a>) cross-compiler on Debian
Linux. The MinGW (<a class="reference external" href="http://www.mingw.org/">http://www.mingw.org/</a>) may not be able to build
aria2.</p>
<p>The easiest way to build Windows binary is use Dockerfile.mingw.  See
Dockerfile.mingw how to build binary.  If you cannot use Dockerfile,
then continue to read following paragraphs.</p>
<p>Basically, after compiling and installing depended libraries, you can
do cross-compile just passing appropriate <tt class="docutils literal"><span class="pre">--host</span></tt> option and
specifying <tt class="docutils literal">CPPFLAGS</tt>, <tt class="docutils literal">LDFLAGS</tt> and <tt class="docutils literal">PKG_CONFIG_LIBDIR</tt>
variables to configure. For convenience and lowering our own
development cost, we provide easier way to configure the build
settings.</p>
<p><tt class="docutils literal"><span class="pre">mingw-config</span></tt> script is a configure script wrapper for mingw-w64.
We use it to create official Windows build.  This script assumes
following libraries have been built for cross-compile:</p>
<ul class="simple">
<li>c-ares</li>
<li>expat</li>
<li>sqlite3</li>
<li>zlib</li>
<li>libssh2</li>
<li>cppunit</li>
</ul>
<p>Some environment variables can be adjusted to change build settings:</p>
<dl class="docutils">
<dt><tt class="docutils literal">HOST</tt></dt>
<dd>cross-compile to build programs to run on <tt class="docutils literal">HOST</tt>. It defaults to
<tt class="docutils literal"><span class="pre">i686-w64-mingw32</span></tt>. To build 64bit binary, specify
<tt class="docutils literal"><span class="pre">x86_64-w64-mingw32</span></tt>.</dd>
<dt><tt class="docutils literal">PREFIX</tt></dt>
<dd>Prefix to the directory where dependent libraries are installed.  It
defaults to <tt class="docutils literal"><span class="pre">/usr/local/$HOST</span></tt>. <tt class="docutils literal"><span class="pre">-I$PREFIX/include</span></tt> will be
added to <tt class="docutils literal">CPPFLAGS</tt>. <tt class="docutils literal"><span class="pre">-L$PREFIX/lib</span></tt> will be added to
<tt class="docutils literal">LDFLAGS</tt>. <tt class="docutils literal">$PREFIX/lib/pkgconfig</tt> will be set to
<tt class="docutils literal">PKG_CONFIG_LIBDIR</tt>.</dd>
</dl>
<p>For example, to build 64bit binary do this:</p>
<pre class="literal-block">
$ HOST=x86_64-w64-mingw32 ./mingw-config
</pre>
<p>If you want libaria2 dll with <tt class="docutils literal"><span class="pre">--enable-libaria2</span></tt>, then don't use
<tt class="docutils literal">ARIA2_STATIC=yes</tt> and prepare the DLL version of external
libraries.</p>
</div>
<div class="section" id="cross-compiling-android-binary">
<h1>Cross-compiling Android binary</h1>
<p>In this section, we describe how to build Android binary using Android
NDK cross-compiler on Debian Linux.</p>
<p>At the time of this writing, Android NDK r21e should compile aria2
without errors.</p>
<p><tt class="docutils literal"><span class="pre">android-config</span></tt> script is a configure script wrapper for Android
build.  We use it to create official Android build.  This script
assumes the following libraries have been built for cross-compile:</p>
<ul class="simple">
<li>c-ares</li>
<li>openssl</li>
<li>expat</li>
<li>zlib</li>
<li>libssh2</li>
</ul>
<p>When building the above libraries, make sure that disable shared
library and enable only static library. We are going to link those
libraries statically.</p>
<p><tt class="docutils literal"><span class="pre">android-config</span></tt> assumes that <tt class="docutils literal">$ANDROID_HOME</tt> and <tt class="docutils literal">$NDK</tt>
environment variables are defined.</p>
<p>We currently use Android NDK r21e.  <tt class="docutils literal">$NDK</tt> should point to the
directory to Anroid NDK.  The build tools will be found under
<tt class="docutils literal"><span class="pre">$NDK/toolchains/llvm/prebuilt/linux-x86_64/bin/</span></tt>.</p>
<p>All the dependent libraries must be installed under
<tt class="docutils literal">$ANDROID_HOME/usr/local</tt>.</p>
<p>After <tt class="docutils literal"><span class="pre">android-config</span></tt>, run <tt class="docutils literal">make</tt> to compile sources.</p>
</div>
<div class="section" id="building-documentation">
<h1>Building documentation</h1>
<p><a class="reference external" href="http://sphinx-doc.org/">Sphinx</a> is used to build the
documentation. aria2 man pages will be build when you run <tt class="docutils literal">make</tt> if
they are not up-to-date.  You can also build HTML version of aria2 man
page by <tt class="docutils literal">make html</tt>. The HTML version manual is also available at
<a class="reference external" href="https://aria2.github.io/manual/en/html/">online</a> (<a class="reference external" href="https://aria2.github.io/manual/ru/html/">Russian
translation</a>, <a class="reference external" href="https://aria2.github.io/manual/pt/html/">Portuguese
translation</a>).</p>
</div>
<div class="section" id="bittorrent">
<h1>BitTorrent</h1>
<div class="section" id="about-file-names">
<h2>About file names</h2>
<p>The file name of the downloaded file is determined as follows:</p>
<dl class="docutils">
<dt>single-file mode</dt>
<dd>If &quot;name&quot; key is present in .torrent file, file name is the value
of &quot;name&quot; key. Otherwise, file name is the base name of .torrent
file appended by &quot;.file&quot;. For example, .torrent file is
&quot;test.torrent&quot;, then file name is &quot;test.torrent.file&quot;.  The
directory to store the downloaded file can be specified by -d
option.</dd>
<dt>multi-file mode</dt>
<dd>The complete directory/file structure mentioned in .torrent file
is created.  The directory to store the top directory of
downloaded files can be specified by -d option.</dd>
</dl>
<p>Before download starts, a complete directory structure is created if
needed. By default, aria2 opens at most 100 files mentioned in
.torrent file, and directly writes to and reads from these files.
The number of files to open simultaneously can be controlled by
<tt class="docutils literal"><span class="pre">--bt-max-open-files</span></tt> option.</p>
</div>
<div class="section" id="dht">
<h2>DHT</h2>
<p>aria2 supports mainline compatible DHT. By default, the routing table
for IPv4 DHT is saved to <tt class="docutils literal">$XDG_CACHE_HOME/aria2/dht.dat</tt> and the
routing table for IPv6 DHT is saved to
<tt class="docutils literal">$XDG_CACHE_HOME/aria2/dht6.dat</tt> unless files exist at
<tt class="docutils literal"><span class="pre">$HOME/.aria2/dht.dat</span></tt> or <tt class="docutils literal"><span class="pre">$HOME/.aria2/dht6.dat</span></tt>. aria2 uses same
port number to listen on for both IPv4 and IPv6 DHT.</p>
</div>
<div class="section" id="udp-tracker">
<h2>UDP tracker</h2>
<p>UDP tracker support is enabled when IPv4 DHT is enabled.  The port
number of UDP tracker is shared with DHT. Use <tt class="docutils literal"><span class="pre">--dht-listen-port</span></tt>
option to change the port number.</p>
</div>
<div class="section" id="other-things-should-be-noted">
<h2>Other things should be noted</h2>
<ul class="simple">
<li><tt class="docutils literal"><span class="pre">-o</span></tt> option is used to change the file name of .torrent file itself,
not a file name of a file in .torrent file. For this purpose, use
<tt class="docutils literal"><span class="pre">--index-out</span></tt> option instead.</li>
<li>The port numbers that aria2 uses by default are 6881-6999 for TCP
and UDP.</li>
<li>aria2 doesn't configure port-forwarding automatically. Please
configure your router or firewall manually.</li>
<li>The maximum number of peers is 55. This limit may be exceeded when
download rate is low. This download rate can be adjusted using
<tt class="docutils literal"><span class="pre">--bt-request-peer-speed-limit</span></tt> option.</li>
<li>As of release 0.10.0, aria2 stops sending request message after
selective download completes.</li>
</ul>
</div>
</div>
<div class="section" id="metalink">
<h1>Metalink</h1>
<p>The current implementation supports HTTP(S)/FTP/SFTP/BitTorrent.  The
other P2P protocols are ignored. Both Metalink4 (RFC 5854) and
Metalink version 3.0 documents are supported.</p>
<p>For checksum verification, md5, sha-1, sha-224, sha-256, sha-384 and
sha-512 are supported. If multiple hash algorithms are provided, aria2
uses stronger one. If whole file checksum verification fails, aria2
doesn't retry the download and just exits with non-zero return code.</p>
<p>The supported user preferences are version, language, location,
protocol and os.</p>
<p>If chunk checksums are provided in Metalink file, aria2 automatically
validates chunks of data during download. This behavior can be turned
off by a command-line option.</p>
<p>If signature is included in a Metalink file, aria2 saves it as a file
after the completion of the download.  The file name is download
file name + &quot;.sig&quot;. If same file already exists, the signature file is
not saved.</p>
<p>In Metalink4, multi-file torrent could appear in metalink:metaurl
element.  Since aria2 cannot download 2 same torrents at the same
time, aria2 groups files in metalink:file element which has same
BitTorrent metaurl and downloads them from a single BitTorrent swarm.
This is basically multi-file torrent download with file selection, so
the adjacent files which is not in Metalink document but shares same
piece with selected file are also created.</p>
<p>If relative URI is specified in metalink:url or metalink:metaurl
element, aria2 uses the URI of Metalink file as base URI to resolve
the relative URI. If relative URI is found in Metalink file which is
read from local disk, aria2 uses the value of <tt class="docutils literal"><span class="pre">--metalink-base-uri</span></tt>
option as base URI. If this option is not specified, the relative URI
will be ignored.</p>
</div>
<div class="section" id="metalink-http">
<h1>Metalink/HTTP</h1>
<p>The current implementation only uses rel=duplicate links only.  aria2
understands Digest header fields and check whether it matches the
digest value from other sources. If it differs, drop connection.
aria2 also uses this digest value to perform checksum verification
after download finished. aria2 recognizes geo value. To tell aria2
which location you prefer, you can use <tt class="docutils literal"><span class="pre">--metalink-location</span></tt> option.</p>
</div>
<div class="section" id="netrc">
<h1>netrc</h1>
<p>netrc support is enabled by default for HTTP(S)/FTP/SFTP.  To disable
netrc support, specify -n command-line option.  Your .netrc file
should have correct permissions(600).</p>
</div>
<div class="section" id="websocket">
<h1>WebSocket</h1>
<p>The WebSocket server embedded in aria2 implements the specification
defined in RFC 6455. The supported protocol version is 13.</p>
</div>
<div class="section" id="libaria2">
<h1>libaria2</h1>
<p>The libaria2 is a C++ library which offers aria2 functionality to the
client code. Currently, libaria2 is not built by default. To enable
libaria2, use <tt class="docutils literal"><span class="pre">--enable-libaria2</span></tt> configure option.  By default,
only the shared library is built. To build static library, use
<tt class="docutils literal"><span class="pre">--enable-static</span></tt> configure option as well. See libaria2
documentation to know how to use API.</p>
</div>
<div class="section" id="references">
<h1>References</h1>
<ul class="simple">
<li><a class="reference external" href="https://aria2.github.io/manual/en/html/">aria2 Online Manual</a></li>
<li><a class="reference external" href="https://aria2.github.io/">https://aria2.github.io/</a></li>
<li><a class="reference external" href="http://tools.ietf.org/html/rfc959">RFC 959 FILE TRANSFER PROTOCOL (FTP)</a></li>
<li><a class="reference external" href="http://tools.ietf.org/html/rfc1738">RFC 1738 Uniform Resource Locators (URL)</a></li>
<li><a class="reference external" href="http://tools.ietf.org/html/rfc2428">RFC 2428 FTP Extensions for IPv6 and NATs</a></li>
<li><a class="reference external" href="http://tools.ietf.org/html/rfc2616">RFC 2616 Hypertext Transfer Protocol -- HTTP/1.1</a></li>
<li><a class="reference external" href="http://tools.ietf.org/html/rfc3659">RFC 3659 Extensions to FTP</a></li>
<li><a class="reference external" href="http://tools.ietf.org/html/rfc3986">RFC 3986 Uniform Resource Identifier (URI): Generic Syntax</a></li>
<li><a class="reference external" href="http://tools.ietf.org/html/rfc4038">RFC 4038 Application Aspects of IPv6 Transition</a></li>
<li><a class="reference external" href="http://tools.ietf.org/html/rfc5854">RFC 5854 The Metalink Download Description Format</a></li>
<li><a class="reference external" href="http://tools.ietf.org/html/rfc6249">RFC 6249 Metalink/HTTP: Mirrors and Hashes</a></li>
<li><a class="reference external" href="http://tools.ietf.org/html/rfc6265">RFC 6265 HTTP State Management Mechanism</a></li>
<li><a class="reference external" href="http://tools.ietf.org/html/rfc6266">RFC 6266 Use of the Content-Disposition Header Field in the Hypertext Transfer Protocol (HTTP)</a></li>
<li><a class="reference external" href="http://tools.ietf.org/html/rfc6455">RFC 6455 The WebSocket Protocol</a></li>
<li><a class="reference external" href="http://tools.ietf.org/html/rfc6555">RFC 6555 Happy Eyeballs: Success with Dual-Stack Hosts</a></li>
<li><a class="reference external" href="http://www.bittorrent.org/beps/bep_0003.html">The BitTorrent Protocol Specification</a></li>
<li><a class="reference external" href="http://www.bittorrent.org/beps/bep_0005.html">BitTorrent: DHT Protocol</a></li>
<li><a class="reference external" href="http://www.bittorrent.org/beps/bep_0006.html">BitTorrent: Fast Extension</a></li>
<li><a class="reference external" href="http://www.bittorrent.org/beps/bep_0007.html">BitTorrent: IPv6 Tracker Extension</a></li>
<li><a class="reference external" href="http://www.bittorrent.org/beps/bep_0009.html">BitTorrent: Extension for Peers to Send Metadata Files</a></li>
<li><a class="reference external" href="http://www.bittorrent.org/beps/bep_0010.html">BitTorrent: Extension Protocol</a></li>
<li><a class="reference external" href="http://www.bittorrent.org/beps/bep_0012.html">BitTorrent: Multitracker Metadata Extension</a></li>
<li><a class="reference external" href="http://www.bittorrent.org/beps/bep_0015.html">BitTorrent: UDP Tracker Protocol for BitTorrent</a>
and <a class="reference external" href="http://www.rasterbar.com/products/libtorrent/udp_tracker_protocol.html">BitTorrent udp-tracker protocol specification</a>.</li>
<li><a class="reference external" href="http://www.bittorrent.org/beps/bep_0019.html">BitTorrent: WebSeed - HTTP/FTP Seeding (GetRight style)</a></li>
<li><a class="reference external" href="http://www.bittorrent.org/beps/bep_0027.html">BitTorrent: Private Torrents</a></li>
<li><a class="reference external" href="http://www.bittorrent.org/beps/bep_0032.html">BitTorrent: BitTorrent DHT Extensions for IPv6</a></li>
<li><a class="reference external" href="http://wiki.vuze.com/w/Message_Stream_Encryption">BitTorrent: Message Stream Encryption</a></li>
<li><a class="reference external" href="https://pdos.csail.mit.edu/~petar/papers/maymounkov-kademlia-lncs.pdf">Kademlia: A Peer-to-peer Information System Based on the  XOR Metric</a></li>
</ul>
</div>
</div>
</body>
</html>
