"""
Utility functions for the video processor application.
"""

import os
import re
import shutil
import subprocess
import tempfile
import time
import logging
from typing import Optional, List, Tuple, Union

logger = logging.getLogger("video_processor.utils")


def format_duration(seconds: Union[int, float]) -> str:
    """
    Format duration in seconds to HH:MM:SS format.

    Args:
        seconds: Duration in seconds.

    Returns:
        Formatted duration string.
    """
    minutes, seconds = divmod(int(seconds), 60)
    hours, minutes = divmod(minutes, 60)
    return f"{hours:02d}:{minutes:02d}:{seconds:02d}"


def format_size(bytes_size: Union[int, float]) -> str:
    """
    Format size in bytes to human-readable format.

    Args:
        bytes_size: Size in bytes.

    Returns:
        Formatted size string.
    """
    if bytes_size < 1024:
        return f"{bytes_size:.2f} B"
    elif bytes_size < 1024 * 1024:
        return f"{bytes_size / 1024:.2f} KB"
    elif bytes_size < 1024 * 1024 * 1024:
        return f"{bytes_size / (1024 * 1024):.2f} MB"
    else:
        return f"{bytes_size / (1024 * 1024 * 1024):.2f} GB"


def extract_video_id(url: str) -> Optional[str]:
    """
    Extract YouTube video ID from URL.

    Args:
        url: YouTube video URL.

    Returns:
        Video ID or None if not found.
    """
    patterns = [
        r'(?:v=|\/)([0-9A-Za-z_-]{11}).*',
        r'(?:embed\/|v\/|youtu.be\/)([0-9A-Za-z_-]{11})',
        r'(?:watch\?v=)([0-9A-Za-z_-]{11})'
    ]

    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)

    return None


def check_ffmpeg() -> bool:
    """
    Check if FFmpeg is installed and available.

    Returns:
        True if FFmpeg is available, False otherwise.
    """
    try:
        # Check for ffmpeg in the project's ffmpeg directory
        ffmpeg_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "ffmpeg", "ffmpeg.exe")
        if os.path.exists(ffmpeg_path):
            return True

        # Check if ffmpeg is in PATH
        result = subprocess.run(
            ["ffmpeg", "-version"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            check=False
        )
        return result.returncode == 0
    except (subprocess.SubprocessError, FileNotFoundError):
        return False


def check_aria2c() -> bool:
    """
    Check if aria2c is installed and available.

    Returns:
        True if aria2c is available, False otherwise.
    """
    try:
        # Check for aria2c in the project's aria2c directory
        aria2c_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "aria2c", "aria2c.exe")
        if os.path.exists(aria2c_path):
            return True

        # Check if aria2c is in PATH
        result = subprocess.run(
            ["aria2c", "--version"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            check=False
        )
        return result.returncode == 0
    except (subprocess.SubprocessError, FileNotFoundError):
        return False


def get_ffmpeg_path() -> str:
    """
    Get the path to the FFmpeg executable.

    Returns:
        Path to FFmpeg executable.
    """
    # Check for ffmpeg in the project's ffmpeg directory
    ffmpeg_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "ffmpeg", "ffmpeg.exe")
    if os.path.exists(ffmpeg_path):
        return ffmpeg_path

    # Assume ffmpeg is in PATH
    return "ffmpeg"


def get_aria2c_path() -> str:
    """
    Get the path to the aria2c executable.

    Returns:
        Path to aria2c executable.
    """
    # Check for aria2c in the project's aria2c directory
    aria2c_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "aria2c", "aria2c.exe")
    if os.path.exists(aria2c_path):
        return aria2c_path

    # Assume aria2c is in PATH
    return "aria2c"


def create_temp_directory() -> str:
    """
    Create a temporary directory for processing.

    Returns:
        Path to the temporary directory.
    """
    temp_dir = tempfile.mkdtemp(prefix="video_processor_")
    logger.debug(f"Created temporary directory: {temp_dir}")
    return temp_dir


def cleanup_temp_directory(temp_dir: str) -> None:
    """
    Clean up a temporary directory.

    Args:
        temp_dir: Path to the temporary directory.
    """
    if os.path.exists(temp_dir):
        try:
            shutil.rmtree(temp_dir)
            logger.debug(f"Cleaned up temporary directory: {temp_dir}")
        except (OSError, IOError) as e:
            logger.warning(f"Failed to clean up temporary directory: {e}")


def check_video_resolution(video_path: str) -> Optional[Tuple[int, int]]:
    """
    Check the resolution of a video file.

    Args:
        video_path: Path to the video file.

    Returns:
        Tuple of (width, height) or None if resolution could not be determined.
    """
    if not os.path.exists(video_path):
        logger.warning(f"Video file not found: {video_path}")
        return None

    try:
        ffprobe_path = os.path.join(os.path.dirname(get_ffmpeg_path()), "ffprobe.exe")
        if not os.path.exists(ffprobe_path):
            ffprobe_path = "ffprobe"

        cmd = [
            ffprobe_path,
            "-v", "error",
            "-select_streams", "v:0",
            "-show_entries", "stream=width,height",
            "-of", "csv=p=0",
            video_path
        ]

        result = subprocess.run(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            check=False,
            encoding='utf-8',
            errors='replace'
        )

        if result.returncode == 0 and result.stdout.strip():
            dimensions = result.stdout.strip().split(",")
            if len(dimensions) == 2:
                width, height = map(int, dimensions)
                return (width, height)
    except (subprocess.SubprocessError, ValueError) as e:
        logger.warning(f"Failed to check video resolution: {e}")

    return None


def check_cookies_freshness(cookies_file: str, max_age_days: int = 7) -> bool:
    """
    Check if cookies file is fresh (not too old).

    Args:
        cookies_file: Path to the cookies file.
        max_age_days: Maximum age in days.

    Returns:
        True if cookies are fresh, False otherwise.
    """
    if not os.path.exists(cookies_file):
        logger.warning(f"Cookies file not found: {cookies_file}")
        return False

    cookies_mtime = os.path.getmtime(cookies_file)
    current_time = time.time()
    cookies_age_days = (current_time - cookies_mtime) / (24 * 60 * 60)

    if cookies_age_days > max_age_days:
        logger.warning(f"Cookies file is {cookies_age_days:.1f} days old (max: {max_age_days} days)")
        return False

    logger.debug(f"Cookies file is {cookies_age_days:.1f} days old (fresh)")
    return True


def sanitize_filename(filename: str) -> str:
    """
    Sanitize a filename by removing invalid characters.

    Args:
        filename: The filename to sanitize.

    Returns:
        Sanitized filename.
    """
    # Kiểm tra xem có ký tự Cyrillic không
    has_cyrillic = any(0x0400 <= ord(c) <= 0x04FF for c in filename)

    if has_cyrillic:
        # Nếu có ký tự Cyrillic, chuyển đổi thành ASCII để tránh lỗi
        # Thay thế các ký tự Cyrillic bằng chữ cái Latin tương ứng
        cyrillic_map = {
            'а': 'a', 'б': 'b', 'в': 'v', 'г': 'g', 'д': 'd', 'е': 'e', 'ё': 'yo', 'ж': 'zh',
            'з': 'z', 'и': 'i', 'й': 'y', 'к': 'k', 'л': 'l', 'м': 'm', 'н': 'n', 'о': 'o',
            'п': 'p', 'р': 'r', 'с': 's', 'т': 't', 'у': 'u', 'ф': 'f', 'х': 'kh', 'ц': 'ts',
            'ч': 'ch', 'ш': 'sh', 'щ': 'sch', 'ъ': '', 'ы': 'y', 'ь': '', 'э': 'e', 'ю': 'yu',
            'я': 'ya',
            'А': 'A', 'Б': 'B', 'В': 'V', 'Г': 'G', 'Д': 'D', 'Е': 'E', 'Ё': 'Yo', 'Ж': 'Zh',
            'З': 'Z', 'И': 'I', 'Й': 'Y', 'К': 'K', 'Л': 'L', 'М': 'M', 'Н': 'N', 'О': 'O',
            'П': 'P', 'Р': 'R', 'С': 'S', 'Т': 'T', 'У': 'U', 'Ф': 'F', 'Х': 'Kh', 'Ц': 'Ts',
            'Ч': 'Ch', 'Ш': 'Sh', 'Щ': 'Sch', 'Ъ': '', 'Ы': 'Y', 'Ь': '', 'Э': 'E', 'Ю': 'Yu',
            'Я': 'Ya'
        }

        transliterated_name = ''
        for char in filename:
            if 0x0400 <= ord(char) <= 0x04FF:  # Nếu là ký tự Cyrillic
                transliterated_name += cyrillic_map.get(char, '_')
            else:
                transliterated_name += char

        filename = transliterated_name

    # Thay thế tất cả các ký tự không phải chữ cái, số, khoảng trắng, dấu gạch ngang bằng khoảng trắng
    # Điều này đảm bảo không có ký tự đặc biệt nào trong tên file
    sanitized = re.sub(r'[^a-zA-Z0-9 \-_]', '', filename)

    # Loại bỏ khoảng trắng thừa
    sanitized = re.sub(r'\s+', ' ', sanitized).strip()

    # Đảm bảo tên file không trống
    if not sanitized:
        import time
        sanitized = f"video_{int(time.time())}"
        print(f"⚠️ Tên file trống sau khi xử lý, sử dụng tên mặc định: {sanitized}")

    return sanitized


def extract_high_value_keywords(title: str, tags: List[str], max_keywords: int = 10) -> List[str]:
    """
    Extract high-value keywords from title and tags, optimized for TikTok SEO.
    Keywords are optimized to be written without spaces and avoid too long or too short words.

    This enhanced version extracts more keywords from YouTube tags and title,
    and ensures each video segment gets different hashtags to avoid detection as tool-processed.

    Args:
        title: Video title.
        tags: List of video tags.
        max_keywords: Maximum number of keywords to return (default: 10 for more variety).

    Returns:
        List of high-value keywords.
    """
    # List of low-value words to filter out
    low_value_words = {
        'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'about',
        'as', 'of', 'from', 'like', 'so', 'than', 'that', 'this', 'these', 'those', 'it', 'its',
        'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
        'will', 'would', 'shall', 'should', 'may', 'might', 'must', 'can', 'could', 'i', 'you', 'he',
        'she', 'we', 'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 'our', 'their',
        'mine', 'yours', 'hers', 'ours', 'theirs', 'what', 'who', 'when', 'where', 'why', 'how',
        'which', 'if', 'then', 'else', 'thus', 'therefore', 'however', 'although', 'though',
        'because', 'since', 'while', 'whereas', 'whether', 'whatever', 'whoever', 'whenever',
        'wherever', 'whichever', 'not', 'no', 'none', 'nothing', 'nobody', 'nowhere', 'never',
        'neither', 'nor', 'either', 'both', 'each', 'every', 'all', 'any', 'some', 'few', 'many',
        'much', 'more', 'most', 'less', 'least', 'other', 'another', 'such', 'same', 'different',
        'various', 'several', 'certain', 'various', 'etc', 'etc.', 'ie', 'i.e.', 'eg', 'e.g.',
        'vs', 'v.s.', 'via', 'fine', 'just', 'get', 'got', 'getting', 'very', 'really',
        'actually', 'basically', 'literally', 'seriously', 'honestly', 'simply', 'absolutely',
        'completely', 'totally', 'entirely', 'definitely', 'certainly', 'probably', 'possibly',
        'maybe', 'perhaps', 'supposedly', 'allegedly', 'apparently', 'obviously', 'clearly',
        'undoubtedly', 'indeed', 'surely', 'part', 'episode', 'season', 'series', 'show',
        'watch', 'listen', 'stream', 'online', 'hd', '4k', 'ultra', 'high', 'quality',
        'resolution', 'definition'
    }

    # Biến tiktok_hashtags đã không còn được sử dụng trong code hiện tại
    # vì chúng ta đã thay đổi cách tiếp cận để ưu tiên các tag từ YouTube

    # Extract words from title
    title_words = re.findall(r'\b\w+\b', title.lower())

    # Extract multi-word phrases from title (2-3 words)
    title_phrases = []
    words = title.lower().split()
    for i in range(len(words) - 1):
        # Two-word phrases
        phrase = words[i] + words[i+1]
        if 5 <= len(phrase) <= 15:  # Reasonable length for a hashtag
            title_phrases.append(phrase)

        # Three-word phrases
        if i < len(words) - 2:
            phrase = words[i] + words[i+1] + words[i+2]
            if 5 <= len(phrase) <= 15:  # Reasonable length for a hashtag
                title_phrases.append(phrase)

    # Process YouTube tags - keep original tags and also extract individual words
    processed_tags = []
    tag_words = []

    # Tạo danh sách tag YouTube đã được xử lý để ưu tiên
    youtube_tags = []

    for tag in tags:
        # Keep original tag if it's a reasonable length
        tag_lower = tag.lower()
        tag_no_spaces = re.sub(r'\s+', '', tag_lower)
        if 3 <= len(tag_no_spaces) <= 15:
            processed_tags.append(tag_no_spaces)
            # Thêm vào danh sách tag YouTube
            youtube_tags.append(tag_no_spaces)

        # Extract individual words from tag
        words = re.findall(r'\b\w+\b', tag_lower)
        tag_words.extend(words)

        # Thêm các từ trong tag vào danh sách tag YouTube
        for word in words:
            if word not in low_value_words and 3 <= len(word) <= 15:
                youtube_tags.append(word)

    # Combine all keywords
    all_keywords = title_words + title_phrases + processed_tags + tag_words

    # Filter out low-value words, too short and too long words
    filtered_keywords = [
        word for word in all_keywords
        if word not in low_value_words and 3 <= len(word) <= 15  # Not too short, not too long
    ]

    # Count occurrences to find most important keywords
    keyword_counts = {}
    for word in filtered_keywords:
        keyword_counts[word] = keyword_counts.get(word, 0) + 1

        # Bỏ tăng điểm cho TikTok-friendly hashtags
        # Chỉ tăng điểm cho tag YouTube và từ trong tiêu đề

        # Boost score for original YouTube tags - tăng điểm cao nhất cho tag YouTube
        if word in processed_tags:
            keyword_counts[word] += 15  # Tăng từ 10 lên 15

        # Boost score for any word from YouTube tags - tăng điểm cho từ trong tag YouTube
        if word in youtube_tags:
            keyword_counts[word] += 10  # Tăng từ 6 lên 10

        # Boost score for phrases from title
        if word in title_phrases:
            keyword_counts[word] += 5  # Tăng từ 3 lên 5

    # Sort by count (descending) and then by optimal length (around 5-10 chars)
    def keyword_score(item):
        word, count = item
        # Optimal length score - words between 5-10 chars get highest score
        length_score = 1.0
        if 5 <= len(word) <= 10:
            length_score = 1.5
        elif len(word) > 12:
            length_score = 0.7  # Penalize very long words
        elif len(word) < 4:
            length_score = 0.8  # Slightly penalize very short words

        return (count * length_score)

    # Sort keywords by our custom scoring function
    sorted_keywords = sorted(
        keyword_counts.items(),
        key=keyword_score,
        reverse=True
    )

    # Get unique keywords
    unique_keywords = []
    for word, _ in sorted_keywords:
        if word not in unique_keywords:
            # Remove any spaces and special characters
            cleaned_word = re.sub(r'[^a-z0-9]', '', word)
            if cleaned_word and cleaned_word not in unique_keywords:
                unique_keywords.append(cleaned_word)

    # Không thêm hashtag mặc định, chỉ sử dụng hashtag từ YouTube và tiêu đề video
    # Nếu không đủ hashtag, vẫn sử dụng những gì có được từ YouTube và tiêu đề

    # Return top keywords (up to max_keywords)
    return unique_keywords[:max_keywords]


def generate_segment_filename(original_filename: str, segment_index: int, keywords: List[str], is_vertical: bool = False) -> str:
    """
    Generate a filename for a video segment with TikTok-optimized hashtags.
    Each segment gets a different set of hashtags to avoid detection as tool-processed.
    For vertical videos, don't add "Part X" suffix.

    Args:
        original_filename: Original filename without extension.
        segment_index: Segment index (1-based).
        keywords: List of keywords to use in hashtags.
        is_vertical: Whether the video is vertical (9:16).

    Returns:
        Generated filename.
    """
    # Không giới hạn độ dài tên file
    # Windows có giới hạn đường dẫn là 260 ký tự, nhưng chúng ta không cần giới hạn tên file

    # Sử dụng danh sách keywords đã được sắp xếp theo thứ tự ưu tiên từ extract_high_value_keywords
    # Các tag YouTube đã được ưu tiên trong extract_high_value_keywords

    # Không thêm hashtag mặc định, chỉ sử dụng hashtag từ YouTube và tiêu đề video
    # Sử dụng segment_index để đảm bảo mỗi phần video có các hashtag khác nhau

    # Tạo danh sách hashtag cho phần video này
    # Sử dụng segment_index để lấy các hashtag khác nhau cho mỗi phần
    combined_keywords = []

    # Số lượng hashtag tối đa cho mỗi phần
    max_hashtags = min(7, len(keywords))

    # Tính toán vị trí bắt đầu dựa trên segment_index
    start_idx = (segment_index * 3) % max(1, len(keywords))

    # Lấy max_hashtags hashtag từ vị trí bắt đầu
    for i in range(max_hashtags):
        idx = (start_idx + i) % len(keywords)
        if keywords[idx] not in combined_keywords:
            combined_keywords.append(keywords[idx])

    # Sử dụng combined_keywords làm selected_keywords
    selected_keywords = combined_keywords

    # Create hashtag string
    hashtags = " ".join([f"#{keyword}" for keyword in selected_keywords])

    # Generate filename with format based on video type
    if is_vertical:
        # For vertical videos, don't add "Part X" suffix and don't add "_vertical"
        # Remove any existing "Part X" text that might be in the original filename
        clean_filename = original_filename.replace(" - Part 1", "").replace(" - Part 2", "").replace(" - Part 3", "")
        result = f"{clean_filename} {hashtags}"
    else:
        # For horizontal videos, use the original format with "Part X"
        result = f"{original_filename} - Part {segment_index} {hashtags}"

    # Không giới hạn độ dài tên file
    # Windows có giới hạn đường dẫn là 260 ký tự, nhưng chúng ta không cần giới hạn tên file

    return result
