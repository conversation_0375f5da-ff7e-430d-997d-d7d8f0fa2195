"""
<PERSON><PERSON><PERSON> hàm tiện ích cho ứng dụng
"""
import os
import re
import json
import shutil
import random
import logging
import subprocess
from typing import Dict, List, Optional, Tuple
from pathlib import Path

def setup_logging(log_file: str, log_level: str, log_format: str, console_level: str = "ERROR") -> None:
    """Thiết lập logging cho ứng dụng"""
    # Đ<PERSON><PERSON> bảo thư mục chứa file log tồn tại
    log_dir = os.path.dirname(log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)

    # C<PERSON>u hình ghi lại tất cả log vào file theo mức độ được cấu hình
    # Sử dụng encoding='utf-8' để hỗ trợ ký tự Unicode
    file_handler = logging.FileHandler(log_file, mode='w', encoding='utf-8')  # <PERSON><PERSON> đè lên file cũ mỗi lần chạy
    file_handler.setLevel(getattr(logging, log_level.upper()))

    # <PERSON><PERSON><PERSON> hình hiển thị trên console chỉ từ mức ERROR trở lên để giảm bớt thông báo
    # Sử dụng encoding='utf-8' cho StreamHandler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(getattr(logging, console_level.upper()))

    # Định dạng log
    formatter = logging.Formatter(log_format)
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # Thiết lập root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    root_logger.handlers = []  # Xóa các handler mặc định
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)

    # Ghi log khởi động - sử dụng ASCII thay vì Unicode để tránh lỗi
    logging.info("=== Bat dau phien lam viec moi ===")
    logging.info(f"Ghi log vao file: {log_file}")

def sanitize_filename(filename: str) -> str:
    """Xử lý tên file để hợp lệ, hỗ trợ Unicode và ký tự Cyrillic"""
    if not filename:
        return "untitled"

    # Loại bỏ ký tự không hợp lệ trong Windows nhưng giữ nguyên Unicode
    # Chỉ loại bỏ các ký tự đặc biệt không được phép trong tên file Windows
    filename = re.sub(r'[<>:"/\\|?*]', '', filename)

    # Thay thế nhiều khoảng trắng liên tiếp bằng một khoảng trắng
    filename = re.sub(r'\s+', ' ', filename)

    # Loại bỏ khoảng trắng ở đầu và cuối
    filename = filename.strip()

    # Giới hạn độ dài tên file
    if len(filename) > 255:
        name, ext = os.path.splitext(filename)
        filename = name[:255-len(ext)] + ext

    # Đảm bảo tên file không trống
    if not filename or filename.isspace():
        return "untitled"

    return filename

def get_video_info(video_path: str) -> Optional[Dict]:
    """Lấy thông tin video sử dụng ffprobe"""
    try:
        cmd = [
            "ffprobe",
            "-v", "quiet",
            "-print_format", "json",
            "-show_format",
            "-show_streams",
            video_path
        ]

        from src.utils.subprocess_wrapper import safe_run
        result = safe_run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            info = json.loads(result.stdout)
            video_stream = next(
                (s for s in info["streams"] if s["codec_type"] == "video"),
                None
            )

            if video_stream:
                return {
                    "width": int(video_stream.get("width", 0)),
                    "height": int(video_stream.get("height", 0)),
                    "duration": float(info["format"].get("duration", 0)),
                    "bitrate": int(info["format"].get("bit_rate", 0)),
                    "fps": eval(video_stream.get("r_frame_rate", "0/1")),
                    "codec": video_stream.get("codec_name", "")
                }

    except Exception as e:
        logging.error(f"Lỗi khi lấy thông tin video: {str(e)}")

    return None

def calculate_crop_size(width: int, height: int, target_size: int) -> Tuple[int, int, int, int]:
    """Tính toán kích thước và vị trí crop để tạo video vuông"""
    if width > height:
        # Video ngang
        crop_size = height
        x_offset = (width - height) // 2
        y_offset = 0
    else:
        # Video dọc
        crop_size = width
        x_offset = 0
        y_offset = (height - width) // 2

    return crop_size, x_offset, y_offset, target_size

def cleanup_temp_files(temp_dir: str) -> None:
    """Dọn dẹp các file tạm"""
    try:
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
            logging.info(f"Đã xóa thư mục tạm: {temp_dir}")
    except Exception as e:
        logging.error(f"Lỗi khi dọn dẹp file tạm: {str(e)}")

def get_gpu_info() -> Dict[str, any]:
    """Lấy thông tin GPU"""
    try:
        # Kiểm tra NVIDIA GPU
        nvidia_smi = shutil.which("nvidia-smi")
        if nvidia_smi:
            cmd = [nvidia_smi, "--query-gpu=gpu_name,memory.total,memory.free", "--format=csv,noheader"]
            from src.utils.subprocess_wrapper import safe_run
            result = safe_run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                name, total, free = result.stdout.strip().split(", ")
                return {
                    "type": "nvidia",
                    "name": name,
                    "total_memory": total,
                    "free_memory": free
                }

        # Kiểm tra Intel GPU
        if os.name == "nt":  # Windows
            cmd = ["wmic", "path", "win32_VideoController", "get", "name"]
        else:  # Linux
            cmd = ["lspci", "|", "grep", "-i", "vga"]

        from src.utils.subprocess_wrapper import safe_run
        result = safe_run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            return {
                "type": "other",
                "name": result.stdout.strip()
            }

    except Exception as e:
        logging.error(f"Lỗi khi lấy thông tin GPU: {str(e)}")

    return {
        "type": "unknown",
        "name": "Unknown GPU"
    }

def format_time(seconds: float) -> str:
    """Định dạng thời gian từ giây sang HH:MM:SS"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = int(seconds % 60)

    if hours > 0:
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    else:
        return f"{minutes:02d}:{seconds:02d}"

def format_size(size_bytes: int) -> str:
    """Định dạng kích thước file từ bytes sang đơn vị đọc được"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024:
            return f"{size_bytes:.2f} {unit}"
        size_bytes /= 1024
    return f"{size_bytes:.2f} TB"

def generate_random_intervals(duration: float, count: int) -> List[Tuple[float, float]]:
    """Tạo các khoảng thời gian ngẫu nhiên cho hiệu ứng lật ngang"""
    intervals = []
    min_gap = 8  # Khoảng cách tối thiểu giữa các đoạn (giây)
    min_length = 3  # Độ dài tối thiểu của mỗi đoạn (giây) - giảm xuống 3 giây
    max_length = 5  # Độ dài tối đa của mỗi đoạn (giây) - giảm xuống 5 giây

    # Đảm bảo count không lớn hơn mức có thể phân bố
    max_possible_count = int(duration / (min_length + min_gap))
    actual_count = min(count, max_possible_count)

    if actual_count < 1:
        # Trường hợp video quá ngắn, trả về ít nhất 1 khoảng nếu có thể
        if duration >= min_length:
            start = random.uniform(0, duration - min_length)
            end = min(start + random.uniform(min_length, max_length), duration)
            intervals.append((start, end))
        return intervals

    # Chia video thành các phần tương đối bằng nhau
    segment_size = duration / actual_count

    for i in range(actual_count):
        # Tính toán vùng có thể đặt đoạn lật cho mỗi phân đoạn
        segment_start = i * segment_size
        segment_end = (i + 1) * segment_size

        # Đảm bảo có khoảng cách với đoạn trước và sau
        if i > 0:
            segment_start = max(segment_start, intervals[-1][1] + min_gap)

        # Tính độ dài đoạn lật trong khoảng 3-5 giây
        flip_length = random.uniform(min_length, max_length)

        # Đảm bảo đoạn lật nằm trong phạm vi phân đoạn
        max_start = max(segment_start, segment_end - flip_length)

        if max_start >= segment_start:
            flip_start = random.uniform(segment_start, max_start)
            flip_end = min(flip_start + flip_length, duration)

            # Chỉ thêm đoạn nếu còn nằm trong thời lượng video
            if flip_end <= duration and flip_end - flip_start >= min_length:
                intervals.append((flip_start, flip_end))

    return sorted(intervals)

def create_fontconfig_file(fonts_dir: str) -> str:
    """
    Tạo file fontconfig để FFmpeg có thể tìm thấy font
    """
    import logging

    try:
        # Tạo nội dung file fontconfig
        fontconfig_content = f"""<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "fonts.dtd">
<fontconfig>
    <dir>{fonts_dir}</dir>
    <cachedir>{fonts_dir}/cache</cachedir>
</fontconfig>
"""
        # Tạo thư mục cache
        cache_dir = os.path.join(fonts_dir, "cache")
        os.makedirs(cache_dir, exist_ok=True)

        # Tạo file fontconfig
        fontconfig_path = os.path.join(fonts_dir, "fonts.conf")
        with open(fontconfig_path, "w") as f:
            f.write(fontconfig_content)

        print(f"✅ Đã tạo file fontconfig tại {fontconfig_path}")
        logging.info(f"✅ Đã tạo file fontconfig tại {fontconfig_path}")

        return fontconfig_path
    except Exception as e:
        print(f"⚠️ Lỗi khi tạo file fontconfig: {str(e)}")
        logging.error(f"⚠️ Lỗi khi tạo file fontconfig: {str(e)}")
        return ""

def install_font_if_needed(font_path: str) -> bool:
    """
    Đảm bảo font được cài đặt trong hệ thống để FFmpeg có thể sử dụng
    """
    import shutil
    import filecmp
    import logging

    try:
        # Tạo thư mục fonts trong thư mục hiện tại nếu chưa tồn tại
        local_fonts_dir = os.path.join(os.getcwd(), "fonts")
        os.makedirs(local_fonts_dir, exist_ok=True)

        # Copy font vào thư mục fonts
        font_name = os.path.basename(font_path)
        local_font_path = os.path.join(local_fonts_dir, font_name)

        # Chỉ copy nếu file chưa tồn tại hoặc khác nhau
        if not os.path.exists(local_font_path) or not filecmp.cmp(font_path, local_font_path, shallow=False):
            shutil.copy2(font_path, local_font_path)
            print(f"✅ Đã copy font {font_name} vào thư mục {local_fonts_dir}")
            logging.info(f"✅ Đã copy font {font_name} vào thư mục {local_fonts_dir}")

        # Tạo file fontconfig
        create_fontconfig_file(local_fonts_dir)

        # Thiết lập biến môi trường FONTCONFIG_FILE
        os.environ["FONTCONFIG_FILE"] = os.path.join(local_fonts_dir, "fonts.conf")

        return True
    except Exception as e:
        print(f"⚠️ Lỗi khi cài đặt font: {str(e)}")
        logging.error(f"⚠️ Lỗi khi cài đặt font: {str(e)}")
        return False

def find_available_font() -> Optional[str]:
    """Tìm font chữ có sẵn từ danh sách font được cấu hình"""
    from ..config.settings import FONT_PATHS
    import logging

    # In ra danh sách font đang tìm kiếm
    logging.info(f"🔤 Đang tìm kiếm font từ danh sách {len(FONT_PATHS)} font được cấu hình")
    print(f"🔤 Đang tìm kiếm font từ danh sách {len(FONT_PATHS)} font được cấu hình")

    # Kiểm tra cụ thể thư mục C:\Users\<USER>\Documents\impact
    impact_dir = "C:/Users/<USER>/Documents/impact"
    if os.path.exists(impact_dir):
        print(f"✅ Đã tìm thấy thư mục font: {impact_dir}")
        logging.info(f"✅ Đã tìm thấy thư mục font: {impact_dir}")

        # Liệt kê tất cả các file trong thư mục
        try:
            files = os.listdir(impact_dir)
            print(f"📁 Các file trong thư mục {impact_dir}:")
            logging.info(f"📁 Các file trong thư mục {impact_dir}:")

            for file in files:
                print(f"  - {file}")
                logging.info(f"  - {file}")

                # Nếu tìm thấy file impact.ttf, sử dụng ngay lập tức
                if file.lower() == "impact.ttf":
                    font_path = os.path.join(impact_dir, file)
                    print(f"🔤 Đã tìm thấy font Impact: {font_path}")
                    logging.info(f"🔤 Đã tìm thấy font Impact: {font_path}")
                    # Đảm bảo font được cài đặt trong hệ thống
                    install_font_if_needed(font_path)
                    return font_path
        except Exception as e:
            print(f"❌ Lỗi khi đọc thư mục {impact_dir}: {str(e)}")
            logging.error(f"❌ Lỗi khi đọc thư mục {impact_dir}: {str(e)}")

    # Kiểm tra từng font trong danh sách FONT_PATHS
    for font_path in FONT_PATHS:
        # Đường dẫn đã được sửa trong settings.py, không cần thay thế nữa
        if os.path.exists(font_path):
            print(f"🔤 Đã tìm thấy font: {os.path.basename(font_path)}")
            logging.info(f"🔤 Đã tìm thấy font: {os.path.basename(font_path)} tại {font_path}")
            # Đảm bảo font được cài đặt trong hệ thống
            install_font_if_needed(font_path)
            return font_path
        else:
            logging.debug(f"❌ Không tìm thấy font: {font_path}")

    # Nếu không tìm thấy font nào trong danh sách, thử tìm các font phổ biến khác có hỗ trợ Cyrillic
    fallback_fonts = [
        "C:/Windows/Fonts/impact.ttf",      # Impact - ưu tiên sử dụng Impact
        "C:/Windows/Fonts/ariblk.ttf",      # Arial Black
        "C:/Windows/Fonts/arialbd.ttf",     # Arial Bold
        "C:/Windows/Fonts/tahomabd.ttf",    # Tahoma Bold
        "C:/Windows/Fonts/arial.ttf",       # Arial
        "C:/Windows/Fonts/tahoma.ttf",      # Tahoma
        "C:/Windows/Fonts/segoeui.ttf",     # Segoe UI
        "C:/Windows/Fonts/times.ttf",       # Times New Roman
        "E:/# GET-VIDEO/fonts/impact.ttf",  # Thử tìm trong thư mục fonts của người dùng
        "E:/# GET-VIDEO/fonts/arial.ttf",
        "E:/# GET-VIDEO/fonts/tahoma.ttf",
    ]

    print(f"🔤 Tìm kiếm font dự phòng từ danh sách {len(fallback_fonts)} font phổ biến")
    logging.info(f"🔤 Tìm kiếm font dự phòng từ danh sách {len(fallback_fonts)} font phổ biến")

    for font_path in fallback_fonts:
        if os.path.exists(font_path):
            print(f"🔤 Sử dụng font dự phòng: {os.path.basename(font_path)} tại {font_path}")
            logging.info(f"🔤 Sử dụng font dự phòng: {os.path.basename(font_path)} tại {font_path}")
            # Đảm bảo font được cài đặt trong hệ thống
            install_font_if_needed(font_path)
            return font_path
        else:
            logging.debug(f"❌ Không tìm thấy font dự phòng: {font_path}")

    # Nếu không tìm thấy font nào, thử tìm bất kỳ font nào trong thư mục Fonts
    print("⚠️ Không tìm thấy font nào trong danh sách, thử tìm bất kỳ font nào trong thư mục Fonts")
    logging.warning("⚠️ Không tìm thấy font nào trong danh sách, thử tìm bất kỳ font nào trong thư mục Fonts")

    # Thử tìm trong thư mục Fonts của Windows
    windows_fonts_dir = "C:/Windows/Fonts"
    if os.path.exists(windows_fonts_dir):
        for font_file in os.listdir(windows_fonts_dir):
            if font_file.lower().endswith('.ttf'):
                font_path = os.path.join(windows_fonts_dir, font_file)
                print(f"🔤 Sử dụng font tìm thấy: {font_file}")
                logging.info(f"🔤 Sử dụng font tìm thấy: {font_file} tại {font_path}")
                # Đảm bảo font được cài đặt trong hệ thống
                install_font_if_needed(font_path)
                return font_path

    # Nếu vẫn không tìm thấy font nào, trả về None
    print("❌ Không tìm thấy font nào, tên kênh sẽ không được hiển thị")
    logging.error("❌ Không tìm thấy font nào, tên kênh sẽ không được hiển thị")
    return None

def print_compact_ffmpeg_cmd(cmd: List[str], prefix: str = "") -> None:
    """In lệnh ffmpeg theo định dạng dễ đọc"""
    cmd_str = " ".join(cmd)
    # Rút gọn các phần filter phức tạp
    cmd_str = re.sub(r'(complex_filter=.*?)"', r'complex_filter=..."', cmd_str)
    logging.debug(f"{prefix} {cmd_str}")

def sanitize_input(input_str: str) -> str:
    """
    Làm sạch đầu vào để tránh lỗi bảo mật như command injection

    Args:
        input_str: Chuỗi đầu vào cần làm sạch

    Returns:
        Chuỗi đã được làm sạch
    """
    if not input_str:
        return ""

    try:
        # Loại bỏ các ký tự đặc biệt có thể gây lỗi command injection
        sanitized = re.sub(r'[;&|`$<>]', '', input_str)

        # Loại bỏ các chuỗi nguy hiểm
        dangerous_patterns = [
            r'rm\s+-rf', r'format\s+[a-zA-Z]:', r'del\s+/[a-zA-Z]', r'wget\s+', r'curl\s+',
            r'powershell', r'cmd\.exe', r'bash\s+-c', r'sudo\s+', r'chmod\s+', r'chown\s+'
        ]

        for pattern in dangerous_patterns:
            sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE)

        return sanitized
    except Exception as e:
        logging.error(f"Lỗi khi làm sạch đầu vào: {str(e)}")
        # Trả về chuỗi rỗng nếu có lỗi
        return ""

def validate_url(url: str) -> bool:
    """
    Kiểm tra URL có hợp lệ không

    Args:
        url: URL cần kiểm tra

    Returns:
        True nếu URL hợp lệ, False nếu không
    """
    if not url:
        return False

    try:
        # Làm sạch URL
        url = sanitize_input(url)

        # Kiểm tra URL có đúng định dạng không
        url_pattern = re.compile(
            r'^(https?://)?(www\.)?(youtube\.com/watch\?v=|youtu\.be/|youtube\.com/shorts/)[a-zA-Z0-9_-]{11}.*$'
        )

        return bool(url_pattern.match(url))
    except Exception as e:
        logging.error(f"Lỗi khi kiểm tra URL: {str(e)}")
        return False

def validate_file_path(file_path: str) -> bool:
    """
    Kiểm tra đường dẫn file có hợp lệ không

    Args:
        file_path: Đường dẫn file cần kiểm tra

    Returns:
        True nếu đường dẫn hợp lệ, False nếu không
    """
    if not file_path:
        return False

    try:
        # Làm sạch đường dẫn
        file_path = sanitize_input(file_path)

        # Kiểm tra đường dẫn có chứa ký tự không hợp lệ không
        invalid_chars = set('<>:"|?*')
        if any(c in invalid_chars for c in file_path):
            return False

        # Kiểm tra đường dẫn có quá dài không
        if len(file_path) > 260:  # Giới hạn độ dài đường dẫn trên Windows
            return False

        # Kiểm tra đường dẫn có phải là đường dẫn tuyệt đối không
        if not os.path.isabs(file_path):
            return False

        # Kiểm tra thư mục cha có tồn tại không
        parent_dir = os.path.dirname(file_path)
        if not os.path.exists(parent_dir):
            return False

        return True
    except Exception as e:
        logging.error(f"Lỗi khi kiểm tra đường dẫn file: {str(e)}")
        return False