import subprocess
import sys
import os
import time

# Chạy script run.py và ghi output vào file
try:
    # <PERSON><PERSON><PERSON> thư mục logs nếu chưa tồn tại
    if not os.path.exists('logs'):
        os.makedirs('logs')
        
    # Tạo tên file log với timestamp
    log_file = f"logs/run_error_{int(time.time())}.txt"
    
    # Chạy script với stderr được chuyển hướng vào file
    with open(log_file, 'w', encoding='utf-8') as f:
        result = subprocess.run(
            ["python", "run.py"],
            stdout=f,
            stderr=f,
            text=True,
            check=False
        )
        
    # Hiển thị kết quả
    print(f"Script đã chạy với mã trạng thái: {result.returncode}")
    print(f"Output và lỗi đã được ghi vào file: {log_file}")
    
    # Đ<PERSON><PERSON> và hiển thị nội dung file log
    with open(log_file, 'r', encoding='utf-8') as f:
        content = f.read()
        print("\n=== Nội dung file log ===")
        print(content)
        
except Exception as e:
    print(f"Lỗi khi chạy script: {str(e)}")
