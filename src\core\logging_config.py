"""
Logging configuration for the video processor application.
"""

import os
import logging
from logging.handlers import RotatingFileHandler
import sys
import locale
from typing import Optional

# Đặt locale mặc định
try:
    locale.setlocale(locale.LC_ALL, '')
except Exception:
    pass  # Bỏ qua lỗi nếu không đặt được locale

# Đảm bảo stdout/stderr sử dụng UTF-8
if hasattr(sys.stdout, 'reconfigure') and sys.stdout.encoding != 'utf-8':
    try:
        sys.stdout.reconfigure(encoding='utf-8')
    except Exception:
        pass

if hasattr(sys.stderr, 'reconfigure') and sys.stderr.encoding != 'utf-8':
    try:
        sys.stderr.reconfigure(encoding='utf-8')
    except Exception:
        pass


def setup_logging(log_file: Optional[str] = None, verbose: bool = False) -> logging.Logger:
    """
    Configure logging for the application.

    Args:
        log_file: Path to the log file. If None, logs will only be sent to console.
        verbose: Whether to enable verbose logging (DEBUG level).

    Returns:
        The configured logger instance.
    """
    # Create logger
    logger = logging.getLogger("video_processor")
    logger.setLevel(logging.DEBUG if verbose else logging.INFO)

    # Create formatter
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )

    # Create console handler with simplified output and proper Unicode handling
    class UnicodeStreamHandler(logging.StreamHandler):
        def emit(self, record):
            try:
                msg = self.format(record)
                stream = self.stream
                # Ensure proper encoding for Windows console
                try:
                    stream.write(msg + self.terminator)
                except UnicodeEncodeError:
                    # If Unicode error occurs, try to encode with 'replace' option
                    stream.write(msg.encode(stream.encoding, 'replace').decode(stream.encoding) + self.terminator)
                self.flush()
            except Exception:
                self.handleError(record)

    console_handler = UnicodeStreamHandler(sys.stdout)

    # Use a simpler formatter for console output
    console_formatter = logging.Formatter(
        "%(levelname)s - %(message)s"
    )
    console_handler.setFormatter(console_formatter)

    # Set higher level for console to reduce verbosity - always use WARNING level for console
    console_handler.setLevel(logging.ERROR)  # Chỉ hiển thị lỗi nghiêm trọng
    logger.addHandler(console_handler)

    # Create file handler if log_file is provided
    if log_file:
        os.makedirs(os.path.dirname(log_file), exist_ok=True)

        # Custom RotatingFileHandler with Unicode support
        class UnicodeRotatingFileHandler(RotatingFileHandler):
            def emit(self, record):
                try:
                    msg = self.format(record)
                    try:
                        self.stream.write(msg + self.terminator)
                    except UnicodeEncodeError:
                        # If Unicode error occurs, try to encode with 'replace' option
                        self.stream.write(msg.encode('utf-8', 'replace').decode('utf-8') + self.terminator)
                    self.flush()
                except Exception:
                    self.handleError(record)

        file_handler = UnicodeRotatingFileHandler(
            log_file,
            maxBytes=10 * 1024 * 1024,  # 10 MB
            backupCount=5,
            encoding='utf-8'  # Explicitly set UTF-8 encoding
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)
        logger.addHandler(file_handler)

    return logger
