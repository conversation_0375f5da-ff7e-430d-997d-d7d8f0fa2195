"""
Advanced resource management for optimal performance.
"""

import os
import gc
import psutil
import threading
import time
from typing import Dict, Any, Optional
from pathlib import Path
import logging

logger = logging.getLogger("video_processor.resource_manager")


class ResourceManager:
    """Advanced resource manager for memory, disk, and GPU optimization."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.temp_dirs = set()
        self.temp_files = set()
        self.memory_threshold = config.get("memory_threshold", 85)  # 85% memory usage
        self.disk_threshold = config.get("disk_threshold", 90)     # 90% disk usage
        self.cleanup_interval = config.get("cleanup_interval", 30) # 30 seconds
        self.monitoring = False
        self.monitor_thread = None
        
    def __enter__(self):
        """Enter context manager."""
        self.start_monitoring()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit context manager."""
        self.stop_monitoring()
        self.cleanup_all()
    
    def start_monitoring(self):
        """Start resource monitoring."""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_resources, daemon=True)
        self.monitor_thread.start()
        logger.info("Resource monitoring started")
    
    def stop_monitoring(self):
        """Stop resource monitoring."""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("Resource monitoring stopped")
    
    def _monitor_resources(self):
        """Monitor system resources and trigger cleanup when needed."""
        while self.monitoring:
            try:
                # Check memory usage
                memory = psutil.virtual_memory()
                if memory.percent > self.memory_threshold:
                    logger.warning(f"High memory usage: {memory.percent:.1f}%")
                    self.emergency_memory_cleanup()
                
                # Check disk usage
                disk = psutil.disk_usage('/')
                if disk.percent > self.disk_threshold:
                    logger.warning(f"High disk usage: {disk.percent:.1f}%")
                    self.cleanup_temp_files()
                
                time.sleep(self.cleanup_interval)
                
            except Exception as e:
                logger.error(f"Error in resource monitoring: {e}")
                time.sleep(self.cleanup_interval)
    
    def register_temp_file(self, file_path: str):
        """Register a temporary file for cleanup."""
        self.temp_files.add(file_path)
        logger.debug(f"Registered temp file: {file_path}")
    
    def register_temp_dir(self, dir_path: str):
        """Register a temporary directory for cleanup."""
        self.temp_dirs.add(dir_path)
        logger.debug(f"Registered temp dir: {dir_path}")
    
    def cleanup_temp_files(self, force: bool = False):
        """Clean up temporary files."""
        cleaned_files = 0
        cleaned_size = 0
        
        # Clean up registered temp files
        for file_path in list(self.temp_files):
            try:
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    os.remove(file_path)
                    cleaned_files += 1
                    cleaned_size += file_size
                    logger.debug(f"Cleaned temp file: {file_path}")
                self.temp_files.discard(file_path)
            except Exception as e:
                logger.warning(f"Failed to clean temp file {file_path}: {e}")
        
        # Clean up registered temp directories
        for dir_path in list(self.temp_dirs):
            try:
                if os.path.exists(dir_path):
                    dir_size = self._get_dir_size(dir_path)
                    self._remove_dir_contents(dir_path)
                    cleaned_size += dir_size
                    logger.debug(f"Cleaned temp dir: {dir_path}")
                self.temp_dirs.discard(dir_path)
            except Exception as e:
                logger.warning(f"Failed to clean temp dir {dir_path}: {e}")
        
        if cleaned_files > 0 or cleaned_size > 0:
            size_mb = cleaned_size / (1024 * 1024)
            logger.info(f"Cleaned {cleaned_files} temp files, freed {size_mb:.1f}MB")
    
    def emergency_memory_cleanup(self):
        """Emergency memory cleanup when usage is high."""
        logger.info("Performing emergency memory cleanup")
        
        # Force garbage collection
        gc.collect()
        
        # Clean up temp files
        self.cleanup_temp_files(force=True)
        
        # Additional cleanup strategies
        try:
            # Clear any cached data
            import sys
            if hasattr(sys, '_clear_type_cache'):
                sys._clear_type_cache()
        except Exception as e:
            logger.debug(f"Failed to clear type cache: {e}")
    
    def optimize_for_gpu_processing(self) -> Dict[str, Any]:
        """Optimize system resources for GPU processing."""
        optimizations = {}
        
        try:
            # Check available GPU memory
            import pynvml
            pynvml.nvmlInit()
            handle = pynvml.nvmlDeviceGetHandleByIndex(0)
            gpu_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
            
            gpu_usage_percent = (gpu_info.used / gpu_info.total) * 100
            
            if gpu_usage_percent > 80:
                optimizations["reduce_gpu_load"] = True
                optimizations["use_smaller_buffers"] = True
                logger.warning(f"High GPU memory usage: {gpu_usage_percent:.1f}%")
            
            # Optimize based on available GPU memory
            available_gb = gpu_info.free / (1024**3)
            if available_gb < 2:
                optimizations["reduce_parallel_segments"] = True
                optimizations["use_cpu_fallback"] = True
            
        except Exception as e:
            logger.debug(f"Could not check GPU memory: {e}")
        
        # Check system RAM
        memory = psutil.virtual_memory()
        if memory.percent > 75:
            optimizations["reduce_buffer_size"] = True
            optimizations["limit_concurrent_operations"] = True
        
        return optimizations
    
    def get_optimal_parallel_count(self) -> int:
        """Get optimal number of parallel processes based on system resources."""
        # Base on CPU cores
        cpu_cores = psutil.cpu_count(logical=False)
        logical_cores = psutil.cpu_count(logical=True)
        
        # Check current CPU usage
        cpu_usage = psutil.cpu_percent(interval=1)
        
        # Check memory usage
        memory = psutil.virtual_memory()
        
        # Start with conservative estimate
        optimal_count = min(cpu_cores, 4)  # Max 4 parallel processes
        
        # Adjust based on current usage
        if cpu_usage > 70:
            optimal_count = max(1, optimal_count - 1)
        
        if memory.percent > 80:
            optimal_count = max(1, optimal_count - 1)
        
        # Check if GPU is available and adjust
        try:
            import pynvml
            pynvml.nvmlInit()
            # If GPU is available, we can handle more parallel processes
            optimal_count = min(optimal_count + 1, 6)
        except:
            pass
        
        logger.info(f"Optimal parallel count: {optimal_count} (CPU: {cpu_cores} cores, {cpu_usage:.1f}% usage)")
        return optimal_count
    
    def _get_dir_size(self, dir_path: str) -> int:
        """Get total size of directory."""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(dir_path):
                for filename in filenames:
                    file_path = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(file_path)
                    except (OSError, FileNotFoundError):
                        pass
        except Exception:
            pass
        return total_size
    
    def _remove_dir_contents(self, dir_path: str):
        """Remove all contents of a directory."""
        try:
            for item in os.listdir(dir_path):
                item_path = os.path.join(dir_path, item)
                if os.path.isfile(item_path):
                    os.remove(item_path)
                elif os.path.isdir(item_path):
                    self._remove_dir_contents(item_path)
                    os.rmdir(item_path)
        except Exception as e:
            logger.warning(f"Failed to remove dir contents {dir_path}: {e}")
    
    def cleanup_all(self):
        """Clean up all registered resources."""
        logger.info("Performing final cleanup")
        self.cleanup_temp_files(force=True)
        gc.collect()
    
    def get_system_stats(self) -> Dict[str, Any]:
        """Get current system resource statistics."""
        stats = {}
        
        try:
            # CPU stats
            stats["cpu_percent"] = psutil.cpu_percent(interval=0.1)
            stats["cpu_cores"] = psutil.cpu_count(logical=False)
            stats["cpu_threads"] = psutil.cpu_count(logical=True)
            
            # Memory stats
            memory = psutil.virtual_memory()
            stats["memory_total"] = memory.total
            stats["memory_used"] = memory.used
            stats["memory_percent"] = memory.percent
            stats["memory_available"] = memory.available
            
            # Disk stats
            disk = psutil.disk_usage('/')
            stats["disk_total"] = disk.total
            stats["disk_used"] = disk.used
            stats["disk_percent"] = disk.percent
            stats["disk_free"] = disk.free
            
            # GPU stats (if available)
            try:
                import pynvml
                pynvml.nvmlInit()
                handle = pynvml.nvmlDeviceGetHandleByIndex(0)
                gpu_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
                gpu_util = pynvml.nvmlDeviceGetUtilizationRates(handle)
                
                stats["gpu_memory_total"] = gpu_info.total
                stats["gpu_memory_used"] = gpu_info.used
                stats["gpu_memory_percent"] = (gpu_info.used / gpu_info.total) * 100
                stats["gpu_utilization"] = gpu_util.gpu
                
            except Exception:
                stats["gpu_available"] = False
            
        except Exception as e:
            logger.error(f"Failed to get system stats: {e}")
        
        return stats


class SmartBufferManager:
    """Smart buffer size management based on available resources."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.base_buffer_size = self._parse_buffer_size(config.get("buffer_size", "64M"))
        self.min_buffer_size = 16 * 1024 * 1024  # 16MB
        self.max_buffer_size = 256 * 1024 * 1024  # 256MB
    
    def _parse_buffer_size(self, size_str: str) -> int:
        """Parse buffer size string to bytes."""
        if isinstance(size_str, int):
            return size_str
        
        size_str = size_str.upper()
        if size_str.endswith('K'):
            return int(size_str[:-1]) * 1024
        elif size_str.endswith('M'):
            return int(size_str[:-1]) * 1024 * 1024
        elif size_str.endswith('G'):
            return int(size_str[:-1]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
    
    def get_optimal_buffer_size(self) -> int:
        """Get optimal buffer size based on current system state."""
        try:
            memory = psutil.virtual_memory()
            
            # Adjust based on available memory
            if memory.percent > 85:
                # High memory usage - use smaller buffers
                return max(self.min_buffer_size, self.base_buffer_size // 4)
            elif memory.percent > 70:
                # Medium memory usage - use reduced buffers
                return max(self.min_buffer_size, self.base_buffer_size // 2)
            elif memory.percent < 50:
                # Low memory usage - can use larger buffers
                return min(self.max_buffer_size, self.base_buffer_size * 2)
            else:
                # Normal memory usage - use base buffer size
                return self.base_buffer_size
                
        except Exception:
            return self.base_buffer_size
    
    def format_buffer_size(self, size_bytes: int) -> str:
        """Format buffer size for FFmpeg."""
        if size_bytes >= 1024 * 1024:
            return f"{size_bytes // (1024 * 1024)}M"
        elif size_bytes >= 1024:
            return f"{size_bytes // 1024}K"
        else:
            return str(size_bytes)
