"""
Wrapper for subprocess module to handle Unicode encoding issues and provide enhanced functionality
"""
import subprocess
import sys
import os
import logging
import time
import re
import signal
import functools
import threading
from typing import Any, Dict, List, Optional, Union, Tuple, Callable

# Decorator for retrying functions
def retry(max_retries=3, backoff_factor=1.5, exceptions=(Exception,)):
    """
    Decorator for retrying a function with exponential backoff

    Args:
        max_retries: Maximum number of retries
        backoff_factor: Backoff factor for wait time between retries
        exceptions: Tuple of exceptions to catch and retry
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            retry_count = 0
            last_error = None

            while retry_count < max_retries:
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    retry_count += 1
                    last_error = e

                    if retry_count >= max_retries:
                        break

                    wait_time = backoff_factor * (2 ** (retry_count - 1))
                    logging.warning(f"Retrying {func.__name__} in {wait_time:.2f}s ({retry_count}/{max_retries}) after error: {e}")
                    time.sleep(wait_time)

            # If we've exhausted all retries, raise the last error
            logging.error(f"Failed after {max_retries} retries: {last_error}")
            raise last_error

        return wrapper
    return decorator

def decode_output(output_bytes, encodings=None):
    """
    Decode bytes with multiple fallback encodings

    Args:
        output_bytes: Bytes to decode
        encodings: List of encodings to try

    Returns:
        Decoded string
    """
    if not output_bytes:
        return ""

    # Default encodings to try
    if encodings is None:
        encodings = ['utf-8', 'cp1252', 'latin-1', 'iso-8859-1']

    # Try each encoding
    for enc in encodings:
        try:
            return output_bytes.decode(enc)
        except UnicodeDecodeError:
            continue

    # If all fail, use latin-1 with replace (never fails)
    return output_bytes.decode('latin-1', errors='replace')

# Wrapper for subprocess.Popen
def safe_popen(*args, **kwargs):
    """
    Enhanced wrapper for subprocess.Popen with better error handling

    Additional features:
    - Automatic encoding detection
    - Proper error handling
    - Timeout support
    """
    # Add encoding and errors parameters if not already present
    if 'encoding' not in kwargs:
        kwargs['encoding'] = 'utf-8'
    if 'errors' not in kwargs:
        kwargs['errors'] = 'replace'

    # If text mode is enabled, ensure universal_newlines is also set
    if kwargs.get('text', False) and 'universal_newlines' not in kwargs:
        kwargs['universal_newlines'] = True

    # Call original Popen
    try:
        return subprocess.Popen(*args, **kwargs)
    except UnicodeDecodeError as e:
        # If we get a UnicodeDecodeError, try with binary mode and manual decoding
        logging.warning(f"UnicodeDecodeError in subprocess.Popen: {e}, trying binary mode")
        kwargs.pop('encoding', None)
        kwargs.pop('errors', None)
        kwargs['text'] = False
        kwargs['universal_newlines'] = False

        process = subprocess.Popen(*args, **kwargs)

        # Monkey patch the communicate method to handle decoding
        original_communicate = process.communicate

        def communicate_wrapper(*a, **kw):
            stdout_data, stderr_data = original_communicate(*a, **kw)
            if stdout_data is not None:
                stdout_data = decode_output(stdout_data)
            if stderr_data is not None:
                stderr_data = decode_output(stderr_data)
            return stdout_data, stderr_data

        process.communicate = communicate_wrapper
        return process

# Wrapper for subprocess.run
def safe_run(*args, **kwargs):
    """
    Enhanced wrapper for subprocess.run with better error handling

    Additional features:
    - Automatic encoding detection
    - Proper error handling
    - Timeout support with graceful termination
    """
    # Add encoding and errors parameters if not already present
    if 'encoding' not in kwargs:
        kwargs['encoding'] = 'utf-8'
    if 'errors' not in kwargs:
        kwargs['errors'] = 'replace'

    # If text mode is enabled, ensure universal_newlines is also set
    if kwargs.get('text', False) and 'universal_newlines' not in kwargs:
        kwargs['universal_newlines'] = True

    # Call original run
    try:
        return subprocess.run(*args, **kwargs)
    except UnicodeDecodeError as e:
        # If we get a UnicodeDecodeError, try with binary mode and manual decoding
        logging.warning(f"UnicodeDecodeError in subprocess.run: {e}, trying binary mode")
        kwargs.pop('encoding', None)
        kwargs.pop('errors', None)
        kwargs['text'] = False
        kwargs['universal_newlines'] = False

        result = subprocess.run(*args, **kwargs)

        # Decode stdout and stderr
        if hasattr(result, 'stdout') and result.stdout is not None:
            result.stdout = decode_output(result.stdout)
        if hasattr(result, 'stderr') and result.stderr is not None:
            result.stderr = decode_output(result.stderr)

        return result

@retry(max_retries=3, backoff_factor=1.5, exceptions=(subprocess.SubprocessError,))
def run_ffmpeg_safely(cmd, timeout=None, quiet=False, **kwargs):
    """
    Run FFmpeg command with enhanced error handling and retry capability

    Args:
        cmd: Command list to run
        timeout: Timeout in seconds
        quiet: Whether to suppress FFmpeg output
        **kwargs: Additional arguments to pass to subprocess.run

    Returns:
        CompletedProcess instance
    """
    # Add loglevel if not already present and quiet is True
    if quiet and '-loglevel' not in cmd:
        cmd.extend(['-loglevel', 'error'])

    # Add default arguments
    default_kwargs = {
        'capture_output': True,
        'text': True,
        'check': False  # We'll handle errors ourselves
    }

    # Update with user kwargs
    default_kwargs.update(kwargs)

    # Run the command
    try:
        result = safe_run(cmd, timeout=timeout, **default_kwargs)

        # Check for FFmpeg-specific errors
        if result.returncode != 0:
            stderr = result.stderr or ""

            # Analyze common FFmpeg errors
            if "No such file or directory" in stderr:
                raise FileNotFoundError(f"FFmpeg error: File not found - {stderr}")
            elif "Invalid data found when processing input" in stderr:
                raise ValueError(f"FFmpeg error: Invalid input data - {stderr}")
            elif "Error while opening encoder" in stderr:
                raise RuntimeError(f"FFmpeg error: Encoder error - {stderr}")
            elif "Unknown encoder" in stderr:
                raise RuntimeError(f"FFmpeg error: Unknown encoder - {stderr}")
            elif "not found" in stderr and "encoder" in stderr:
                raise RuntimeError(f"FFmpeg error: Encoder not found - {stderr}")
            else:
                raise subprocess.SubprocessError(f"FFmpeg error (code {result.returncode}): {stderr}")

        return result

    except subprocess.TimeoutExpired as e:
        logging.error(f"FFmpeg command timed out after {timeout}s: {' '.join(cmd)}")
        raise

def apply_patch():
    """
    Apply the patch to subprocess module
    This function is called from __init__.py
    """
    logging.info("Enhanced subprocess wrappers are available")
