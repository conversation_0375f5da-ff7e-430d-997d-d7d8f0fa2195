"""
Module ch<PERSON>h để khởi chạy ứng dụng
"""
import os
import sys
import logging
import argparse
import json
import time
from datetime import datetime
import traceback
import random
from typing import List, Optional
import unicodedata
import re
from pathlib import Path
import psutil  # Thêm import psutil để hiển thị thông tin hệ thống

# Khôi phục lại import tương đối
from .config.settings import LOGGING, VIDEO_SETTINGS, GPU_SETTINGS
from .utils.helpers import setup_logging, get_gpu_info
from .processors.video_processor import VideoProcessor
from .processors.advanced_video_processor import AdvancedVideoProcessor
from .processors.youtube_downloader import YouTubeDownloader
from .utils.ui_utils import ProgressBar, DownloadStatus, ProcessingSummary, print_banner, print_section

def sanitize_path(path: str) -> str:
    """Xử lý đường dẫn để loại bỏ ký tự đặc biệt nhưng giữ nguyên Unicode và ký tự ổ đĩa"""
    try:
        # Tách phần ổ đĩa (nếu có)
        drive = ""
        if len(path) >= 2 and path[1] == ':':
            drive = path[:2]
            path = path[2:]

        # Chuẩn hóa Unicode
        path = unicodedata.normalize('NFC', path)

        # Loại bỏ ký tự không hợp lệ trong tên file Windows
        # Giữ lại các ký tự Unicode hợp lệ
        path = re.sub(r'[<>:"|?*]', '_', path)

        # Ghép lại với ổ đĩa
        return drive + path
    except Exception as e:
        logging.error(f"❌ Lỗi khi xử lý đường dẫn: {str(e)}")
        return path

def normalize_path(path: str) -> str:
    """Chuẩn hóa đường dẫn để xử lý các ký tự Unicode và đặc biệt"""
    try:
        # Loại bỏ dấu ngoặc kép từ đường dẫn
        path = path.strip('"\'')

        # Xử lý đường dẫn
        path = sanitize_path(path)

        # Chuẩn hóa dấu gạch chéo
        path = path.replace('/', '\\')

        return path
    except Exception as e:
        logging.error(f"❌ Lỗi khi chuẩn hóa đường dẫn: {str(e)}")
        return path

def count_mp4_files(directory: str) -> int:
    """Đếm số lượng file MP4 trong thư mục"""
    try:
        if not os.path.exists(directory):
            return 0

        count = 0
        for file in os.listdir(directory):
            if file.lower().endswith('.mp4'):
                count += 1
        return count
    except Exception as e:
        logging.error(f"❌ Lỗi khi đếm file MP4: {str(e)}")
        return 0

def process_single_url(url: str, output_dir: str, cookies_file: Optional[str] = None,
                       optimize_bitrate: bool = True, target_bitrate: Optional[int] = None,
                       max_retries: int = 3) -> bool:
    """Xử lý một URL YouTube với cơ chế thử lại thông minh"""
    # Cải thiện xử lý lỗi và khả năng phục hồi
    retry_count = 0
    backoff_time = 2  # Thời gian chờ ban đầu (giây)

    while retry_count <= max_retries:
        try:
            # Tạo thư mục output nếu chưa tồn tại
            os.makedirs(output_dir, exist_ok=True)

            # Tạo đối tượng YouTubeDownloader
            downloader = YouTubeDownloader(url, output_dir, cookies_file)

            # Kiểm tra nếu URL đã được xử lý thành công trước đó
            if downloader._is_completed(url):
                print(f"⏭️ Bỏ qua URL đã xử lý thành công: {url}")
                # Đảm bảo URL được xóa khỏi videos.txt nếu đã xử lý thành công
                downloader._remove_from_videos_txt(url)
                return True

            # Tải video
            downloaded_path = downloader.download()

            # Kiểm tra nếu tải thất bại
            if not downloaded_path or not os.path.exists(downloaded_path):
                raise Exception(f"Không thể tải video: {url}")

            # Tìm file phụ đề nếu có
            subtitle_path = None
            if hasattr(downloader, '_find_subtitle_file'):
                video_id = downloader._extract_video_id(url)
                if video_id:
                    subtitle_path = downloader._find_subtitle_file(video_id)
                    if subtitle_path:
                        print(f"✅ Đã tìm thấy phụ đề: {os.path.basename(subtitle_path)}")

            # Xử lý video với bộ xử lý nâng cao
            # Trích xuất tên kênh từ đường dẫn output
            channel_name = None
            try:
                # Tìm tên kênh từ đường dẫn output
                path_parts = Path(output_dir).parts
                for part in reversed(path_parts):
                    if part.lower() not in ['new folder', 'videos', 'download', 'downloads']:
                        # Loại bỏ số ở đầu nếu có
                        channel_name = re.sub(r'^\d+\s+', '', part)
                        break
            except Exception as e:
                logging.error(f"⚠️ Không thể trích xuất tên kênh: {str(e)}")

            # Sử dụng bộ xử lý video nâng cao
            processor = AdvancedVideoProcessor(
                input_path=downloaded_path,
                output_dir=output_dir,
                channel_name=channel_name,
                video_title=downloader.video_title,
                video_info=downloader.video_info,
                settings=VIDEO_SETTINGS,
                subtitle_path=subtitle_path
            )

            # Xử lý video
            process_result = processor.process()

            # Kiểm tra kết quả (process() trả về True/False)

            # Đảm bảo xóa video gốc sau khi xử lý, bất kể thành công hay thất bại
            downloader._cleanup_original_video()

            if process_result:
                # Đánh dấu URL đã hoàn thành và xóa khỏi videos.txt
                downloader._add_to_completed(url)
                print(f"✅ Đã xử lý thành công và xóa video gốc: {url}")
                return True
            else:
                logging.error(f"❌ Không thể xử lý video: {url}")
                # Không xóa URL khỏi videos.txt nếu xử lý thất bại
                if not downloader._is_completed(url):
                    # Thêm tham số remove_from_videos=False
                    downloader._add_to_failed(url, "Processing failed", remove_from_videos=False)
                return False

        except Exception as e:
            retry_count += 1
            if retry_count <= max_retries:
                # Tính toán thời gian chờ với exponential backoff
                wait_time = backoff_time * (2 ** (retry_count - 1))
                # Thêm jitter để tránh thundering herd
                import random
                wait_time *= random.uniform(0.8, 1.2)

                print(f"⚠️ Lỗi: {str(e)}. Thử lại lần {retry_count}/{max_retries} sau {wait_time:.2f}s")
                import time
                time.sleep(wait_time)
            else:
                # Đã hết số lần thử lại
                logging.error(f"❌ Lỗi khi xử lý URL {url} sau {max_retries} lần thử: {str(e)}")

                # Cố gắng xóa video gốc nếu có
                try:
                    if 'downloader' in locals() and hasattr(downloader, '_cleanup_original_video'):
                        downloader._cleanup_original_video()
                except Exception as cleanup_error:
                    logging.error(f"⚠️ Không thể xóa video gốc: {str(cleanup_error)}")

                # Đánh dấu URL lỗi nhưng không xóa khỏi videos.txt
                try:
                    if 'downloader' not in locals():
                        downloader = YouTubeDownloader(url, output_dir)
                    if not downloader._is_completed(url):
                        downloader._add_to_failed(url, f"Exception: {str(e)}", remove_from_videos=False)
                except Exception as mark_error:
                    logging.error(f"❌ Không thể đánh dấu URL lỗi: {str(mark_error)}")

                return False

    # Không bao giờ đến đây, nhưng để đảm bảo type checking
    return False

def process_videos_txt(videos_txt_path: str, cookies_file: Optional[str] = None,
                      optimize_bitrate: bool = True, target_bitrate: Optional[int] = None,
                      use_advanced_processor: bool = False) -> None:
    """Xử lý một file videos.txt"""
    # Kiểm tra sự tồn tại của file
    if not os.path.exists(videos_txt_path):
        logging.error(f"❌ Không tìm thấy file videos.txt: {videos_txt_path}")
        return

    try:
        # Đọc danh sách URL từ file
        with open(videos_txt_path, 'r', encoding='utf-8') as f:
            urls = [line.strip() for line in f if line.strip()]

        if not urls:
            print(f"⚠️ Không có URL nào trong file: {videos_txt_path}")
            return

        # Lấy thư mục output
        parent_dir = os.path.dirname(videos_txt_path)
        if parent_dir:
            output_dir = sanitize_path(os.path.dirname(parent_dir))
        else:
            # Nếu videos.txt nằm ở thư mục gốc, sử dụng thư mục "output" làm thư mục đầu ra
            output_dir = sanitize_path(os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "output"))
            # Tạo thư mục output nếu chưa tồn tại
            os.makedirs(output_dir, exist_ok=True)
        print(f"📂 Thư mục output: {output_dir}")

        # Kiểm tra số lượng video hiện có
        current_video_count = count_mp4_files(output_dir)
        print(f"📊 Số video hiện có: {current_video_count}")

        if current_video_count >= 50:
            print(f"⚠️ Đã đạt giới hạn 50 video, chuyển sang kênh tiếp theo")
            return

        # Số lượng video còn có thể tải
        remaining_slots = 50 - current_video_count
        print(f"📊 Còn có thể tải thêm: {remaining_slots} video")

        # Giới hạn số URL cần xử lý
        total = min(len(urls), remaining_slots)
        print(f"📊 Sẽ xử lý: {total}/{len(urls)} URL")

        if total == 0:
            return

        # Cải thiện quản lý bộ nhớ: Xử lý theo batch tối ưu
        batch_size = 10  # Tăng số URL xử lý trong một batch lên 10
        total_batches = (total + batch_size - 1) // batch_size  # Làm tròn lên

        # Khởi tạo thanh tiến trình
        progress_bar = ProgressBar(total, prefix='Tiến trình:', suffix='Hoàn thành', length=40)

        # Khởi tạo biến đếm
        success = 0
        processed_urls = []

        # Thời gian bắt đầu xử lý
        start_time = time.time()

        for batch_idx in range(total_batches):
            # Tính toán chỉ số bắt đầu và kết thúc của batch
            start_idx = batch_idx * batch_size
            end_idx = min(start_idx + batch_size, total)
            batch_urls = urls[start_idx:end_idx]

            print(f"\n🔄 Đang xử lý batch {batch_idx+1}/{total_batches} ({len(batch_urls)} URLs)")

            # Kiểm tra số lượng video hiện có (chỉ kiểm tra một lần trước mỗi batch)
            current_video_count = count_mp4_files(output_dir)
            if current_video_count >= 50:
                print(f"\n⚠️ Đã đạt giới hạn 50 video, chuyển sang kênh tiếp theo")
                # Dừng xử lý batch hiện tại và chuyển sang kênh tiếp theo ngay lập tức
                return

            # Xử lý từng URL trong batch
            for i, url in enumerate(batch_urls, start_idx + 1):
                try:
                    # Kiểm tra số lượng video hiện có sau mỗi URL để đảm bảo dừng ngay khi đạt giới hạn
                    current_video_count = count_mp4_files(output_dir)
                    if current_video_count >= 50:
                        print(f"\n⚠️ Đã đạt giới hạn 50 video, chuyển sang kênh tiếp theo")
                        # Dừng xử lý URL hiện tại và chuyển sang kênh tiếp theo ngay lập tức
                        return

                    # Tính thời gian đã trôi qua và thời gian còn lại ước tính
                    elapsed_time = time.time() - start_time
                    if i > start_idx + 1:  # Nếu đã xử lý ít nhất 1 URL
                        avg_time_per_url = elapsed_time / (i - start_idx)
                        remaining_urls = total - i
                        estimated_time_left = avg_time_per_url * remaining_urls
                        # Chuyển đổi sang giờ:phút:giây
                        hours, remainder = divmod(estimated_time_left, 3600)
                        minutes, seconds = divmod(remainder, 60)
                        time_left_str = f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
                    else:
                        time_left_str = "--:--:--"

                    print(f"\n🔄 Đang xử lý video {i}/{total}: {url} (Thời gian còn lại: {time_left_str})")

                    # Sử dụng bộ xử lý video nâng cao nếu được yêu cầu
                    if use_advanced_processor:
                        print(f"🎥 Sử dụng bộ xử lý video nâng cao")

                    if process_single_url(url, output_dir, cookies_file, optimize_bitrate, target_bitrate):
                        success += 1
                        processed_urls.append(url)
                        print(f"✅ URL đã xử lý thành công và được xóa khỏi videos.txt: {url}")

                    # Cập nhật thanh tiến trình với thông tin thời gian
                    progress_bar.update(i, f"{success}/{i} thành công - Còn lại: {time_left_str}")
                except Exception as e:
                    logging.error(f"❌ Lỗi xử lý URL {url}: {str(e)}")
                    # Đánh dấu URL lỗi nhưng không xóa khỏi videos.txt
                    try:
                        downloader = YouTubeDownloader(url, output_dir)
                        if not downloader._is_completed(url):
                            downloader._add_to_failed(url, f"Unexpected error: {str(e)}", remove_from_videos=False)
                    except Exception as inner_e:
                        logging.error(f"❌ Không thể đánh dấu URL lỗi: {str(inner_e)}")

                    # Cập nhật thanh tiến trình
                    progress_bar.update(i, f"{success}/{i} thành công")

            # Giải phóng bộ nhớ sau mỗi batch
            import gc
            gc.collect()

            # Dừng 1 giây để hệ thống có thể đọc/ghi file và giải phóng tài nguyên
            time.sleep(1)

        # Hoàn thành thanh tiến trình
        progress_bar.update(total, f"{success}/{total} thành công")

        # Tối ưu hóa I/O: Xử lý các URL đã hoàn thành trong một lần ghi duy nhất
        if processed_urls:
            try:
                if os.path.exists(videos_txt_path):
                    # Đọc tất cả URL trong file
                    with open(videos_txt_path, 'r', encoding='utf-8') as f:
                        all_urls = [line.strip() for line in f if line.strip()]

                    # Lọc ra các URL chưa được xử lý
                    remaining_urls = [url for url in all_urls if url not in processed_urls]

                    # Thông báo số URL đã được xóa
                    removed_count = len(all_urls) - len(remaining_urls)
                    if removed_count > 0:
                        print(f"\n✅ Đã xóa {removed_count} URL đã xử lý khỏi videos.txt")

                    # Ghi lại file chỉ với các URL chưa xử lý (một lần duy nhất)
                    with open(videos_txt_path, 'w', encoding='utf-8') as f:
                        for url in remaining_urls:
                            f.write(f"{url}\n")

                    # Thông báo số URL còn lại
                    if remaining_urls:
                        print(f"⚠️ Còn {len(remaining_urls)} URL chưa được xử lý trong videos.txt")
                        print("📋 Các URL này sẽ được giữ lại cho lần xử lý tiếp theo")
                    else:
                        print("✅ Tất cả URL trong videos.txt đã được xử lý")
            except Exception as e:
                logging.error(f"❌ Lỗi khi cập nhật videos.txt: {str(e)}")
    except Exception as e:
        logging.error(f"❌ Lỗi khi xử lý file videos.txt: {str(e)}")

def main() -> None:
    """Hàm main của ứng dụng"""

    # Hiển thị banner chuyên nghiệp
    print_banner()

    # Thiết lập logging
    setup_logging(
        LOGGING["FILE"],
        LOGGING["LEVEL"],
        LOGGING["FORMAT"],
        LOGGING.get("CONSOLE_LEVEL", "ERROR")  # Sử dụng mức ERROR cho console nếu không được cấu hình
    )

    # Hiển thị thông tin phiên bản
    print(f"🚀 Khởi động ứng dụng...")

    # Tiếp tục với mã bình thường
    parser = argparse.ArgumentParser(
        description="Tải và xử lý video từ YouTube"
    )

    parser.add_argument(
        "--channel-list", "-c",
        required=True,
        help="File chứa danh sách đường dẫn tới các file videos.txt"
    )

    parser.add_argument(
        "--cookies-file",
        help="File cookies.txt để tải video (tùy chọn)"
    )

    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Hiển thị thông tin chi tiết"
    )

    parser.add_argument(
        "--no-optimize-bitrate",
        action="store_true",
        help="Tắt tối ưu hóa bitrate (giữ bitrate cao để đảm bảo chất lượng)"
    )

    parser.add_argument(
        "--bitrate",
        type=int,
        help="Chỉ định bitrate (kb/s) cho video đầu ra (ví dụ: 4000 cho 4Mbps)"
    )

    # Thêm tùy chọn cho bộ xử lý video nâng cao
    parser.add_argument(
        "--advanced",
        action="store_true",
        help="Sử dụng bộ xử lý video nâng cao"
    )

    parser.add_argument(
        "--speed",
        type=float,
        default=1.1,
        help="Tốc độ video (mặc định: 1.1)"
    )

    parser.add_argument(
        "--no-crop",
        action="store_true",
        help="Không crop video ngang thành 1:1"
    )

    parser.add_argument(
        "--no-flip",
        action="store_true",
        help="Không lật ngang video"
    )

    parser.add_argument(
        "--no-copyright-bypass",
        action="store_true",
        help="Không áp dụng hiệu ứng lách bản quyền"
    )

    parser.add_argument(
        "--no-segment",
        action="store_true",
        help="Không cắt video thành các đoạn ngắn"
    )

    args = parser.parse_args()

    # Hiển thị thông tin cấu hình chuyên nghiệp
    print_section("CẤU HÌNH HỆ THỐNG")
    print(f"📋 Channel list: {args.channel_list}")
    print(f"🍪 Cookies file: {args.cookies_file or 'Không sử dụng'}")
    print(f"⚙️ Tối ưu bitrate: {'❌ Không' if args.no_optimize_bitrate else '✅ Có'}")
    if args.bitrate:
        print(f"📊 Bitrate: {args.bitrate}kb/s")

    # Hiển thị thông tin về bộ xử lý video nâng cao
    if args.advanced:
        print_section("CẤU HÌNH XỬ LÝ VIDEO")
        print(f"🎥 Bộ xử lý video nâng cao: ✅ Đã kích hoạt")
        print(f"⏩ Tốc độ video: {args.speed}x")
        print(f"📐 Kích thước xuất: {'9:16 (1080x1920)' if VIDEO_SETTINGS['CROP']['VERTICAL_OUTPUT'] else '1:1 (1080x1080)'}")
        print(f"📝 Hiển thị tiêu đề: {'✅ Có' if VIDEO_SETTINGS['CROP']['SHOW_TITLE'] else '❌ Không'}")
        print(f"🔄 Lật ngang video: {'❌ Không' if args.no_flip else '✅ Có'}")
        print(f"🛡️ Hiệu ứng lách bản quyền: {'❌ Không' if args.no_copyright_bypass else '✅ Có'}")
        print(f"✂️ Cắt video thành đoạn ngắn: {'❌ Không' if args.no_segment else '✅ Có'}")
        print(f"💬 Thêm phụ đề: ❌ Không (tắt để tăng tốc độ xử lý)")

        # Cập nhật cấu hình video dựa trên tham số dòng lệnh
        VIDEO_SETTINGS["VIDEO_SPEED"] = args.speed
        VIDEO_SETTINGS["CROP"]["SQUARE_FOR_LANDSCAPE"] = not args.no_crop
        VIDEO_SETTINGS["COPYRIGHT_BYPASS"]["ENABLED"] = not args.no_copyright_bypass

    # Thiết lập logging
    setup_logging(
        LOGGING["FILE"],
        LOGGING["LEVEL"],
        LOGGING["FORMAT"]
    )

    # Kiểm tra GPU và hiển thị thông tin hệ thống
    gpu_info = get_gpu_info()
    print_section("THÔNG TIN HỆ THỐNG")
    print(f"💻 CPU: {os.cpu_count()} cores")
    print(f"🎮 GPU: {gpu_info['name']}")
    print(f"🧠 RAM: {round(psutil.virtual_memory().total / (1024**3), 1)} GB")
    print(f"💾 Ổ đĩa: {round(psutil.disk_usage('/').free / (1024**3), 1)} GB còn trống")

    # Khởi tạo ProcessingSummary để theo dõi tiến trình
    summary = ProcessingSummary()

    # Đọc danh sách đường dẫn tới các file videos.txt
    if not os.path.exists(args.channel_list):
        logging.error(f"❌ Không tìm thấy file channel list: {args.channel_list}")
        return

    with open(args.channel_list, 'r', encoding='utf-8') as f:
        # Loại bỏ dấu ngoặc kép từ các đường dẫn
        videos_txt_paths = [line.strip().strip('"\'') for line in f if line.strip()]

    total_channels = len(videos_txt_paths)
    success_channels = 0

    print_section("XỬ LÝ KÊNH")
    print(f"📚 Tổng số kênh: {total_channels}")

    # Xử lý từng file videos.txt
    try:
        for i, videos_txt in enumerate(videos_txt_paths, 1):
            try:
                print(f"\n🔄 Đang xử lý kênh {i}/{total_channels}: {videos_txt}")

                # Chuẩn hóa đường dẫn
                normalized_path = normalize_path(videos_txt)

                # Kiểm tra sự tồn tại của file bằng cách sử dụng đối tượng Path
                videos_txt_path = Path(normalized_path)

                if videos_txt_path.exists():
                    print(f"✅ Đã xác minh file tồn tại: {normalized_path}")

                    # Lấy thư mục output của kênh
                    parent_dir = os.path.dirname(normalized_path)
                    if parent_dir:
                        output_dir = sanitize_path(os.path.dirname(parent_dir))
                    else:
                        # Nếu videos.txt nằm ở thư mục gốc, sử dụng thư mục "output" làm thư mục đầu ra
                        output_dir = sanitize_path(os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "output"))
                        # Tạo thư mục output nếu chưa tồn tại
                        os.makedirs(output_dir, exist_ok=True)

                    # Kiểm tra số lượng video trong thư mục
                    current_video_count = count_mp4_files(output_dir)
                    if current_video_count >= 50:
                        print(f"⚠️ Kênh đã có đủ 50 video, chuyển sang kênh tiếp theo")
                        success_channels += 1
                        continue

                    process_videos_txt(normalized_path, args.cookies_file,
                                      not args.no_optimize_bitrate, args.bitrate,
                                      args.advanced)
                    success_channels += 1
                else:
                    error_msg = f"❌ Không tìm thấy file: {normalized_path}"
                    print(error_msg)
                    logging.error(error_msg)

                    # Thêm thông tin chi tiết để debug
                    print(f"🔍 Kiểm tra chi tiết đường dẫn: {normalized_path}")
                    parent_dir = os.path.dirname(normalized_path)
                    if os.path.exists(parent_dir):
                        print(f"✅ Thư mục cha tồn tại: {parent_dir}")
                        # Liệt kê các file trong thư mục cha
                        print("📑 Các file trong thư mục cha:")
                        for file in os.listdir(parent_dir):
                            print(f"   - {file}")
                    else:
                        print(f"❌ Thư mục cha không tồn tại: {parent_dir}")
            except Exception as path_error:
                error_msg = f"❌ Lỗi khi kiểm tra đường dẫn {normalized_path}: {str(path_error)}"
                print(error_msg)
                logging.error(error_msg)
    except Exception as e:
        logging.error(f"❌ Lỗi trong quá trình xử lý: {str(e)}")
    finally:
        # In tóm tắt quá trình xử lý
        summary.print_summary()

        # Khi kết thúc chương trình, kiểm tra số URL còn lại trong videos.txt
        print_section("THỐNG KÊ URL CÒN LẠI")
        total_remaining = 0
        for videos_txt in videos_txt_paths:
            # Loại bỏ dấu ngoặc kép từ đường dẫn
            videos_txt = videos_txt.strip('"\'')
            # Chuẩn hóa đường dẫn
            normalized_path = normalize_path(videos_txt)

            if Path(normalized_path).exists():
                try:
                    # Đọc các URL còn lại
                    with open(normalized_path, 'r', encoding='utf-8') as f:
                        remaining_urls = [line.strip() for line in f if line.strip()]

                    if remaining_urls:
                        total_remaining += len(remaining_urls)
                        print(f"📌 {normalized_path}: còn {len(remaining_urls)} URL")
                except Exception as e:
                    logging.error(f"❌ Lỗi khi kiểm tra {normalized_path}: {str(e)}")

        if total_remaining > 0:
            print(f"\n⚠️ Tổng số URL chưa xử lý: {total_remaining}")
            print("📋 Các URL này sẽ được giữ lại cho lần xử lý tiếp theo")

        print(f"\n✅ Đã xử lý thành công {success_channels}/{total_channels} kênh")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ Đã hủy bởi người dùng")
        sys.exit(1)
    except Exception as e:
        logging.error(f"❌ Lỗi không mong muốn: {str(e)}")
